# 📚 API参考文档

## 📋 **概述**

本文档提供VRM 1.0捏脸换装系统的完整API参考，包括所有公开类、方法、属性和事件的详细说明。适用于开发者集成和扩展系统功能。

**API版本**: 1.0.0  
**兼容性**: VRM 1.0, Unity 2022.3+, C# 8.0+  
**命名空间**: `VRoidFaceCustomization`

---

## 🎯 **核心管理器 API**

### VRM10UnifiedManager

**继承**: `MonoBehaviour`  
**单例**: `VRM10UnifiedManager.Instance`  
**用途**: 系统核心控制器，提供统一的API接口

#### **属性 (Properties)**

```csharp
/// <summary>获取管理器单例实例</summary>
public static VRM10UnifiedManager Instance { get; }

/// <summary>系统是否已初始化</summary>
public bool IsSystemInitialized { get; }

/// <summary>当前VRM实例引用</summary>
public Vrm10Instance VrmInstance { get; set; }

/// <summary>面部控制器组件</summary>
public VRM10FaceController FaceController { get; }

/// <summary>服装绑定器组件</summary>
public VRM10ClothBinder ClothBinder { get; }

/// <summary>服装库路径</summary>
public string ClothLibraryPath { get; }

/// <summary>当前系统状态</summary>
public SystemState CurrentState { get; }
```

#### **方法 (Methods)**

##### 系统管理
```csharp
/// <summary>初始化系统</summary>
/// <param name="autoScan">是否自动扫描服装库</param>
/// <returns>初始化是否成功</returns>
public bool InitializeSystem(bool autoScan = false)

/// <summary>重置系统到初始状态</summary>
public void ResetSystem()

/// <summary>获取系统状态报告</summary>
/// <returns>详细的系统状态信息</returns>
public SystemStatusReport GetSystemStatus()

/// <summary>验证系统配置</summary>
/// <returns>验证结果和问题列表</returns>
public ValidationResult ValidateSystemConfiguration()
```

##### 面部表情控制
```csharp
/// <summary>设置单个面部表情</summary>
/// <param name="expressionName">表情名称 (如 "happy", "sad")</param>
/// <param name="value">表情强度 (0.0-1.0)</param>
/// <returns>设置是否成功</returns>
public bool SetFaceExpression(string expressionName, float value)

/// <summary>批量设置多个表情</summary>
/// <param name="expressions">表情名称和值的字典</param>
/// <returns>成功设置的表情数量</returns>
public int SetMultipleFaceExpressions(Dictionary<string, float> expressions)

/// <summary>获取当前表情值</summary>
/// <param name="expressionName">表情名称</param>
/// <returns>当前表情值，不存在则返回0</returns>
public float GetFaceExpressionValue(string expressionName)

/// <summary>重置所有面部表情</summary>
public void ResetFacialParameters()

/// <summary>随机化面部表情</summary>
/// <param name="intensity">随机化强度 (0.0-1.0)</param>
public void RandomizeFacialParameters(float intensity = 0.5f)
```

##### 动态换装
```csharp
/// <summary>根据名称穿戴服装</summary>
/// <param name="clothName">服装名称</param>
/// <returns>换装是否成功</returns>
public bool WearClothByName(string clothName)

/// <summary>根据类型和名称穿戴服装</summary>
/// <param name="clothType">服装类型</param>
/// <param name="clothName">服装名称</param>
/// <returns>换装是否成功</returns>
public bool WearClothByType(VRM10ClothType clothType, string clothName)

/// <summary>穿戴服装Prefab</summary>
/// <param name="clothPrefab">服装Prefab对象</param>
/// <returns>换装是否成功</returns>
public bool WearCloth(GameObject clothPrefab)

/// <summary>批量穿戴多个服装</summary>
/// <param name="clothPrefabs">服装Prefab数组</param>
/// <returns>成功穿戴的服装数量</returns>
public int WearMultipleClothes(GameObject[] clothPrefabs)

/// <summary>移除指定类型的服装</summary>
/// <param name="clothType">要移除的服装类型</param>
/// <returns>移除是否成功</returns>
public bool RemoveClothByType(VRM10ClothType clothType)

/// <summary>移除所有服装</summary>
public void RemoveAllClothes()

/// <summary>获取当前穿戴的服装列表</summary>
/// <returns>当前穿戴的服装信息列表</returns>
public List<ClothInfo> GetWornClothes()

/// <summary>检查是否穿戴了指定类型的服装</summary>
/// <param name="clothType">服装类型</param>
/// <returns>是否穿戴</returns>
public bool IsWearingClothType(VRM10ClothType clothType)
```

##### 套装管理
```csharp
/// <summary>穿戴指定套装</summary>
/// <param name="outfitName">套装名称</param>
/// <returns>穿戴是否成功</returns>
public bool WearOutfitByName(string outfitName)

/// <summary>保存当前穿戴为套装</summary>
/// <param name="outfitName">套装名称</param>
/// <param name="description">套装描述</param>
/// <returns>保存是否成功</returns>
public bool SaveCurrentAsOutfit(string outfitName, string description = "")

/// <summary>删除指定套装</summary>
/// <param name="outfitName">套装名称</param>
/// <returns>删除是否成功</returns>
public bool DeleteOutfit(string outfitName)

/// <summary>获取所有套装列表</summary>
/// <returns>套装名称列表</returns>
public List<string> GetAvailableOutfits()
```

##### 数据管理
```csharp
/// <summary>加载角色数据</summary>
/// <param name="characterData">角色数据</param>
/// <returns>加载是否成功</returns>
public bool LoadCharacterData(CharacterData characterData)

/// <summary>获取当前角色数据</summary>
/// <returns>当前角色的完整数据</returns>
public CharacterData GetCurrentCharacterData()

/// <summary>刷新服装库</summary>
/// <returns>刷新是否成功</returns>
public bool RefreshClothLibrary()
```

#### **事件 (Events)**

```csharp
/// <summary>系统初始化完成事件</summary>
public event System.Action OnSystemInitialized;

/// <summary>面部表情变化事件</summary>
/// <param name="expressionName">表情名称</param>
/// <param name="newValue">新的表情值</param>
public event System.Action<string, float> OnFaceExpressionChanged;

/// <summary>服装穿戴事件</summary>
/// <param name="clothInfo">服装信息</param>
public event System.Action<ClothInfo> OnClothWorn;

/// <summary>服装移除事件</summary>
/// <param name="clothType">移除的服装类型</param>
public event System.Action<VRM10ClothType> OnClothRemoved;

/// <summary>套装变更事件</summary>
/// <param name="outfitName">套装名称</param>
public event System.Action<string> OnOutfitChanged;

/// <summary>系统错误事件</summary>
/// <param name="errorMessage">错误信息</param>
public event System.Action<string> OnSystemError;
```

---

## 🎭 **面部控制 API**

### VRM10FaceController

**继承**: `MonoBehaviour`  
**用途**: VRM 1.0模型的面部表情控制

#### **属性 (Properties)**

```csharp
/// <summary>是否已初始化</summary>
public bool IsInitialized { get; }

/// <summary>VRM实例引用</summary>
public Vrm10Instance VrmInstance { get; set; }

/// <summary>面部参数数据</summary>
public VRM10FacialParameters FacialParameters { get; }

/// <summary>支持的表情列表</summary>
public List<string> SupportedExpressions { get; }

/// <summary>调试模式开关</summary>
public bool DebugMode { get; set; }
```

#### **方法 (Methods)**

##### 初始化和配置
```csharp
/// <summary>初始化面部控制器</summary>
/// <returns>初始化是否成功</returns>
public bool InitializeFaceController()

/// <summary>重新扫描可用的表情参数</summary>
public void RefreshAvailableExpressions()

/// <summary>验证表情参数配置</summary>
/// <returns>验证结果</returns>
public ValidationResult ValidateExpressionConfiguration()
```

##### 表情控制
```csharp
/// <summary>设置表情参数</summary>
/// <param name="expressionName">表情名称</param>
/// <param name="value">表情值 (0.0-1.0)</param>
/// <param name="lerp">是否使用插值过渡</param>
/// <returns>设置是否成功</returns>
public bool SetExpression(string expressionName, float value, bool lerp = false)

/// <summary>获取表情参数值</summary>
/// <param name="expressionName">表情名称</param>
/// <returns>当前表情值</returns>
public float GetExpression(string expressionName)

/// <summary>批量设置表情</summary>
/// <param name="expressions">表情字典</param>
/// <param name="lerp">是否使用插值</param>
/// <returns>成功设置的数量</returns>
public int SetMultipleExpressions(Dictionary<string, float> expressions, bool lerp = false)

/// <summary>重置所有表情参数</summary>
public void ResetFacialParameters()

/// <summary>设置BlendShape参数</summary>
/// <param name="blendShapeName">BlendShape名称</param>
/// <param name="value">参数值</param>
/// <returns>设置是否成功</returns>
public bool SetBlendShapeParameter(string blendShapeName, float value)
```

##### 数据管理
```csharp
/// <summary>获取面部参数数据</summary>
/// <returns>当前面部参数数据</returns>
public VRM10FacialParameters GetFacialParametersData()

/// <summary>应用面部参数数据</summary>
/// <param name="facialData">要应用的面部数据</param>
/// <returns>应用是否成功</returns>
public bool ApplyFacialData(VRM10FacialParameters facialData)
```

#### **事件 (Events)**

```csharp
/// <summary>参数变化事件</summary>
public event System.Action<string, float> OnParameterChanged;

/// <summary>参数重置事件</summary>
public event System.Action OnParametersReset;

/// <summary>表情随机化事件</summary>
public event System.Action OnParametersRandomized;
```

---

## 👔 **服装绑定 API**

### VRM10ClothBinder

**继承**: `MonoBehaviour`  
**用途**: VRM 1.0模型的动态换装系统

#### **属性 (Properties)**

```csharp
/// <summary>是否启用调试模式</summary>
public bool DebugMode { get; set; }

/// <summary>是否自动更新边界</summary>
public bool AutoUpdateBounds { get; set; }

/// <summary>是否保留SpringBone</summary>
public bool PreserveSpringBones { get; set; }

/// <summary>当前穿戴的服装列表</summary>
public List<WornClothInfo> WornClothes { get; }

/// <summary>最小绑定成功率</summary>
public float MinimumSuccessRate { get; set; }
```

#### **方法 (Methods)**

##### 服装穿戴
```csharp
/// <summary>穿戴服装</summary>
/// <param name="clothPrefab">服装Prefab</param>
/// <returns>绑定结果</returns>
public BindingResult WearCloth(GameObject clothPrefab)

/// <summary>批量穿戴服装</summary>
/// <param name="clothPrefabs">服装Prefab数组</param>
/// <returns>绑定结果列表</returns>
public List<BindingResult> WearMultipleClothes(GameObject[] clothPrefabs)

/// <summary>移除指定类型的服装</summary>
/// <param name="clothType">服装类型</param>
/// <returns>移除是否成功</returns>
public bool RemoveClothByType(VRM10ClothType clothType)

/// <summary>移除指定服装实例</summary>
/// <param name="clothInstance">服装实例</param>
/// <returns>移除是否成功</returns>
public bool RemoveCloth(GameObject clothInstance)

/// <summary>移除所有服装</summary>
public void RemoveAllClothes()
```

##### 状态查询
```csharp
/// <summary>获取穿戴的服装列表</summary>
/// <returns>穿戴的服装信息</returns>
public List<WornClothInfo> GetWornClothes()

/// <summary>检查是否穿戴指定类型服装</summary>
/// <param name="clothType">服装类型</param>
/// <returns>是否穿戴</returns>
public bool IsWearingClothType(VRM10ClothType clothType)

/// <summary>根据类型获取服装实例</summary>
/// <param name="clothType">服装类型</param>
/// <returns>服装实例，没有则返回null</returns>
public GameObject GetClothByType(VRM10ClothType clothType)

/// <summary>获取服装绑定信息</summary>
/// <param name="clothInstance">服装实例</param>
/// <returns>绑定信息</returns>
public ClothBindingInfo GetClothBindingInfo(GameObject clothInstance)
```

##### 高级功能
```csharp
/// <summary>设置服装可见性</summary>
/// <param name="clothInstance">服装实例</param>
/// <param name="visible">是否可见</param>
public void SetClothVisibility(GameObject clothInstance, bool visible)

/// <summary>更新所有服装的渲染边界</summary>
public void UpdateAllBounds()

/// <summary>验证所有服装绑定</summary>
/// <returns>验证结果列表</returns>
public List<ValidationResult> ValidateAllBindings()

/// <summary>重新绑定指定服装</summary>
/// <param name="clothInstance">服装实例</param>
/// <returns>重新绑定结果</returns>
public BindingResult RebindCloth(GameObject clothInstance)
```

#### **事件 (Events)**

```csharp
/// <summary>服装穿戴事件</summary>
public event System.Action<GameObject, BindingResult> OnClothWorn;

/// <summary>服装移除事件</summary>
public event System.Action<GameObject> OnClothRemoved;

/// <summary>绑定失败事件</summary>
public event System.Action<GameObject, string> OnBindingFailed;

/// <summary>边界更新事件</summary>
public event System.Action OnBoundsUpdated;
```

---

## 💾 **数据管理 API**

### CharacterDataManager

**继承**: `MonoBehaviour`  
**单例**: `CharacterDataManager.Instance`  
**用途**: 角色数据的统一管理

#### **方法 (Methods)**

##### 异步数据操作
```csharp
/// <summary>异步加载角色数据</summary>
/// <param name="slotName">存档槽名称</param>
/// <returns>角色数据，失败返回null</returns>
public async Task<CharacterData?> LoadCharacterAsync(string slotName)

/// <summary>异步保存角色数据</summary>
/// <param name="data">要保存的角色数据</param>
/// <param name="slotName">存档槽名称</param>
/// <returns>保存是否成功</returns>
public async Task<bool> SaveCharacterAsync(CharacterData data, string slotName)

/// <summary>异步删除角色数据</summary>
/// <param name="slotName">存档槽名称</param>
/// <returns>删除是否成功</returns>
public async Task<bool> DeleteCharacterAsync(string slotName)
```

##### 同步数据操作
```csharp
/// <summary>加载角色数据</summary>
/// <param name="slotName">存档槽名称</param>
/// <returns>角色数据</returns>
public CharacterData LoadCharacter(string slotName)

/// <summary>保存角色数据</summary>
/// <param name="data">角色数据</param>
/// <param name="slotName">存档槽名称</param>
/// <returns>保存是否成功</returns>
public bool SaveCharacter(CharacterData data, string slotName)

/// <summary>删除角色数据</summary>
/// <param name="slotName">存档槽名称</param>
/// <returns>删除是否成功</returns>
public bool DeleteCharacter(string slotName)
```

##### 数据管理
```csharp
/// <summary>获取所有保存的存档槽</summary>
/// <returns>存档槽名称列表</returns>
public List<string> GetSavedSlots()

/// <summary>检查指定槽是否有数据</summary>
/// <param name="slotName">存档槽名称</param>
/// <returns>是否存在数据</returns>
public bool HasSavedData(string slotName)

/// <summary>清空所有数据</summary>
public void ClearAllData()

/// <summary>获取存档元信息</summary>
/// <param name="slotName">存档槽名称</param>
/// <returns>存档元信息</returns>
public SaveMetadata GetSaveMetadata(string slotName)
```

#### **事件 (Events)**

```csharp
/// <summary>数据加载完成事件</summary>
public event System.Action<string> OnDataLoaded;

/// <summary>数据保存完成事件</summary>
public event System.Action<string> OnDataSaved;

/// <summary>数据删除完成事件</summary>
public event System.Action<string> OnDataDeleted;

/// <summary>数据操作错误事件</summary>
public event System.Action<string, string> OnDataError;
```

---

## 📊 **数据结构定义**

### 核心数据类型

#### **CharacterData**
```csharp
[System.Serializable]
public struct CharacterData
{
    public string characterName;              // 角色名称
    public string description;                // 角色描述
    public System.DateTime createdTime;       // 创建时间
    public System.DateTime lastModified;     // 最后修改时间
    public string version;                    // 数据版本
    public VRM10FacialParameters facialData; // 面部数据
    public ClothingData clothingData;         // 服装数据
    public SceneData sceneData;               // 场景数据
    public Dictionary<string, object> customData; // 自定义数据
}
```

#### **VRM10ClothType**
```csharp
public enum VRM10ClothType
{
    Top,        // 上衣
    Bottom,     // 下装
    Dress,      // 连衣裙
    Shoes,      // 鞋子
    Accessory,  // 配饰
    Hat,        // 帽子
    Gloves,     // 手套
    Socks       // 袜子
}
```

#### **BindingResult**
```csharp
[System.Serializable]
public class BindingResult
{
    public bool success;                    // 绑定是否成功
    public string errorMessage;            // 错误信息
    public float successRate;              // 绑定成功率
    public int boundBoneCount;             // 成功绑定的骨骼数
    public int totalBoneCount;             // 总骨骼数
    public List<string> failedBones;       // 失败的骨骼列表
    public TimeSpan bindingTime;           // 绑定用时
}
```

#### **SystemState**
```csharp
public enum SystemState
{
    Uninitialized,  // 未初始化
    Initializing,   // 初始化中
    Ready,          // 就绪
    Active,         // 活跃
    Error           // 错误状态
}
```

---

## 🌐 **WebGL 接口 API**

### VRM10FaceControllerWebGLBridge

**用途**: 为WebGL平台提供JavaScript调用接口

#### **JavaScript 调用方法**

```javascript
// 设置面部表情
// unityInstance: Unity WebGL实例
// gameObjectName: 包含VRM10FaceController的GameObject名称
// expressionName: 表情名称
// value: 表情值 (0.0-1.0)
unityInstance.SendMessage(gameObjectName, "SetVRMExpression", expressionName + "," + value);

// 重置面部表情
unityInstance.SendMessage(gameObjectName, "ResetVRMExpressions");

// 穿戴服装
// clothName: 服装名称
unityInstance.SendMessage(gameObjectName, "WearVRMCloth", clothName);

// 移除服装
// clothType: 服装类型
unityInstance.SendMessage(gameObjectName, "RemoveVRMCloth", clothType);
```

#### **C# WebGL接口方法**

```csharp
/// <summary>设置VRM表情 (WebGL调用)</summary>
/// <param name="expressionData">表情名称和值，格式: "expressionName,value"</param>
[System.Runtime.InteropServices.DllImport("__Internal")]
public static extern void SetVRMExpression(string expressionData);

/// <summary>重置VRM表情 (WebGL调用)</summary>
[System.Runtime.InteropServices.DllImport("__Internal")]
public static extern void ResetVRMExpressions();

/// <summary>穿戴VRM服装 (WebGL调用)</summary>
/// <param name="clothName">服装名称</param>
[System.Runtime.InteropServices.DllImport("__Internal")]
public static extern void WearVRMCloth(string clothName);

/// <summary>移除VRM服装 (WebGL调用)</summary>
/// <param name="clothType">服装类型</param>
[System.Runtime.InteropServices.DllImport("__Internal")]
public static extern void RemoveVRMCloth(string clothType);
```

---

## 🛠️ **编辑器 API**

### 编辑器工具静态方法

#### **VRM10SystemSetup**
```csharp
/// <summary>设置VRM 1.0系统</summary>
/// <param name="vrmModel">VRM模型GameObject</param>
/// <returns>设置是否成功</returns>
public static bool SetupVRM10System(GameObject vrmModel)

/// <summary>验证VRM模型</summary>
/// <param name="vrmModel">要验证的VRM模型</param>
/// <returns>验证结果</returns>
public static ValidationResult ValidateVRMModel(GameObject vrmModel)
```

#### **VRM10ClothExtractor**
```csharp
/// <summary>提取所有服装</summary>
/// <param name="vrmModel">源VRM模型</param>
/// <param name="outputPath">输出路径</param>
/// <returns>提取结果</returns>
public static ExtractionResult ExtractAllClothes(GameObject vrmModel, string outputPath)

/// <summary>提取指定服装</summary>
/// <param name="vrmModel">源VRM模型</param>
/// <param name="clothNames">要提取的服装名称列表</param>
/// <param name="outputPath">输出路径</param>
/// <returns>提取结果</returns>
public static ExtractionResult ExtractSpecificClothes(GameObject vrmModel, List<string> clothNames, string outputPath)
```

---

## 🔧 **扩展和自定义**

### 自定义组件接口

#### **IClothBindingProvider**
```csharp
public interface IClothBindingProvider
{
    /// <summary>绑定服装到目标模型</summary>
    /// <param name="clothPrefab">服装Prefab</param>
    /// <param name="targetModel">目标模型</param>
    /// <returns>绑定结果</returns>
    BindingResult BindCloth(GameObject clothPrefab, GameObject targetModel);
    
    /// <summary>解绑服装</summary>
    /// <param name="clothInstance">服装实例</param>
    /// <returns>解绑是否成功</returns>
    bool UnbindCloth(GameObject clothInstance);
}
```

#### **IExpressionProvider**
```csharp
public interface IExpressionProvider
{
    /// <summary>设置表情参数</summary>
    /// <param name="expressionName">表情名称</param>
    /// <param name="value">表情值</param>
    /// <returns>设置是否成功</returns>
    bool SetExpression(string expressionName, float value);
    
    /// <summary>获取支持的表情列表</summary>
    /// <returns>表情名称列表</returns>
    List<string> GetSupportedExpressions();
}
```

### 事件回调委托

```csharp
/// <summary>系统状态变化回调</summary>
/// <param name="oldState">旧状态</param>
/// <param name="newState">新状态</param>
public delegate void SystemStateChangedHandler(SystemState oldState, SystemState newState);

/// <summary>服装绑定回调</summary>
/// <param name="clothInfo">服装信息</param>
/// <param name="result">绑定结果</param>
public delegate void ClothBindingHandler(ClothInfo clothInfo, BindingResult result);

/// <summary>表情变化回调</summary>
/// <param name="expressionName">表情名称</param>
/// <param name="oldValue">旧值</param>
/// <param name="newValue">新值</param>
public delegate void ExpressionChangedHandler(string expressionName, float oldValue, float newValue);
```

---

## 📝 **使用示例**

### 完整的系统使用示例

```csharp
using UnityEngine;
using VRoidFaceCustomization;
using System.Collections.Generic;

public class VRMSystemExample : MonoBehaviour
{
    private VRM10UnifiedManager manager;
    
    void Start()
    {
        // 获取管理器实例
        manager = VRM10UnifiedManager.Instance;
        
        // 订阅事件
        manager.OnSystemInitialized += OnSystemReady;
        manager.OnFaceExpressionChanged += OnExpressionChanged;
        manager.OnClothWorn += OnClothChanged;
        
        // 初始化系统
        manager.InitializeSystem(autoScan: true);
    }
    
    private void OnSystemReady()
    {
        Debug.Log("✅ VRM系统初始化完成！");
        
        // 设置面部表情
        SetupFacialExpressions();
        
        // 设置服装
        SetupClothing();
    }
    
    private void SetupFacialExpressions()
    {
        // 批量设置表情
        var expressions = new Dictionary<string, float>
        {
            {"happy", 0.8f},
            {"blink", 0.3f},
            {"surprised", 0.1f}
        };
        
        manager.SetMultipleFaceExpressions(expressions);
    }
    
    private void SetupClothing()
    {
        // 动态换装
        manager.WearClothByType(VRM10ClothType.Top, "CasualShirt");
        manager.WearClothByType(VRM10ClothType.Bottom, "JeansPants");
        manager.WearClothByType(VRM10ClothType.Shoes, "Sneakers");
    }
    
    private void OnExpressionChanged(string expressionName, float value)
    {
        Debug.Log($"表情变化: {expressionName} = {value}");
    }
    
    private void OnClothChanged(ClothInfo clothInfo)
    {
        Debug.Log($"服装变化: {clothInfo.name} ({clothInfo.type})");
    }
    
    // 保存当前状态
    public async void SaveCurrentState()
    {
        var characterData = manager.GetCurrentCharacterData();
        characterData.characterName = "MyCharacter";
        
        var dataManager = CharacterDataManager.Instance;
        bool success = await dataManager.SaveCharacterAsync(characterData, "slot_01");
        
        if (success)
        {
            Debug.Log("✅ 角色数据保存成功！");
        }
        else
        {
            Debug.LogError("❌ 角色数据保存失败！");
        }
    }
    
    // 加载保存的状态
    public async void LoadSavedState()
    {
        var dataManager = CharacterDataManager.Instance;
        var characterData = await dataManager.LoadCharacterAsync("slot_01");
        
        if (characterData.HasValue)
        {
            manager.LoadCharacterData(characterData.Value);
            Debug.Log($"✅ 加载角色: {characterData.Value.characterName}");
        }
        else
        {
            Debug.LogError("❌ 未找到保存的角色数据！");
        }
    }
}
```

---

## 🚨 **错误处理**

### 常见异常类型

```csharp
/// <summary>VRM系统异常</summary>
public class VRMSystemException : System.Exception
{
    public VRMSystemException(string message) : base(message) { }
    public VRMSystemException(string message, System.Exception innerException) : base(message, innerException) { }
}

/// <summary>服装绑定异常</summary>
public class ClothBindingException : VRMSystemException
{
    public ClothBindingException(string message) : base(message) { }
}

/// <summary>数据序列化异常</summary>
public class DataSerializationException : VRMSystemException
{
    public DataSerializationException(string message) : base(message) { }
}
```

### 错误码定义

```csharp
public enum VRMErrorCode
{
    None = 0,                    // 无错误
    SystemNotInitialized = 1001, // 系统未初始化
    VRMModelNotFound = 1002,     // VRM模型未找到
    ClothBindingFailed = 2001,   // 服装绑定失败
    ExpressionNotSupported = 3001, // 表情不支持
    DataSaveError = 4001,        // 数据保存错误
    DataLoadError = 4002,        // 数据加载错误
    ConfigurationError = 5001    // 配置错误
}
```

---

## 📚 **相关文档**
- [VRoid核心系统文档](01_VRoid核心系统文档.md) - 系统架构详解
- [数据管理系统文档](02_数据管理系统文档.md) - 数据处理细节
- [编辑器工具文档](03_编辑器工具文档.md) - 编辑器工具使用
- [FAQ问题解答](08_FAQ问题解答.md) - 常见问题解决

---

**版本**: 1.0.0  
**最后更新**: 2025-01-22  
**API数量**: 120+ 公开接口  
**维护者**: VRM系统API开发团队 