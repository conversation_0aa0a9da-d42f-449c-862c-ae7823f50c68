using UnityEngine;
using UnityEditor;
using VRoidFaceCustomization;
using System.Collections.Generic;
using System.Linq;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// VRM10骨骼提取工具
    /// 从完整的VRoid模型中提取服装的完整骨骼数据
    /// </summary>
    public class VRM10BoneExtractor : EditorWindow
    {
        [Header("输入")]
        private GameObject sourceVRoidModel;
        private GameObject clothPrefab;
        
        [Header("输出")]
        private string outputPath = "Assets/Prefabs/clorh model/ExtractedClothes/";
        
        [Header("设置")]
        private bool includeBindposes = true;
        private bool includeWorldData = true;
        private bool autoDetectVRoidBones = true;
        private bool createBackup = true;
        
        private Vector2 scrollPosition;
        
        [MenuItem("Tools/VRM 1.0/Bone Extractor")]
        public static void ShowWindow()
        {
            GetWindow<VRM10BoneExtractor>("骨骼提取工具");
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("VRM10 骨骼提取工具", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("从完整VRoid模型中提取服装的完整骨骼数据", MessageType.Info);
            EditorGUILayout.Space();
            
            // 输入设置
            EditorGUILayout.LabelField("输入设置", EditorStyles.boldLabel);
            sourceVRoidModel = (GameObject)EditorGUILayout.ObjectField(
                "源VRoid模型", sourceVRoidModel, typeof(GameObject), true);
            
            clothPrefab = (GameObject)EditorGUILayout.ObjectField(
                "服装Prefab", clothPrefab, typeof(GameObject), false);
            
            EditorGUILayout.Space();
            
            // 输出设置
            EditorGUILayout.LabelField("输出设置", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            outputPath = EditorGUILayout.TextField("输出路径", outputPath);
            if (GUILayout.Button("选择", GUILayout.Width(50)))
            {
                string path = EditorUtility.OpenFolderPanel("选择输出文件夹", outputPath, "");
                if (!string.IsNullOrEmpty(path))
                {
                    outputPath = "Assets" + path.Substring(Application.dataPath.Length);
                }
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 提取设置
            EditorGUILayout.LabelField("提取设置", EditorStyles.boldLabel);
            includeBindposes = EditorGUILayout.Toggle("包含绑定姿势", includeBindposes);
            includeWorldData = EditorGUILayout.Toggle("包含世界坐标", includeWorldData);
            autoDetectVRoidBones = EditorGUILayout.Toggle("自动检测VRoid骨骼", autoDetectVRoidBones);
            createBackup = EditorGUILayout.Toggle("创建备份", createBackup);
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUI.BeginDisabledGroup(sourceVRoidModel == null || clothPrefab == null);
            
            if (GUILayout.Button("分析骨骼结构", GUILayout.Height(30)))
            {
                AnalyzeBoneStructure();
            }
            
            if (GUILayout.Button("提取骨骼数据", GUILayout.Height(30)))
            {
                ExtractBoneData();
            }
            
            EditorGUI.EndDisabledGroup();
            
            if (GUILayout.Button("批量处理文件夹"))
            {
                BatchProcessFolder();
            }
        }
        
        private void AnalyzeBoneStructure()
        {
            Debug.Log("=== 开始分析骨骼结构 ===");
            
            var clothRenderer = clothPrefab.GetComponent<SkinnedMeshRenderer>();
            if (clothRenderer == null)
            {
                EditorUtility.DisplayDialog("错误", "服装Prefab没有SkinnedMeshRenderer组件", "确定");
                return;
            }
            
            var sourceBones = sourceVRoidModel.GetComponentsInChildren<Transform>();
            var sourceBoneMap = sourceBones.ToDictionary(b => b.name, b => b);
            
            Debug.Log($"源模型骨骼总数: {sourceBones.Length}");
            Debug.Log($"服装需要骨骼数: {clothRenderer.bones.Length}");
            
            int foundCount = 0;
            int vroidCount = 0;
            
            foreach (var bone in clothRenderer.bones)
            {
                if (bone == null) continue;
                
                bool found = sourceBoneMap.ContainsKey(bone.name);
                bool isVRoid = IsVRoidSpecificBone(bone.name);
                
                if (found) foundCount++;
                if (isVRoid) vroidCount++;
                
                string status = found ? "✓" : "✗";
                string type = isVRoid ? "[VRoid]" : "[标准]";
                
                Debug.Log($"{status} {type} {bone.name}");
            }
            
            Debug.Log($"=== 分析完成 ===");
            Debug.Log($"找到骨骼: {foundCount}/{clothRenderer.bones.Length}");
            Debug.Log($"VRoid专用骨骼: {vroidCount}");
            Debug.Log($"标准骨骼: {clothRenderer.bones.Length - vroidCount}");
        }
        
        private void ExtractBoneData()
        {
            try
            {
                Debug.Log("=== 开始提取骨骼数据 ===");
                
                var clothRenderer = clothPrefab.GetComponent<SkinnedMeshRenderer>();
                if (clothRenderer == null)
                {
                    throw new System.Exception("服装Prefab没有SkinnedMeshRenderer组件");
                }
                
                // 创建备份
                if (createBackup)
                {
                    CreateBackup();
                }
                
                // 获取源模型的骨骼映射
                var sourceBones = sourceVRoidModel.GetComponentsInChildren<Transform>();
                var sourceBoneMap = sourceBones.ToDictionary(b => b.name, b => b);
                
                // 提取骨骼信息
                var boneInfos = new List<ClothBoneInfo>();
                var bindposes = new List<Matrix4x4>();
                
                Transform rootBone = clothRenderer.rootBone;
                if (rootBone == null && clothRenderer.bones.Length > 0)
                {
                    rootBone = FindRootBone(clothRenderer.bones, sourceBoneMap);
                }
                
                for (int i = 0; i < clothRenderer.bones.Length; i++)
                {
                    var bone = clothRenderer.bones[i];
                    if (bone == null)
                    {
                        Debug.LogWarning($"骨骼索引 {i} 为空，跳过");
                        continue;
                    }
                    
                    // 在源模型中查找对应的骨骼
                    if (!sourceBoneMap.TryGetValue(bone.name, out Transform sourceBone))
                    {
                        Debug.LogError($"在源模型中找不到骨骼: {bone.name}");
                        continue;
                    }
                    
                    // 创建骨骼信息
                    bool isVRoid = autoDetectVRoidBones ? IsVRoidSpecificBone(bone.name) : false;
                    var boneInfo = ClothBoneInfo.CreateFromTransform(sourceBone, rootBone, i, isVRoid);
                    boneInfos.Add(boneInfo);
                    
                    // 添加绑定姿势
                    if (includeBindposes && clothRenderer.sharedMesh != null && 
                        i < clothRenderer.sharedMesh.bindposes.Length)
                    {
                        bindposes.Add(clothRenderer.sharedMesh.bindposes[i]);
                    }
                    else
                    {
                        // 计算绑定姿势
                        var bindpose = sourceBone.worldToLocalMatrix * clothRenderer.transform.localToWorldMatrix;
                        bindposes.Add(bindpose);
                    }
                }
                
                // 添加或更新骨骼数据组件
                var boneDataComponent = clothPrefab.GetComponent<VRM10ClothBoneData>();
                if (boneDataComponent == null)
                {
                    boneDataComponent = clothPrefab.AddComponent<VRM10ClothBoneData>();
                }
                
                boneDataComponent.SetBoneData(boneInfos, bindposes, 
                    rootBone != null ? rootBone.name : "", sourceVRoidModel.name);
                
                // 验证数据
                if (!boneDataComponent.ValidateBoneData())
                {
                    throw new System.Exception("骨骼数据验证失败");
                }
                
                // 保存Prefab
                EditorUtility.SetDirty(clothPrefab);
                PrefabUtility.SavePrefabAsset(clothPrefab);
                
                Debug.Log("=== 骨骼数据提取完成 ===");
                Debug.Log($"提取了 {boneInfos.Count} 个骨骼的完整数据");
                Debug.Log($"VRoid专用骨骼: {boneInfos.Count(b => b.isVRoidSpecific)}");
                Debug.Log(boneDataComponent.GetDebugInfo());
                
                EditorUtility.DisplayDialog("成功", 
                    $"骨骼数据提取完成！\n提取了 {boneInfos.Count} 个骨骼", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"骨骼数据提取失败: {e.Message}");
                EditorUtility.DisplayDialog("错误", $"提取失败: {e.Message}", "确定");
            }
        }
        
        private Transform FindRootBone(Transform[] bones, Dictionary<string, Transform> sourceBoneMap)
        {
            // 查找最可能的根骨骼
            string[] rootCandidates = { "Hips", "Root", "Armature", "Skeleton" };
            
            foreach (string candidate in rootCandidates)
            {
                if (sourceBoneMap.TryGetValue(candidate, out Transform root))
                {
                    return root;
                }
            }
            
            // 如果找不到标准根骨骼，返回第一个非空骨骼
            return bones.FirstOrDefault(b => b != null);
        }
        
        private bool IsVRoidSpecificBone(string boneName)
        {
            return boneName.Contains("transferable_J_") ||
                   boneName.Contains("CoatSkirt") ||
                   boneName.Contains("_Sec_") ||
                   boneName.Contains("_Adj_") ||
                   boneName.Contains("_Sub_") ||
                   boneName.Contains(".transferable_") ||
                   boneName.Length > 50;
        }
        
        private void CreateBackup()
        {
            string backupPath = outputPath + "Backups/";
            if (!System.IO.Directory.Exists(backupPath))
            {
                System.IO.Directory.CreateDirectory(backupPath);
            }
            
            string backupName = $"{clothPrefab.name}_backup_{System.DateTime.Now:yyyyMMdd_HHmmss}.prefab";
            string fullBackupPath = backupPath + backupName;
            
            AssetDatabase.CopyAsset(AssetDatabase.GetAssetPath(clothPrefab), fullBackupPath);
            Debug.Log($"创建备份: {fullBackupPath}");
        }
        
        private void BatchProcessFolder()
        {
            string folderPath = EditorUtility.OpenFolderPanel("选择服装文件夹", outputPath, "");
            if (string.IsNullOrEmpty(folderPath)) return;
            
            // 实现批量处理逻辑
            Debug.Log($"批量处理文件夹: {folderPath}");
            EditorUtility.DisplayDialog("提示", "批量处理功能开发中", "确定");
        }
    }
}
