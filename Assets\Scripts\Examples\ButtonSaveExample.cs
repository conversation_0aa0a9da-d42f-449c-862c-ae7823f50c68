using UnityEngine;
using UnityEngine.UI;
using UniVRM10;
using VRoidFaceCustomization.Data;

/// <summary>
/// 示例：如何在现有按钮脚本中添加VRM保存功能
/// </summary>
public class ButtonSaveExample : MonoBehaviour
{
    [Header("UI")]
    public Button saveButton;
    
    private void Start()
    {
        // 绑定按钮事件
        if (saveButton != null)
        {
            saveButton.onClick.AddListener(OnSaveButtonClick);
        }
    }
    
    /// <summary>
    /// 按钮点击事件 - 保存VRM状态
    /// </summary>
    private void OnSaveButtonClick()
    {
        Debug.Log("🎯 保存按钮被点击");
        
        // 查找VRM对象
        var vrmInstance = FindObjectOfType<Vrm10Instance>();
        if (vrmInstance == null)
        {
            Debug.LogError("❌ 未找到VRM对象！");
            return;
        }
        
        // 获取VRMStateManager
        var stateManager = VRMStateManager.Instance;
        if (stateManager == null)
        {
            Debug.LogError("❌ VRMStateManager未找到！");
            return;
        }
        
        // 保存VRM状态
        var renderState = stateManager.CaptureVRMRenderState(vrmInstance.gameObject);
        if (renderState != null)
        {
            Debug.Log("✅ VRM状态保存成功！");
        }
        else
        {
            Debug.LogError("❌ VRM状态保存失败！");
        }
    }
}
