using UnityEngine;
using VRoidFaceCustomization.UI;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// 第三人称测试场景设置工具
    /// 用于在场景中创建必要的基础设施（地面、生成点等）
    /// </summary>
    public class ThirdPersonSceneSetup : MonoBehaviour
    {
        [Header("地面设置")]
        [SerializeField] private bool createGround = true;
        [SerializeField] private Vector3 groundSize = new Vector3(50f, 1f, 50f);
        [SerializeField] private Vector3 groundPosition = new Vector3(0f, -0.5f, 0f);
        [SerializeField] private Material groundMaterial;
        
        [Header("生成点设置")]
        [SerializeField] private bool createSpawnPoint = true;
        [SerializeField] private Vector3 spawnPointPosition = new Vector3(0f, 1f, 0f);
        
        [Header("摄像机设置")]
        [SerializeField] private bool setupCamera = true;
        [SerializeField] private Vector3 cameraPosition = new Vector3(0f, 2f, -5f);
        [SerializeField] private Vector3 cameraRotation = new Vector3(10f, 0f, 0f);
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool setupOnStart = true;
        
        private GameObject groundObject;
        private GameObject spawnPointObject;
        private ThirdPersonSceneLoader sceneLoader;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupScene();
            }
        }
        
        /// <summary>
        /// 设置场景
        /// </summary>
        [ContextMenu("Setup Scene")]
        public void SetupScene()
        {
            LogDebug("开始设置第三人称测试场景...");
            
            if (createGround)
            {
                CreateGround();
            }
            
            if (createSpawnPoint)
            {
                CreateSpawnPoint();
            }
            
            if (setupCamera)
            {
                SetupMainCamera();
            }
            
            // 配置ThirdPersonSceneLoader
            ConfigureSceneLoader();
            
            LogDebug("场景设置完成！");
        }
        
        /// <summary>
        /// 创建地面
        /// </summary>
        private void CreateGround()
        {
            // 检查是否已存在地面
            groundObject = GameObject.Find("Ground");
            if (groundObject != null)
            {
                LogDebug("地面已存在，跳过创建");
                return;
            }
            
            // 创建地面对象
            groundObject = GameObject.CreatePrimitive(PrimitiveType.Cube);
            groundObject.name = "Ground";
            groundObject.transform.position = groundPosition;
            groundObject.transform.localScale = groundSize;
            
            // 设置材质
            if (groundMaterial != null)
            {
                var renderer = groundObject.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.sharedMaterial = groundMaterial;
                }
            }
            else
            {
                // 使用默认材质并设置颜色
                var renderer = groundObject.GetComponent<Renderer>();
                if (renderer != null)
                {
                    // 在编辑器模式下创建新材质
                    var newMaterial = new Material(Shader.Find("Standard"));
                    newMaterial.color = new Color(0.8f, 0.8f, 0.8f);
                    renderer.sharedMaterial = newMaterial;
                }
            }
            
            // 确保有碰撞器
            var collider = groundObject.GetComponent<Collider>();
            if (collider == null)
            {
                groundObject.AddComponent<BoxCollider>();
            }
            
            LogDebug($"创建地面: 位置{groundPosition}, 大小{groundSize}");
        }
        
        /// <summary>
        /// 创建角色生成点
        /// </summary>
        private void CreateSpawnPoint()
        {
            // 检查是否已存在生成点
            spawnPointObject = GameObject.Find("SpawnPoint");
            if (spawnPointObject != null)
            {
                LogDebug("生成点已存在，更新位置");
                spawnPointObject.transform.position = spawnPointPosition;
                return;
            }

            // 创建生成点对象
            spawnPointObject = new GameObject("SpawnPoint");
            spawnPointObject.transform.position = spawnPointPosition;

            // 创建可视化球体（仅在编辑器中显示）
            var visualSphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            visualSphere.name = "SpawnPoint_Visual";
            visualSphere.transform.SetParent(spawnPointObject.transform);
            visualSphere.transform.localPosition = Vector3.zero;
            visualSphere.transform.localScale = Vector3.one * 0.5f;

            // 设置材质为绿色
            var renderer = visualSphere.GetComponent<Renderer>();
            if (renderer != null)
            {
                var greenMaterial = new Material(Shader.Find("Standard"));
                greenMaterial.color = Color.green;
                greenMaterial.SetFloat("_Metallic", 0f);
                greenMaterial.SetFloat("_Smoothness", 0.5f);
                renderer.sharedMaterial = greenMaterial;
            }

            // 移除碰撞器（不需要物理碰撞）
            var collider = visualSphere.GetComponent<Collider>();
            if (collider != null)
            {
                DestroyImmediate(collider);
            }

            // 添加Gizmo组件用于Scene视图显示
            var gizmo = spawnPointObject.AddComponent<SpawnPointGizmo>();

            LogDebug($"创建生成点: 位置{spawnPointPosition}");
        }
        
        /// <summary>
        /// 设置主摄像机
        /// </summary>
        private void SetupMainCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                LogDebug("未找到主摄像机");
                return;
            }
            
            // 设置摄像机位置和旋转
            mainCamera.transform.position = cameraPosition;
            mainCamera.transform.rotation = Quaternion.Euler(cameraRotation);
            
            LogDebug($"设置主摄像机: 位置{cameraPosition}, 旋转{cameraRotation}");
        }
        
        /// <summary>
        /// 配置ThirdPersonSceneLoader
        /// </summary>
        private void ConfigureSceneLoader()
        {
            sceneLoader = FindObjectOfType<ThirdPersonSceneLoader>();
            if (sceneLoader == null)
            {
                LogDebug("未找到ThirdPersonSceneLoader");
                return;
            }
            
            // 通过反射设置spawnPoint（因为它是private字段）
            var spawnPointField = typeof(ThirdPersonSceneLoader).GetField("spawnPoint", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (spawnPointField != null && spawnPointObject != null)
            {
                spawnPointField.SetValue(sceneLoader, spawnPointObject.transform);
                LogDebug("已配置ThirdPersonSceneLoader的生成点");
            }
        }
        
        /// <summary>
        /// 清理场景
        /// </summary>
        [ContextMenu("Clear Scene")]
        public void ClearScene()
        {
            // 清理地面
            if (groundObject != null)
            {
                DestroyImmediate(groundObject);
                LogDebug("删除地面");
            }
            else
            {
                // 尝试通过名称查找并删除
                var existingGround = GameObject.Find("Ground");
                if (existingGround != null)
                {
                    DestroyImmediate(existingGround);
                    LogDebug("删除现有地面");
                }
            }

            // 清理生成点
            if (spawnPointObject != null)
            {
                DestroyImmediate(spawnPointObject);
                LogDebug("删除生成点");
            }
            else
            {
                // 尝试通过名称查找并删除
                var existingSpawnPoint = GameObject.Find("SpawnPoint");
                if (existingSpawnPoint != null)
                {
                    DestroyImmediate(existingSpawnPoint);
                    LogDebug("删除现有生成点");
                }
            }
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[ThirdPersonSceneSetup] {message}");
            }
        }
        
        /// <summary>
        /// 获取地面对象
        /// </summary>
        public GameObject GetGroundObject()
        {
            return groundObject;
        }
        
        /// <summary>
        /// 获取生成点对象
        /// </summary>
        public GameObject GetSpawnPointObject()
        {
            return spawnPointObject;
        }
    }
    

}
