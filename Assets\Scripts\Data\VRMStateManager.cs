using UnityEngine;
using UniVRM10;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace VRoidFaceCustomization.Data
{
    /// <summary>
    /// VRM状态管理器 - 管理VRM模型的运行时状态
    /// </summary>
    public class VRMStateManager : MonoBehaviour
    {
        public static VRMStateManager Instance { get; private set; }
        
        [Header("状态管理设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool autoSaveOnStateChange = false;
        
        // 当前保存的VRM渲染状态
        private VRMRenderState savedRenderState;
        
        // 事件系统
        public System.Action<VRMRenderState> OnStateCapture;
        public System.Action<VRMRenderState> OnStateApply;
        public System.Action<string> OnStateError;
        
        private void Awake()
        {
            // 单例模式设置
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                LogDebug("🎯 VRMStateManager初始化完成");
            }
            else if (Instance != this)
            {
                Destroy(gameObject);
                return;
            }
        }
        
        /// <summary>
        /// 捕获VRM渲染状态
        /// </summary>
        public VRMRenderState CaptureVRMRenderState(GameObject vrmObject)
        {
            if (vrmObject == null)
            {
                LogError("❌ VRM对象为空，无法捕获状态");
                return null;
            }
            
            LogDebug($"📸 开始捕获VRM渲染状态: {vrmObject.name}");
            
            var vrm10Instance = vrmObject.GetComponent<Vrm10Instance>();
            if (vrm10Instance == null)
            {
                LogError("❌ 未找到Vrm10Instance组件");
                return null;
            }
            
            var renderState = new VRMRenderState
            {
                modelName = vrmObject.name,
                timestamp = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                blendShapes = new Dictionary<string, float>(),
                materialProperties = new Dictionary<string, MaterialPropertyData>(),
                boneTransforms = new Dictionary<string, TransformData>(),
                rootTransform = new TransformData
                {
                    position = vrmObject.transform.position,
                    rotation = vrmObject.transform.rotation,
                    scale = vrmObject.transform.localScale
                }
            };
            
            // 捕获BlendShape数据
            CaptureBlendShapes(vrm10Instance, renderState);
            
            // 捕获材质属性
            CaptureMaterialProperties(vrmObject, renderState);
            
            // 捕获骨骼变换
            CaptureBoneTransforms(vrmObject, renderState);
            
            // 保存状态
            savedRenderState = renderState;
            
            LogDebug($"✅ VRM渲染状态捕获完成");
            LogDebug($"   BlendShapes: {renderState.blendShapes.Count}");
            LogDebug($"   Materials: {renderState.materialProperties.Count}");
            LogDebug($"   Bones: {renderState.boneTransforms.Count}");
            
            OnStateCapture?.Invoke(renderState);
            return renderState;
        }
        
        /// <summary>
        /// 应用VRM渲染状态
        /// </summary>
        public async Task<bool> ApplyVRMRenderState(GameObject vrmObject, VRMRenderState renderState)
        {
            if (vrmObject == null || renderState == null)
            {
                LogError("❌ VRM对象或渲染状态为空");
                return false;
            }
            
            LogDebug($"🎨 开始应用VRM渲染状态到: {vrmObject.name}");
            
            try
            {
                var vrm10Instance = vrmObject.GetComponent<Vrm10Instance>();
                if (vrm10Instance == null)
                {
                    LogError("❌ 未找到Vrm10Instance组件");
                    return false;
                }
                
                // 应用根变换
                vrmObject.transform.position = renderState.rootTransform.position;
                vrmObject.transform.rotation = renderState.rootTransform.rotation;
                vrmObject.transform.localScale = renderState.rootTransform.scale;
                
                // 应用BlendShape
                await ApplyBlendShapes(vrm10Instance, renderState);
                
                // 应用材质属性
                await ApplyMaterialProperties(vrmObject, renderState);
                
                // 应用骨骼变换
                await ApplyBoneTransforms(vrmObject, renderState);
                
                LogDebug("✅ VRM渲染状态应用完成");
                OnStateApply?.Invoke(renderState);
                return true;
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 应用VRM渲染状态时发生错误: {ex.Message}");
                OnStateError?.Invoke(ex.Message);
                return false;
            }
        }
        
        /// <summary>
        /// 检查是否有保存的渲染状态
        /// </summary>
        public bool HasSavedRenderState()
        {
            return savedRenderState != null;
        }
        
        /// <summary>
        /// 获取保存的渲染状态
        /// </summary>
        public VRMRenderState GetSavedRenderState()
        {
            return savedRenderState;
        }
        
        /// <summary>
        /// 清除保存的状态
        /// </summary>
        public void ClearSavedState()
        {
            savedRenderState = null;
            LogDebug("🗑️ 已清除保存的VRM状态");
        }
        
        #region 私有方法
        
        private void CaptureBlendShapes(Vrm10Instance vrm10Instance, VRMRenderState renderState)
        {
            if (vrm10Instance.Runtime?.Expression != null)
            {
                var expression = vrm10Instance.Runtime.Expression;
                var presetKeys = System.Enum.GetValues(typeof(ExpressionKey));
                
                foreach (ExpressionKey key in presetKeys)
                {
                    float weight = expression.GetWeight(key);
                    if (weight > 0.001f) // 只保存有意义的权重
                    {
                        renderState.blendShapes[key.ToString()] = weight;
                    }
                }
            }
        }
        
        private void CaptureMaterialProperties(GameObject vrmObject, VRMRenderState renderState)
        {
            var renderers = vrmObject.GetComponentsInChildren<SkinnedMeshRenderer>();
            foreach (var renderer in renderers)
            {
                if (renderer.material != null)
                {
                    string materialKey = $"{renderer.name}_{renderer.material.name}";
                    var materialData = new MaterialPropertyData
                    {
                        materialName = renderer.material.name,
                        shaderName = renderer.material.shader.name,
                        properties = new Dictionary<string, object>()
                    };
                    
                    // 捕获常见的材质属性
                    if (renderer.material.HasProperty("_Color"))
                        materialData.properties["_Color"] = renderer.material.color;
                    if (renderer.material.HasProperty("_Metallic"))
                        materialData.properties["_Metallic"] = renderer.material.GetFloat("_Metallic");
                    if (renderer.material.HasProperty("_Smoothness"))
                        materialData.properties["_Smoothness"] = renderer.material.GetFloat("_Smoothness");
                    
                    renderState.materialProperties[materialKey] = materialData;
                }
            }
        }
        
        private void CaptureBoneTransforms(GameObject vrmObject, VRMRenderState renderState)
        {
            var animator = vrmObject.GetComponent<Animator>();
            if (animator != null && animator.isHuman)
            {
                // 捕获重要的人体骨骼
                var importantBones = new HumanBodyBones[]
                {
                    HumanBodyBones.Head, HumanBodyBones.Neck,
                    HumanBodyBones.LeftShoulder, HumanBodyBones.RightShoulder,
                    HumanBodyBones.LeftUpperArm, HumanBodyBones.RightUpperArm,
                    HumanBodyBones.LeftLowerArm, HumanBodyBones.RightLowerArm,
                    HumanBodyBones.LeftHand, HumanBodyBones.RightHand
                };
                
                foreach (var boneType in importantBones)
                {
                    var bone = animator.GetBoneTransform(boneType);
                    if (bone != null)
                    {
                        renderState.boneTransforms[boneType.ToString()] = new TransformData
                        {
                            position = bone.localPosition,
                            rotation = bone.localRotation,
                            scale = bone.localScale
                        };
                    }
                }
            }
        }
        
        private async Task ApplyBlendShapes(Vrm10Instance vrm10Instance, VRMRenderState renderState)
        {
            if (vrm10Instance.Runtime?.Expression != null && renderState.blendShapes != null)
            {
                var expression = vrm10Instance.Runtime.Expression;
                
                foreach (var kvp in renderState.blendShapes)
                {
                    if (System.Enum.TryParse<ExpressionKey>(kvp.Key, out var key))
                    {
                        expression.SetWeight(key, kvp.Value);
                    }
                }
                
                await Task.Yield(); // 让渲染系统有时间更新
            }
        }
        
        private async Task ApplyMaterialProperties(GameObject vrmObject, VRMRenderState renderState)
        {
            if (renderState.materialProperties != null)
            {
                var renderers = vrmObject.GetComponentsInChildren<SkinnedMeshRenderer>();
                foreach (var renderer in renderers)
                {
                    string materialKey = $"{renderer.name}_{renderer.material.name}";
                    if (renderState.materialProperties.ContainsKey(materialKey))
                    {
                        var materialData = renderState.materialProperties[materialKey];
                        foreach (var prop in materialData.properties)
                        {
                            if (renderer.material.HasProperty(prop.Key))
                            {
                                if (prop.Value is Color color)
                                    renderer.material.color = color;
                                else if (prop.Value is float floatValue)
                                    renderer.material.SetFloat(prop.Key, floatValue);
                            }
                        }
                    }
                }
                
                await Task.Yield();
            }
        }
        
        private async Task ApplyBoneTransforms(GameObject vrmObject, VRMRenderState renderState)
        {
            if (renderState.boneTransforms != null)
            {
                var animator = vrmObject.GetComponent<Animator>();
                if (animator != null && animator.isHuman)
                {
                    foreach (var kvp in renderState.boneTransforms)
                    {
                        if (System.Enum.TryParse<HumanBodyBones>(kvp.Key, out var boneType))
                        {
                            var bone = animator.GetBoneTransform(boneType);
                            if (bone != null)
                            {
                                bone.localPosition = kvp.Value.position;
                                bone.localRotation = kvp.Value.rotation;
                                bone.localScale = kvp.Value.scale;
                            }
                        }
                    }
                }
                
                await Task.Yield();
            }
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMStateManager] {message}");
            }
        }
        
        private void LogError(string message)
        {
            Debug.LogError($"[VRMStateManager] {message}");
        }
        
        #endregion
    }

    /// <summary>
    /// VRM渲染状态数据结构
    /// </summary>
    [System.Serializable]
    public class VRMRenderState
    {
        public string modelName;
        public string timestamp;
        public Dictionary<string, float> blendShapes;
        public Dictionary<string, MaterialPropertyData> materialProperties;
        public Dictionary<string, TransformData> boneTransforms;
        public TransformData rootTransform;
    }

    /// <summary>
    /// 材质属性数据
    /// </summary>
    [System.Serializable]
    public class MaterialPropertyData
    {
        public string materialName;
        public string shaderName;
        public Dictionary<string, object> properties;
    }

    /// <summary>
    /// 变换数据
    /// </summary>
    [System.Serializable]
    public class TransformData
    {
        public Vector3 position;
        public Quaternion rotation;
        public Vector3 scale;
    }
}
