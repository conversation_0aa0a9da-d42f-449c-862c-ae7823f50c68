using UnityEngine;
using UniVRM10;
using System.Collections.Generic;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM资产管理器
    /// 管理VRM模型资产路径和实例，支持场景间传递
    /// </summary>
    public class VRMAssetManager : MonoBehaviour
    {
    [System.Serializable]
    public class VRMAssetInfo
    {
            public string modelName;
        public string assetPath;
            public GameObject prefabReference;
        
            public VRMAssetInfo(string name, string path, GameObject prefab = null)
            {
                modelName = name;
                assetPath = path;
                prefabReference = prefab;
            }
        }
        
        [Header("VRM资产管理")]
        [SerializeField] private VRMAssetInfo currentVRMInfo;
        [SerializeField] private Vrm10Instance currentVRMInstance;
        [SerializeField] private List<VRMAssetInfo> registeredVRMs = new List<VRMAssetInfo>();
        [SerializeField] private bool debugMode = true;
        
        // 单例
        private static VRMAssetManager _instance;
        public static VRMAssetManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<VRMAssetManager>();
                    if (_instance == null)
                    {
                        Debug.Log("🚨 [VRMAssetManager] 没有找到预部署的实例，创建新的GameObject");
                        var go = new GameObject("VRMAssetManager");
                        _instance = go.AddComponent<VRMAssetManager>();
                        DontDestroyOnLoad(go);
                    }
                    else
                    {
                        Debug.Log("✅ [VRMAssetManager] 使用预部署的实例");
                    }
                }
                return _instance;
            }
        }
        
        void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                // 只有动态创建的实例才设置DontDestroyOnLoad
                if (gameObject.name == "VRMAssetManager" && transform.parent == null)
                {
                    DontDestroyOnLoad(gameObject);
                    LogDebug("🎯 VRMAssetManager初始化 (DontDestroyOnLoad)");
                }
                else
                {
                    LogDebug("🎯 VRMAssetManager初始化 (预部署实例)");
                }
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 注册VRM模型
        /// </summary>
        public void RegisterVRMModel(GameObject vrmObject, string assetPath = null)
            {
            if (vrmObject == null) return;
            
            var vrmInstance = vrmObject.GetComponent<Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug($"❌ {vrmObject.name} 不是有效的VRM模型");
                return;
            }
            
            // 获取模型名称
            string modelName = vrmInstance.Vrm?.Meta?.Name ?? vrmObject.name;
            modelName = modelName.Replace("(Clone)", "").Trim();
            
            // 尝试自动查找资产路径
            if (string.IsNullOrEmpty(assetPath))
            {
                assetPath = FindVRMAssetPath(modelName);
            }
            
            // 创建资产信息
            var info = new VRMAssetInfo(modelName, assetPath, vrmObject);
            
            // 更新当前VRM信息
            currentVRMInfo = info;
            currentVRMInstance = vrmInstance;
            
            // 注册到列表
            var existing = registeredVRMs.Find(v => v.modelName == modelName);
            if (existing != null)
            {
                existing.assetPath = assetPath;
                existing.prefabReference = vrmObject;
            }
            else
            {
                registeredVRMs.Add(info);
            }
            
            LogDebug($"📝 注册VRM模型: {modelName}");
            if (!string.IsNullOrEmpty(assetPath))
            {
                LogDebug($"📁 资源路径: {assetPath}");
            }
        }
        
        /// <summary>
        /// 查找VRM资产路径
        /// </summary>
        private string FindVRMAssetPath(string modelName)
            {
            // 常见的VRM文件位置
            string[] searchPaths = {
                $"Assets/Prefabs/model/{modelName}.vrm",
                $"Assets/VRMModels/{modelName}.vrm",
                $"Assets/Models/{modelName}.vrm",
                $"Assets/Characters/{modelName}.vrm",
                $"Assets/{modelName}.vrm"
            };
            
            foreach (var path in searchPaths)
            {
                if (System.IO.File.Exists(path))
                            {
                                LogDebug($"🔍 找到匹配的VRM资源: {path}");
                    return path;
                            }
                        }
            
            return null;
        }
        
        /// <summary>
        /// 检查是否有有效的VRM模型
        /// </summary>
        public bool HasValidVRMModel()
        {
            return currentVRMInfo != null && !string.IsNullOrEmpty(currentVRMInfo.assetPath);
        }
        
                /// <summary>
        /// 重新创建VRM模型
        /// </summary>
        public GameObject RecreateVRMModel()
        {
            // 🎯 新方案：优先使用场景中的LoadedCharacter
            var existingLoadedCharacter = GameObject.Find("LoadedCharacter");
            if (existingLoadedCharacter != null)
            {
                var existingVrm = existingLoadedCharacter.GetComponent<Vrm10Instance>();
                if (existingVrm != null)
                {
                    LogDebug("🎯 [新方案] RecreateVRMModel使用场景中的LoadedCharacter，跳过重新创建");
                    currentVRMInstance = existingVrm;
                    currentVRMInfo.prefabReference = existingLoadedCharacter;
                    return existingLoadedCharacter;
                }
            }
            
            // 🚫 旧方案：只有在没有LoadedCharacter时才重新创建
            if (!HasValidVRMModel())
            {
                LogDebug("❌ 没有有效的VRM信息");
                return null;
            }
            
            LogDebug("📍 [旧方案] 未找到LoadedCharacter，从资源路径创建新VRM");
            var instance = CreateVRMFromAssetPath(currentVRMInfo.assetPath);
            if (instance != null)
            {
                // 设置为当前实例
                currentVRMInstance = instance.GetComponent<Vrm10Instance>();
                currentVRMInfo.prefabReference = instance;
            }
            
            return instance;
        }
        
        /// <summary>
        /// 获取当前VRM模型GameObject
        /// </summary>
        public GameObject GetCurrentVRMInstance()
        {
            // 🎯 新方案：优先检查场景中的LoadedCharacter
            if (currentVRMInstance == null)
            {
                var existingLoadedCharacter = GameObject.Find("LoadedCharacter");
                if (existingLoadedCharacter != null)
                {
                    var existingVrm = existingLoadedCharacter.GetComponent<Vrm10Instance>();
                    if (existingVrm != null)
                    {
                        LogDebug("🎯 [新方案] 在GetCurrentVRMInstance中使用场景LoadedCharacter");
                        currentVRMInstance = existingVrm;
                        return existingLoadedCharacter;
                    }
                }
            }
            
            if (currentVRMInstance != null)
            {
                return currentVRMInstance.gameObject;
            }
            
            // 尝试从prefabReference获取
            if (currentVRMInfo?.prefabReference != null)
            {
                currentVRMInstance = currentVRMInfo.prefabReference.GetComponent<Vrm10Instance>();
                if (currentVRMInstance != null)
            {
                    return currentVRMInstance.gameObject;
            }
            }
            
            return null;
        }
        
        /// <summary>
        /// 获取当前VRM实例组件
        /// </summary>
        public Vrm10Instance GetCurrentVRMInstanceComponent()
        {
            LogDebug($"🔍 GetCurrentVRMInstance调用 - 当前实例: {currentVRMInstance != null}, 信息: {currentVRMInfo?.modelName}");

            // 🎯 新方案：优先检查场景中的LoadedCharacter
            if (currentVRMInstance == null)
            {
                var existingLoadedCharacter = GameObject.Find("LoadedCharacter");
                if (existingLoadedCharacter != null)
                {
                    var existingVrm = existingLoadedCharacter.GetComponent<Vrm10Instance>();
                    if (existingVrm != null)
                    {
                        LogDebug("🎯 [新方案] 在GetCurrentVRMInstanceComponent中使用场景LoadedCharacter");
                        currentVRMInstance = existingVrm;
                        return currentVRMInstance;
                    }
                }
            }

            // 如果当前实例无效，尝试重新创建
            if (currentVRMInstance == null && currentVRMInfo != null)
            {
                LogDebug("⚠️ 当前VRM实例无效，尝试重新创建...");
                
                if (!string.IsNullOrEmpty(currentVRMInfo.assetPath))
                {
                    var newInstance = CreateVRMFromAssetPath(currentVRMInfo.assetPath);
                    if (newInstance != null)
                    {
                        currentVRMInstance = newInstance.GetComponent<Vrm10Instance>();
                        LogDebug($"✅ 重新创建VRM模型成功: {currentVRMInfo.modelName}");
                    }
                }
            }

            return currentVRMInstance;
        }
        
                /// <summary>
        /// 从资产路径创建VRM
        /// </summary>
        private GameObject CreateVRMFromAssetPath(string assetPath)
        {
            #if UNITY_EDITOR
            var prefab = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
            if (prefab != null)
            {
                var instance = Instantiate(prefab);
                instance.name = prefab.name;
                return instance;
            }
            #endif
            
            LogDebug($"❌ 无法从路径加载VRM: {assetPath}");
            return null;
        }
        
        /// <summary>
        /// 获取当前VRM信息
        /// </summary>
        public VRMAssetInfo GetCurrentVRMInfo()
            {
            return currentVRMInfo;
        }
        
        /// <summary>
        /// 清除当前VRM
        /// </summary>
        public void ClearCurrentVRM()
        {
            currentVRMInfo = null;
            currentVRMInstance = null;
            LogDebug("🗑️ 清除当前VRM信息");
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMAssetManager] {message}");
            }
        }
    }
}
