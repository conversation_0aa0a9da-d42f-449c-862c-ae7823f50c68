using UnityEngine;
using UniVRM10;
using System.Threading.Tasks;
using System.IO;

namespace VRoidFaceCustomization.Data
{
    /// <summary>
    /// VRM运行时加载器 - 处理VRM文件的运行时加载和保存
    /// </summary>
    public class VRMRuntimeLoader : MonoBehaviour
    {
        public static VRMRuntimeLoader Instance { get; private set; }
        
        [Header("加载设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool autoCleanupOnLoad = true;
        
        // 保存的VRM数据
        private byte[] savedVRMData;
        private string savedVRMFileName;
        private VRMRenderState savedRenderState;
        
        // 事件系统
        public System.Action<GameObject> OnVRMLoaded;
        public System.Action<string> OnVRMSaved;
        public System.Action<string> OnLoadError;
        
        private void Awake()
        {
            // 单例模式设置
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                LogDebug("🎯 VRMRuntimeLoader初始化完成");
            }
            else if (Instance != this)
            {
                Destroy(gameObject);
                return;
            }
        }
        
        /// <summary>
        /// 保存VRM文件数据
        /// </summary>
        public async Task<bool> SaveVRMFromFile(string filePath, GameObject vrmObject = null)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                LogError($"❌ VRM文件不存在: {filePath}");
                return false;
            }
            
            try
            {
                LogDebug($"💾 开始保存VRM文件数据: {filePath}");
                
                // 读取VRM文件数据
                savedVRMData = await File.ReadAllBytesAsync(filePath);
                savedVRMFileName = Path.GetFileName(filePath);
                
                // 如果提供了VRM对象，同时保存渲染状态
                if (vrmObject != null)
                {
                    var stateManager = VRMStateManager.Instance;
                    if (stateManager != null)
                    {
                        savedRenderState = stateManager.CaptureVRMRenderState(vrmObject);
                    }
                }
                
                LogDebug($"✅ VRM文件数据保存成功");
                LogDebug($"   文件名: {savedVRMFileName}");
                LogDebug($"   数据大小: {savedVRMData.Length / 1024}KB");
                LogDebug($"   渲染状态: {(savedRenderState != null ? "已保存" : "未保存")}");
                
                OnVRMSaved?.Invoke(savedVRMFileName);
                return true;
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 保存VRM文件数据时发生错误: {ex.Message}");
                OnLoadError?.Invoke(ex.Message);
                return false;
            }
        }
        
        /// <summary>
        /// 在新场景中重新加载VRM
        /// </summary>
        public async Task<GameObject> LoadVRMInNewScene()
        {
            if (savedVRMData == null || savedVRMData.Length == 0)
            {
                LogError("❌ 没有保存的VRM数据");
                return null;
            }
            
            try
            {
                LogDebug($"🔄 开始在新场景中加载VRM: {savedVRMFileName}");
                
                // 使用UniVRM10加载VRM数据
                var vrm10Instance = await Vrm10.LoadBytesAsync(savedVRMData);
                if (vrm10Instance == null)
                {
                    LogError("❌ VRM加载失败");
                    return null;
                }
                
                var vrmObject = vrm10Instance.gameObject;
                vrmObject.name = Path.GetFileNameWithoutExtension(savedVRMFileName);
                
                LogDebug($"✅ VRM加载成功: {vrmObject.name}");
                
                // 如果有保存的渲染状态，应用它
                if (savedRenderState != null)
                {
                    LogDebug("🎨 应用保存的渲染状态...");
                    var stateManager = VRMStateManager.Instance;
                    if (stateManager != null)
                    {
                        await stateManager.ApplyVRMRenderState(vrmObject, savedRenderState);
                    }
                }
                
                OnVRMLoaded?.Invoke(vrmObject);
                return vrmObject;
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 加载VRM时发生错误: {ex.Message}");
                OnLoadError?.Invoke(ex.Message);
                return null;
            }
        }
        
        /// <summary>
        /// 检查是否有保存的VRM数据
        /// </summary>
        public bool HasSavedVRMData()
        {
            return savedVRMData != null && savedVRMData.Length > 0;
        }
        
        /// <summary>
        /// 获取保存的VRM文件名
        /// </summary>
        public string GetSavedVRMFileName()
        {
            return savedVRMFileName;
        }
        
        /// <summary>
        /// 获取保存的渲染状态
        /// </summary>
        public VRMRenderState GetSavedRenderState()
        {
            return savedRenderState;
        }
        
        /// <summary>
        /// 清除保存的数据
        /// </summary>
        public void ClearSavedData()
        {
            savedVRMData = null;
            savedVRMFileName = null;
            savedRenderState = null;
            LogDebug("🗑️ 已清除保存的VRM数据");
        }
        
        /// <summary>
        /// 保存当前场景中的VRM对象
        /// </summary>
        public async Task<bool> SaveCurrentSceneVRM()
        {
            var vrm10Instance = FindObjectOfType<Vrm10Instance>();
            if (vrm10Instance == null)
            {
                LogError("❌ 当前场景中没有找到VRM对象");
                return false;
            }
            
            // 尝试从VRM实例获取原始文件路径
            // 注意：这需要VRM对象有相关的文件路径信息
            // 如果没有，可能需要用户手动指定或使用其他方法
            
            LogDebug("⚠️ SaveCurrentSceneVRM需要VRM文件路径，请使用SaveVRMFromFile方法");
            return false;
        }
        
        /// <summary>
        /// 获取VRM数据统计信息
        /// </summary>
        public VRMDataInfo GetVRMDataInfo()
        {
            return new VRMDataInfo
            {
                hasVRMData = HasSavedVRMData(),
                fileName = savedVRMFileName,
                dataSize = savedVRMData?.Length ?? 0,
                hasRenderState = savedRenderState != null,
                blendShapeCount = savedRenderState?.blendShapes?.Count ?? 0,
                materialCount = savedRenderState?.materialProperties?.Count ?? 0,
                boneCount = savedRenderState?.boneTransforms?.Count ?? 0
            };
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMRuntimeLoader] {message}");
            }
        }
        
        private void LogError(string message)
        {
            Debug.LogError($"[VRMRuntimeLoader] {message}");
        }
    }
    
    /// <summary>
    /// VRM数据信息
    /// </summary>
    [System.Serializable]
    public class VRMDataInfo
    {
        public bool hasVRMData;
        public string fileName;
        public int dataSize;
        public bool hasRenderState;
        public int blendShapeCount;
        public int materialCount;
        public int boneCount;
        
        public override string ToString()
        {
            return $"VRM数据: {(hasVRMData ? "有" : "无")}, " +
                   $"文件: {fileName ?? "无"}, " +
                   $"大小: {dataSize / 1024}KB, " +
                   $"渲染状态: {(hasRenderState ? "有" : "无")}";
        }
    }
}
