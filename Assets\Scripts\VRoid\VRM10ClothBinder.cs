using UnityEngine;
using UniVRM10;
using System.Collections.Generic;
using System.Linq;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM 1.0 动态换装绑定器 - 核心换装引擎
    /// 
    /// <para>
    /// 这是专门为VRM 1.0模型设计的高级换装系统核心组件。
    /// 支持实时服装切换、智能骨骼映射、冲突检测和SpringBone物理系统集成。
    /// 专门优化了VRoid Studio导出模型的兼容性，提供稳定可靠的换装体验。
    /// </para>
    /// 
    /// <para><b>核心功能：</b></para>
    /// <list type="bullet">
    /// <item><description>🔄 动态换装 - 运行时实时服装切换，无需重启</description></item>
    /// <item><description>🦴 智能骨骼映射 - 自动匹配骨骼结构，支持精确和模糊匹配</description></item>
    /// <item><description>⚡ 冲突检测 - 自动处理服装间冲突（如上衣vs连衣裙）</description></item>
    /// <item><description>🌸 SpringBone集成 - 保留VRM物理系统，支持动态效果</description></item>
    /// <item><description>🎨 材质管理 - 自动处理材质副本和渲染优化</description></item>
    /// <item><description>📊 性能优化 - 智能批量操作和渲染边界更新</description></item>
    /// </list>
    /// 
    /// <para><b>VRoid专用优化：</b></para>
    /// <list type="bullet">
    /// <item><description>🔧 VRoid模式支持 - 专门优化VRoid Studio导出的模型</description></item>
    /// <item><description>🦴 缺失骨骼创建 - 自动创建缺失的中间骨骼节点</description></item>
    /// <item><description>⚖️ 灵活成功率 - 降低绑定成功率要求，提高兼容性</description></item>
    /// <item><description>🔄 延迟绑定选项 - 支持延迟绑定处理复杂情况</description></item>
    /// </list>
    /// 
    /// <para><b>使用示例：</b></para>
    /// <code>
    /// var clothBinder = GetComponent&lt;VRM10ClothBinder&gt;();
    /// 
    /// // 基础换装
    /// clothBinder.WearCloth(clothPrefab);
    /// clothBinder.RemoveClothByType(VRM10ClothType.Top);
    /// 
    /// // 批量换装
    /// clothBinder.WearMultipleClothes(new[] { shirt, pants, shoes });
    /// clothBinder.RemoveAllClothes();
    /// 
    /// // 获取穿戴状态
    /// var wornClothes = clothBinder.GetWornClothes();
    /// bool isWearing = clothBinder.IsWearingClothType(VRM10ClothType.Dress);
    /// 
    /// // 高级功能
    /// clothBinder.SetClothVisibility(clothPrefab, false);
    /// clothBinder.UpdateAllBounds();
    /// </code>
    /// 
    /// <para><b>支持的服装类型：</b></para>
    /// <list type="table">
    /// <item><term>Top</term><description>上衣类（衬衫、T恤、外套等）</description></item>
    /// <item><term>Bottom</term><description>下装类（裤子、裙子、短裤等）</description></item>
    /// <item><term>Dress</term><description>连衣裙类（与Top/Bottom冲突）</description></item>
    /// <item><term>Shoes</term><description>鞋子类（运动鞋、高跟鞋等）</description></item>
    /// <item><term>Accessory</term><description>配饰类（包包、饰品等）</description></item>
    /// <item><term>Hat</term><description>帽子类（棒球帽、贝雷帽等）</description></item>
    /// <item><term>Gloves</term><description>手套类</description></item>
    /// <item><term>Socks</term><description>袜子类</description></item>
    /// </list>
    /// 
    /// <para><b>高级特性：</b></para>
    /// <list type="number">
    /// <item><description><b>双重绑定验证</b> - 确保骨骼绑定的稳定性和准确性</description></item>
    /// <item><description><b>渐进式绑定</b> - 支持分帧处理，避免性能尖峰</description></item>
    /// <item><description><b>智能边界更新</b> - 自动优化渲染边界，提升性能</description></item>
    /// <item><description><b>材质实例化</b> - 避免材质冲突，支持独立修改</description></item>
    /// </list>
    /// 
    /// <para><b>注意事项：</b></para>
    /// <list type="number">
    /// <item><description>服装Prefab必须使用VRM10ClothExtractor正确提取</description></item>
    /// <item><description>建议启用VRoid模式以获得最佳兼容性</description></item>
    /// <item><description>大量服装切换时建议分帧处理避免卡顿</description></item>
    /// <item><description>SpringBone较多时可能影响性能，可选择性保留</description></item>
    /// </list>
    /// 
    /// <para><b>版本：</b> 2.0.0</para>
    /// <para><b>兼容：</b> VRM 1.0, VRoid Studio, UniVRM10</para>
    /// <para><b>依赖：</b> VRM10ClothBoneData, VRM10SharedTypes</para>
    /// </summary>
    public class VRM10ClothBinder : MonoBehaviour
    {
        [Header("绑定设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool autoUpdateBounds = true;
        [SerializeField] private bool preserveSpringBones = true;
        [SerializeField] private bool strictBoneMapping = false;

        [Header("VRoid专用设置")]
        [SerializeField] private bool enableVRoidMode = true;
        [SerializeField] private bool createMissingBones = true;
        [SerializeField] private bool usePhysicsBonesAsStatic = true; // 预留的物理骨骼设置
        [SerializeField] private float minimumSuccessRate = 0.6f; // 降低成功率要求
        [SerializeField] private bool delayedBoneBinding = false; // 延迟骨骼绑定（建议关闭）
        [SerializeField] private float bindingDelay = 0.1f; // 绑定延迟时间（秒）
        [SerializeField] private bool forceSyncBinding = true; // 强制同步绑定
        [SerializeField] private bool preInitializeBones = true; // 预初始化骨骼（新增）
        [SerializeField] private int stabilizationFrames = 3; // 骨骼稳定帧数（新增）
        
        [Header("VRM 1.0 组件")]
        [SerializeField] private Vrm10Instance vrmInstance;
        [SerializeField] private Transform avatarRoot;
        
        [Header("当前服装")]
        [SerializeField] private List<VRM10WornCloth> wornClothes = new List<VRM10WornCloth>();
        
        // 骨骼映射缓存
        private Dictionary<string, Transform> boneMap = new Dictionary<string, Transform>();
        private bool isBoneMapInitialized = false;

        // VRoid专用骨骼缓存
        private Dictionary<string, Transform> createdBones = new Dictionary<string, Transform>();
        private Transform dynamicBonesParent;
        
        // 事件
        public System.Action<VRM10WornCloth> OnClothWorn;
        public System.Action<VRM10WornCloth> OnClothRemoved;
        public System.Action OnAllClothesRemoved;
        

        
        private void Awake()
        {
            InitializeVRMComponents();
        }
        
        private void Start()
        {
            if (vrmInstance != null)
            {
                InitializeBoneMap();
            }
        }
        
        /// <summary>
        /// 初始化VRM组件
        /// </summary>
        private void InitializeVRMComponents()
        {
            if (vrmInstance == null)
            {
                vrmInstance = GetComponent<Vrm10Instance>();
            }
            
            if (vrmInstance == null)
            {
                vrmInstance = GetComponentInParent<Vrm10Instance>();
            }
            
            if (vrmInstance == null)
            {
                vrmInstance = FindObjectOfType<Vrm10Instance>();
            }
            
            if (avatarRoot == null && vrmInstance != null)
            {
                avatarRoot = vrmInstance.transform;
            }
            
            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] VRM实例: {(vrmInstance != null ? vrmInstance.name : "未找到")}");
                Debug.Log($"[VRM10ClothBinder] Avatar根: {(avatarRoot != null ? avatarRoot.name : "未找到")}");
            }
        }
        
        /// <summary>
        /// 初始化骨骼映射
        /// </summary>
        private void InitializeBoneMap()
        {
            if (isBoneMapInitialized || avatarRoot == null) return;
            
            boneMap.Clear();
            
            // 获取所有子骨骼
            Transform[] allBones = avatarRoot.GetComponentsInChildren<Transform>();
            
            foreach (Transform bone in allBones)
            {
                if (!boneMap.ContainsKey(bone.name))
                {
                    boneMap[bone.name] = bone;
                }
            }
            
            isBoneMapInitialized = true;
            
            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 初始化骨骼映射完成，共 {boneMap.Count} 个骨骼");
            }
        }
        
        /// <summary>
        /// 穿上服装
        /// </summary>
        public VRM10WornCloth WearCloth(GameObject clothPrefab)
        {
            if (clothPrefab == null)
            {
                Debug.LogError("[VRM10ClothBinder] 服装Prefab为空");
                return null;
            }

            // 验证系统状态
            if (!ValidateSystemState())
            {
                return null;
            }

            if (!isBoneMapInitialized)
            {
                InitializeBoneMap();
            }

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 开始穿戴服装: {clothPrefab.name}");
            }

            // 预初始化骨骼（关键修复）
            if (preInitializeBones && enableVRoidMode)
            {
                PreInitializeDynamicBones(clothPrefab);
            }

            // 实例化服装
            GameObject clothInstance = Instantiate(clothPrefab, avatarRoot);
            clothInstance.name = clothPrefab.name + "_Instance";

            // 获取服装组件
            var renderer = clothInstance.GetComponent<SkinnedMeshRenderer>();
            var clothInfo = clothInstance.GetComponent<VRM10ClothInfo>();
            var dualMode = clothInstance.GetComponent<VRM10DualModeCloth>();

            // 处理双模式服装
            if (dualMode != null && dualMode.IsDualMode())
            {
                // 切换到换装模式
                dualMode.SwitchToWearMode();
                renderer = dualMode.GetWearRenderer();
                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 检测到双模式服装 {clothPrefab.name}，切换到换装模式");
                }
            }

            if (renderer == null)
            {
                Debug.LogError($"[VRM10ClothBinder] 服装 {clothPrefab.name} 缺少SkinnedMeshRenderer组件");
                DestroyImmediate(clothInstance);
                return null;
            }

            // 验证服装数据
            if (!ValidateClothData(renderer, clothPrefab.name))
            {
                DestroyImmediate(clothInstance);
                return null;
            }

            // 创建穿戴记录
            var wornCloth = new VRM10WornCloth
            {
                name = clothPrefab.name,
                clothObject = clothInstance,
                renderer = renderer,
                clothInfo = clothInfo,
                type = clothInfo?.clothType ?? VRM10ClothType.Other,
                originalBones = renderer.bones,
                dualMode = dualMode  // 添加双模式引用
            };

            // 执行骨骼绑定（使用稳定的绑定方式）
            string bindingError = "";
            bool bindingSuccess = false;

            if (preInitializeBones && enableVRoidMode)
            {
                // 使用稳定的协程绑定
                StartCoroutine(StabilizedBoneBinding(wornCloth, (success) => {
                    bindingSuccess = success;
                }));
                bindingSuccess = true; // 先假设成功，实际结果在协程中处理
            }
            else
            {
                // 传统绑定方式
                bindingSuccess = BindClothBones(wornCloth, out bindingError);
            }

            if (bindingSuccess)
            {
                // 检查冲突并移除
                RemoveConflictingClothes(wornCloth.type);

                // 添加到穿戴列表
                wornClothes.Add(wornCloth);

                // 更新渲染边界
                if (autoUpdateBounds)
                {
                    UpdateRendererBounds(renderer);
                }

                // 处理SpringBone
                if (preserveSpringBones)
                {
                    ProcessSpringBones(wornCloth);
                }

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 成功穿上服装: {wornCloth.name}");
                }

                OnClothWorn?.Invoke(wornCloth);
                return wornCloth;
            }
            else
            {
                Debug.LogError($"[VRM10ClothBinder] 服装 {clothPrefab.name} 骨骼绑定失败: {bindingError}");
                DestroyImmediate(clothInstance);
                return null;
            }
        }
        
        /// <summary>
        /// 脱下服装
        /// </summary>
        public bool RemoveCloth(VRM10WornCloth wornCloth)
        {
            if (wornCloth == null || !wornClothes.Contains(wornCloth))
            {
                return false;
            }
            
            wornClothes.Remove(wornCloth);
            
            if (wornCloth.clothObject != null)
            {
                DestroyImmediate(wornCloth.clothObject);
            }
            
            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 脱下服装: {wornCloth.name}");
            }
            
            OnClothRemoved?.Invoke(wornCloth);
            return true;
        }
        
        /// <summary>
        /// 按类型脱下服装
        /// </summary>
        public bool RemoveClothByType(VRM10ClothType type)
        {
            var clothesToRemove = wornClothes.Where(c => c.type == type).ToList();
            
            foreach (var cloth in clothesToRemove)
            {
                RemoveCloth(cloth);
            }
            
            return clothesToRemove.Count > 0;
        }
        
        /// <summary>
        /// 脱下所有服装
        /// </summary>
        public void RemoveAllClothes()
        {
            var clothesToRemove = wornClothes.ToList();

            foreach (var cloth in clothesToRemove)
            {
                RemoveCloth(cloth);
            }

            // 清理动态创建的骨骼
            if (enableVRoidMode)
            {
                CleanupDynamicBones();
            }

            if (debugMode)
            {
                Debug.Log("[VRM10ClothBinder] 脱下所有服装");
            }

            OnAllClothesRemoved?.Invoke();
        }
        
        /// <summary>
        /// 绑定服装骨骼（数据驱动版本）
        /// </summary>
        private bool BindClothBones(VRM10WornCloth wornCloth, out string errorMessage)
        {
            errorMessage = "";
            var renderer = wornCloth.renderer;
            var originalBones = wornCloth.originalBones;

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 开始绑定服装 {wornCloth.name} 的骨骼");
            }

            if (originalBones == null || originalBones.Length == 0)
            {
                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 服装 {wornCloth.name} 没有骨骼信息，视为静态网格");
                }
                return true; // 静态网格，不需要骨骼绑定
            }

            // 检查是否有保存的骨骼数据
            var boneData = wornCloth.clothObject.GetComponent<VRM10ClothBoneData>();
            if (boneData != null && boneData.ValidateBoneData())
            {
                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 使用保存的骨骼数据进行重建");
                }

                // 执行数据驱动绑定
                bool bindingResult = BindClothBonesFromData(wornCloth, boneData, out errorMessage);

                // 如果启用强制同步绑定，立即强制刷新
                if (bindingResult && forceSyncBinding && enableVRoidMode)
                {
                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 执行强制同步刷新");
                    }

                    // 立即强制刷新渲染器
                    RefreshClothRendering(wornCloth);

                    // 强制更新所有Transform
                    ForceUpdateTransforms(wornCloth);
                }
                // 如果启用延迟优化，启动延迟优化协程
                else if (bindingResult && delayedBoneBinding && enableVRoidMode)
                {
                    StartCoroutine(DelayedOptimizeClothBones(wornCloth, boneData));
                }

                return bindingResult;
            }
            else
            {
                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 没有骨骼数据，使用传统方法绑定");
                }
                return BindClothBonesTraditional(wornCloth, out errorMessage);
            }

        }

        /// <summary>
        /// 传统的骨骼绑定方法（备选方案）
        /// </summary>
        private bool BindClothBonesTraditional(VRM10WornCloth wornCloth, out string errorMessage)
        {
            errorMessage = "";
            var renderer = wornCloth.renderer;
            var originalBones = wornCloth.originalBones;

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 服装有 {originalBones.Length} 个骨骼需要绑定");
                Debug.Log($"[VRM10ClothBinder] 角色骨骼映射表有 {boneMap.Count} 个骨骼");
            }

            Transform[] mappedBones = new Transform[originalBones.Length];
            int successCount = 0;
            int nullBoneCount = 0;
            List<string> failedBones = new List<string>();
            List<string> fuzzyMatchedBones = new List<string>();

            for (int i = 0; i < originalBones.Length; i++)
            {
                if (originalBones[i] == null)
                {
                    mappedBones[i] = null;
                    nullBoneCount++;
                    continue;
                }

                string boneName = originalBones[i].name;
                Transform mappedBone = null;

                // 1. 尝试直接匹配
                if (boneMap.TryGetValue(boneName, out mappedBone))
                {
                    mappedBones[i] = mappedBone;
                    successCount++;
                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 直接匹配骨骼: {boneName}");
                    }
                }
                // 2. 检查是否是已创建的动态骨骼
                else if (createdBones.TryGetValue(boneName, out mappedBone))
                {
                    mappedBones[i] = mappedBone;
                    successCount++;
                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 使用已创建的动态骨骼: {boneName}");
                    }
                }
                // 3. VRoid模式：尝试创建缺失的骨骼
                else if (enableVRoidMode && createMissingBones && IsVRoidClothBone(boneName))
                {
                    mappedBone = CreateVRoidClothBone(boneName, originalBones[i]);
                    if (mappedBone != null)
                    {
                        mappedBones[i] = mappedBone;
                        successCount++;
                        if (debugMode)
                        {
                            Debug.Log($"[VRM10ClothBinder] 创建VRoid服装骨骼: {boneName}");
                        }
                    }
                    else
                    {
                        mappedBones[i] = GetFallbackBone(boneName);
                        if (debugMode)
                        {
                            Debug.Log($"[VRM10ClothBinder] VRoid骨骼创建失败，使用备选: {boneName}");
                        }
                    }
                }
                // 4. 尝试模糊匹配
                else if (!strictBoneMapping)
                {
                    mappedBone = FindSimilarBone(boneName);
                    if (mappedBone != null)
                    {
                        mappedBones[i] = mappedBone;
                        successCount++;
                        fuzzyMatchedBones.Add($"{boneName} -> {mappedBone.name}");
                        if (debugMode)
                        {
                            Debug.Log($"[VRM10ClothBinder] 模糊匹配骨骼: {boneName} -> {mappedBone.name}");
                        }
                    }
                    else
                    {
                        failedBones.Add(boneName);
                        mappedBones[i] = GetFallbackBone(boneName);
                        if (debugMode)
                        {
                            Debug.LogWarning($"[VRM10ClothBinder] 未找到骨骼: {boneName}，使用备选骨骼");
                        }
                    }
                }
                // 5. 严格模式失败
                else
                {
                    failedBones.Add(boneName);
                    errorMessage = $"严格模式下未找到骨骼: {boneName}";
                    Debug.LogError($"[VRM10ClothBinder] {errorMessage}");
                    return false;
                }
            }

            // 应用骨骼映射
            renderer.bones = mappedBones;
            wornCloth.mappedBones = mappedBones;

            // 设置根骨骼
            if (renderer.rootBone != null)
            {
                string rootBoneName = renderer.rootBone.name;
                if (boneMap.TryGetValue(rootBoneName, out Transform newRootBone))
                {
                    renderer.rootBone = newRootBone;
                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 设置根骨骼: {rootBoneName}");
                    }
                }
                else
                {
                    renderer.rootBone = avatarRoot;
                    Debug.LogWarning($"[VRM10ClothBinder] 未找到根骨骼 {rootBoneName}，使用Avatar根骨骼");
                }
            }

            float successRate = (float)successCount / originalBones.Length;

            // 详细的绑定结果报告
            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 骨骼绑定结果:");
                Debug.Log($"  - 总骨骼数: {originalBones.Length}");
                Debug.Log($"  - 成功绑定: {successCount}");
                Debug.Log($"  - 空骨骼: {nullBoneCount}");
                Debug.Log($"  - 失败骨骼: {failedBones.Count}");
                Debug.Log($"  - 成功率: {successRate:P1}");

                if (fuzzyMatchedBones.Count > 0)
                {
                    Debug.Log($"  - 模糊匹配的骨骼: {string.Join(", ", fuzzyMatchedBones)}");
                }

                if (failedBones.Count > 0)
                {
                    Debug.LogWarning($"  - 失败的骨骼: {string.Join(", ", failedBones)}");
                }
            }

            // 检查成功率（VRoid模式使用更低的要求）
            float requiredSuccessRate = enableVRoidMode ? minimumSuccessRate : 0.8f;
            if (successRate < requiredSuccessRate)
            {
                errorMessage = $"骨骼绑定成功率过低 ({successRate:P1})，需要至少{requiredSuccessRate:P1}。失败的骨骼: {string.Join(", ", failedBones)}";
                return false;
            }

            return true;
        }
        
        /// <summary>
        /// 查找相似的骨骼
        /// </summary>
        private Transform FindSimilarBone(string boneName)
        {
            // 移除常见的前缀和后缀
            string cleanName = boneName.ToLower()
                .Replace("mixamorig:", "")
                .Replace("_end", "")
                .Replace("_l", "")
                .Replace("_r", "")
                .Replace(".l", "")
                .Replace(".r", "");
            
            foreach (var kvp in boneMap)
            {
                string targetName = kvp.Key.ToLower()
                    .Replace("mixamorig:", "")
                    .Replace("_end", "")
                    .Replace("_l", "")
                    .Replace("_r", "")
                    .Replace(".l", "")
                    .Replace(".r", "");
                
                if (targetName.Contains(cleanName) || cleanName.Contains(targetName))
                {
                    return kvp.Value;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 移除冲突的服装
        /// </summary>
        private void RemoveConflictingClothes(VRM10ClothType newClothType)
        {
            // 定义冲突规则
            var conflictingTypes = new List<VRM10ClothType>();

            switch (newClothType)
            {
                case VRM10ClothType.Dress:
                    conflictingTypes.AddRange(new[] {
                        VRM10ClothType.Top,
                        VRM10ClothType.Bottom,
                        VRM10ClothType.Dress
                    });
                    break;

                case VRM10ClothType.Top:
                    conflictingTypes.AddRange(new[] {
                        VRM10ClothType.Top,
                        VRM10ClothType.Dress
                    });
                    break;

                case VRM10ClothType.Bottom:
                    conflictingTypes.AddRange(new[] {
                        VRM10ClothType.Bottom,
                        VRM10ClothType.Dress
                    });
                    break;

                case VRM10ClothType.Shoes:
                    conflictingTypes.Add(VRM10ClothType.Shoes);
                    break;
            }
            
            // 移除冲突的服装
            var conflictingClothes = wornClothes.Where(c => conflictingTypes.Contains(c.type)).ToList();
            foreach (var cloth in conflictingClothes)
            {
                RemoveCloth(cloth);
            }
        }
        
        /// <summary>
        /// 更新渲染器边界
        /// </summary>
        private void UpdateRendererBounds(SkinnedMeshRenderer renderer)
        {
            if (renderer.sharedMesh != null)
            {
                renderer.localBounds = renderer.sharedMesh.bounds;
            }
        }
        
        /// <summary>
        /// 处理SpringBone组件
        /// </summary>
        private void ProcessSpringBones(VRM10WornCloth wornCloth)
        {
            // 查找服装上的SpringBone组件
            var springBoneJoints = wornCloth.clothObject.GetComponentsInChildren<VRM10SpringBoneJoint>();

            if (springBoneJoints.Length > 0)
            {
                // 将SpringBone组件集成到VRM实例的SpringBone系统中
                var vrmSpringBone = vrmInstance.GetComponent<Vrm10InstanceSpringBone>();
                if (vrmSpringBone != null)
                {
                    // 这里需要根据VRM 1.0的SpringBone系统进行集成
                    // 具体实现可能需要调用VRM 1.0的相关API
                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 检测到SpringBone组件，需要集成到VRM系统中");
                    }
                }
            }
        }

        /// <summary>
        /// 重新绑定所有服装（用于角色姿态变化后）
        /// </summary>
        public void RebindAllClothes()
        {
            int successCount = 0;
            foreach (var cloth in wornClothes.ToList())
            {
                if (cloth.clothObject != null)
                {
                    string bindingError;
                    if (BindClothBones(cloth, out bindingError))
                    {
                        successCount++;
                        if (autoUpdateBounds)
                        {
                            UpdateRendererBounds(cloth.renderer);
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"[VRM10ClothBinder] 重新绑定服装 {cloth.name} 失败: {bindingError}");
                    }
                }
            }

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 重新绑定完成: {successCount}/{wornClothes.Count} 件服装成功");
            }
        }
        
        /// <summary>
        /// 获取已穿戴的服装列表
        /// </summary>
        public List<VRM10WornCloth> GetWornClothes()
        {
            return new List<VRM10WornCloth>(wornClothes);
        }
        
        /// <summary>
        /// 获取指定类型的已穿戴服装
        /// </summary>
        public VRM10WornCloth GetWornClothByType(VRM10ClothType type)
        {
            return wornClothes.FirstOrDefault(c => c.type == type);
        }
        
        /// <summary>
        /// 检查是否穿戴了指定类型的服装
        /// </summary>
        public bool IsWearingClothType(VRM10ClothType type)
        {
            return wornClothes.Any(c => c.type == type);
        }

        /// <summary>
        /// 验证系统状态
        /// </summary>
        private bool ValidateSystemState()
        {
            if (vrmInstance == null)
            {
                Debug.LogError("[VRM10ClothBinder] VRM实例未找到，请确保角色有Vrm10Instance组件");
                return false;
            }

            if (avatarRoot == null)
            {
                Debug.LogError("[VRM10ClothBinder] Avatar根骨骼未找到");
                return false;
            }

            if (!isBoneMapInitialized)
            {
                Debug.LogWarning("[VRM10ClothBinder] 骨骼映射未初始化，尝试重新初始化");
                InitializeBoneMap();

                if (!isBoneMapInitialized)
                {
                    Debug.LogError("[VRM10ClothBinder] 骨骼映射初始化失败");
                    return false;
                }
            }

            if (boneMap.Count == 0)
            {
                Debug.LogError("[VRM10ClothBinder] 骨骼映射为空，角色可能没有骨骼结构");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证服装数据
        /// </summary>
        private bool ValidateClothData(SkinnedMeshRenderer renderer, string clothName)
        {
            if (renderer.sharedMesh == null)
            {
                Debug.LogError($"[VRM10ClothBinder] 服装 {clothName} 的SkinnedMeshRenderer没有网格数据");
                return false;
            }

            if (renderer.bones == null)
            {
                Debug.LogWarning($"[VRM10ClothBinder] 服装 {clothName} 没有骨骼数据，将作为静态网格处理");
                return true;
            }

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 服装 {clothName} 验证通过:");
                Debug.Log($"  - 网格: {renderer.sharedMesh.name}");
                Debug.Log($"  - 骨骼数量: {renderer.bones.Length}");
                Debug.Log($"  - 材质数量: {renderer.materials.Length}");
            }

            return true;
        }

        /// <summary>
        /// 诊断骨骼绑定问题
        /// </summary>
        [ContextMenu("诊断骨骼绑定")]
        public void DiagnoseBoneBinding()
        {
            Debug.Log("=== VRM10ClothBinder 诊断报告 ===");

            // 检查VRM组件
            Debug.Log($"VRM实例: {(vrmInstance != null ? vrmInstance.name : "未找到")}");
            Debug.Log($"Avatar根: {(avatarRoot != null ? avatarRoot.name : "未找到")}");
            Debug.Log($"骨骼映射初始化: {isBoneMapInitialized}");
            Debug.Log($"骨骼映射数量: {boneMap.Count}");

            // 显示前10个骨骼
            if (boneMap.Count > 0)
            {
                Debug.Log("角色骨骼示例 (前10个):");
                int count = 0;
                foreach (var kvp in boneMap)
                {
                    if (count >= 10) break;
                    Debug.Log($"  - {kvp.Key}");
                    count++;
                }
            }

            // 检查已穿戴的服装
            Debug.Log($"已穿戴服装数量: {wornClothes.Count}");
            foreach (var cloth in wornClothes)
            {
                Debug.Log($"  - {cloth.name} ({cloth.type})");
            }

            Debug.Log("=== 诊断完成 ===");
        }

        /// <summary>
        /// 重置骨骼映射
        /// </summary>
        [ContextMenu("重置骨骼映射")]
        public void ResetBoneMapping()
        {
            isBoneMapInitialized = false;
            boneMap.Clear();
            createdBones.Clear();
            InitializeBoneMap();
            Debug.Log($"[VRM10ClothBinder] 骨骼映射已重置，重新初始化了 {boneMap.Count} 个骨骼");
        }

        /// <summary>
        /// 测试坐标系修复
        /// </summary>
        [ContextMenu("测试坐标系修复")]
        public void TestCoordinateSystemFix()
        {
            Debug.Log("=== 测试坐标系修复 ===");

            foreach (var cloth in wornClothes)
            {
                if (cloth.renderer != null && cloth.renderer.bones != null)
                {
                    Debug.Log($"检查服装: {cloth.name}");

                    int validBones = 0;
                    int dynamicBones = 0;

                    foreach (var bone in cloth.renderer.bones)
                    {
                        if (bone != null)
                        {
                            validBones++;
                            if (bone.name.Contains("transferable_") || bone.name.Contains("J_Sec_"))
                            {
                                dynamicBones++;
                                Debug.Log($"  动态骨骼: {bone.name} 位置: {bone.position}");
                            }
                        }
                    }

                    Debug.Log($"  有效骨骼: {validBones}/{cloth.renderer.bones.Length}");
                    Debug.Log($"  动态骨骼: {dynamicBones}");

                    // 检查绑定姿势
                    if (cloth.renderer.sharedMesh != null)
                    {
                        Debug.Log($"  绑定姿势数量: {cloth.renderer.sharedMesh.bindposes.Length}");
                        Debug.Log($"  网格名称: {cloth.renderer.sharedMesh.name}");
                    }
                }
            }

            Debug.Log("=== 测试完成 ===");
        }

        /// <summary>
        /// 判断是否是VRoid服装骨骼
        /// </summary>
        private bool IsVRoidClothBone(string boneName)
        {
            // VRoid服装骨骼的特征
            return boneName.Contains("transferable_J_") ||
                   boneName.Contains("CoatSkirt") ||
                   boneName.Contains("Skirt") ||
                   boneName.Contains("Hair") ||
                   boneName.Contains("Accessory") ||
                   boneName.Contains("_Sec_") ||
                   boneName.Contains("_Adj_") ||
                   boneName.Contains("_Sub_");
        }

        /// <summary>
        /// 创建VRoid服装骨骼
        /// </summary>
        private Transform CreateVRoidClothBone(string boneName, Transform originalBone)
        {
            try
            {
                // 确保有动态骨骼父节点
                if (dynamicBonesParent == null)
                {
                    GameObject dynamicParent = new GameObject("DynamicClothBones");
                    dynamicParent.transform.SetParent(avatarRoot);
                    dynamicParent.transform.localPosition = Vector3.zero;
                    dynamicParent.transform.localRotation = Quaternion.identity;
                    dynamicParent.transform.localScale = Vector3.one;
                    dynamicBonesParent = dynamicParent.transform;
                }

                // 创建新骨骼
                GameObject newBone = new GameObject(boneName);
                newBone.transform.SetParent(GetBestParentForClothBone(boneName));

                // 尝试从原始骨骼复制变换信息
                if (originalBone != null)
                {
                    newBone.transform.localPosition = originalBone.localPosition;
                    newBone.transform.localRotation = originalBone.localRotation;
                    newBone.transform.localScale = originalBone.localScale;
                }
                else
                {
                    newBone.transform.localPosition = Vector3.zero;
                    newBone.transform.localRotation = Quaternion.identity;
                    newBone.transform.localScale = Vector3.one;
                }

                // 缓存创建的骨骼
                createdBones[boneName] = newBone.transform;

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 成功创建动态骨骼: {boneName}");
                }

                return newBone.transform;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothBinder] 创建骨骼失败 {boneName}: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// 为服装骨骼找到最佳的父骨骼
        /// </summary>
        private Transform GetBestParentForClothBone(string boneName)
        {
            // 根据骨骼名称推断最佳父骨骼
            if (boneName.Contains("CoatSkirt") || boneName.Contains("Skirt"))
            {
                // 裙摆骨骼通常挂在腰部
                if (boneMap.TryGetValue("Hips", out Transform hips)) return hips;
                if (boneMap.TryGetValue("Spine", out Transform spine)) return spine;
            }
            else if (boneName.Contains("Hair"))
            {
                // 头发骨骼挂在头部
                if (boneMap.TryGetValue("Head", out Transform head)) return head;
                if (boneMap.TryGetValue("Neck", out Transform neck)) return neck;
            }
            else if (boneName.Contains("_L_") || boneName.Contains("Left"))
            {
                // 左侧骨骼
                if (boneMap.TryGetValue("LeftShoulder", out Transform leftShoulder)) return leftShoulder;
                if (boneMap.TryGetValue("LeftUpperArm", out Transform leftArm)) return leftArm;
            }
            else if (boneName.Contains("_R_") || boneName.Contains("Right"))
            {
                // 右侧骨骼
                if (boneMap.TryGetValue("RightShoulder", out Transform rightShoulder)) return rightShoulder;
                if (boneMap.TryGetValue("RightUpperArm", out Transform rightArm)) return rightArm;
            }

            // 默认挂在动态骨骼父节点下
            return dynamicBonesParent != null ? dynamicBonesParent : avatarRoot;
        }

        /// <summary>
        /// 获取备选骨骼
        /// </summary>
        private Transform GetFallbackBone(string boneName)
        {
            // 根据骨骼类型返回合适的备选骨骼
            if (boneName.Contains("CoatSkirt") || boneName.Contains("Skirt"))
            {
                if (boneMap.TryGetValue("Hips", out Transform hips)) return hips;
            }
            else if (boneName.Contains("Hair"))
            {
                if (boneMap.TryGetValue("Head", out Transform head)) return head;
            }
            else if (boneName.Contains("_L_") || boneName.Contains("Left"))
            {
                if (boneMap.TryGetValue("LeftShoulder", out Transform leftShoulder)) return leftShoulder;
            }
            else if (boneName.Contains("_R_") || boneName.Contains("Right"))
            {
                if (boneMap.TryGetValue("RightShoulder", out Transform rightShoulder)) return rightShoulder;
            }

            // 最终备选：根骨骼
            return avatarRoot;
        }

        /// <summary>
        /// 使用保存的骨骼数据进行绑定
        /// </summary>
        private bool BindClothBonesFromData(VRM10WornCloth wornCloth, VRM10ClothBoneData boneData, out string errorMessage)
        {
            errorMessage = "";

            try
            {
                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 开始数据驱动的骨骼重建");
                    Debug.Log(boneData.GetDebugInfo());
                }

                // 重建骨骼结构
                var rebuiltBones = RebuildBoneStructure(boneData);
                if (rebuiltBones == null || rebuiltBones.Count == 0)
                {
                    errorMessage = "骨骼结构重建失败";
                    return false;
                }

                // 更新SkinnedMeshRenderer
                var renderer = wornCloth.renderer;
                var newBonesArray = new Transform[rebuiltBones.Count];

                for (int i = 0; i < rebuiltBones.Count; i++)
                {
                    newBonesArray[i] = rebuiltBones[i];
                }

                renderer.bones = newBonesArray;
                wornCloth.mappedBones = newBonesArray;

                // 设置根骨骼
                string rootBoneName = boneData.GetRootBoneName();
                if (!string.IsNullOrEmpty(rootBoneName) && boneMap.TryGetValue(rootBoneName, out Transform rootBone))
                {
                    renderer.rootBone = rootBone;
                }
                else
                {
                    renderer.rootBone = avatarRoot;
                }

                // 更新绑定姿势
                UpdateBindposes(renderer, boneData);

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 数据驱动绑定完成: {rebuiltBones.Count} 个骨骼");
                }

                return true;
            }
            catch (System.Exception e)
            {
                errorMessage = $"数据驱动绑定失败: {e.Message}";
                Debug.LogError($"[VRM10ClothBinder] {errorMessage}");
                return false;
            }
        }

        /// <summary>
        /// 重建骨骼结构
        /// </summary>
        private List<Transform> RebuildBoneStructure(VRM10ClothBoneData boneData)
        {
            var rebuiltBones = new List<Transform>();
            var boneInfos = boneData.GetBonesSortedByDepth(); // 按层级深度排序
            var createdBonesMap = new Dictionary<string, Transform>();

            // 确保有动态骨骼父节点
            if (dynamicBonesParent == null)
            {
                GameObject dynamicParent = new GameObject("DynamicClothBones");
                dynamicParent.transform.SetParent(avatarRoot);
                dynamicParent.transform.localPosition = Vector3.zero;
                dynamicParent.transform.localRotation = Quaternion.identity;
                dynamicParent.transform.localScale = Vector3.one;
                dynamicBonesParent = dynamicParent.transform;
            }

            foreach (var boneInfo in boneInfos)
            {
                Transform bone = null;

                // 1. 尝试使用现有的标准骨骼
                if (!boneInfo.isVRoidSpecific && boneMap.TryGetValue(boneInfo.boneName, out bone))
                {
                    rebuiltBones.Add(bone);
                    createdBonesMap[boneInfo.boneName] = bone;

                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 使用现有骨骼: {boneInfo.boneName}");
                    }
                }
                // 2. 创建VRoid专用骨骼
                else
                {
                    bone = CreateBoneFromData(boneInfo, createdBonesMap);
                    if (bone != null)
                    {
                        rebuiltBones.Add(bone);
                        createdBonesMap[boneInfo.boneName] = bone;
                        createdBones[boneInfo.boneName] = bone; // 添加到清理列表

                        if (debugMode)
                        {
                            Debug.Log($"[VRM10ClothBinder] 创建骨骼: {boneInfo.boneName}");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"[VRM10ClothBinder] 创建骨骼失败: {boneInfo.boneName}");
                        // 使用备选骨骼
                        bone = GetFallbackBone(boneInfo.boneName);
                        rebuiltBones.Add(bone);
                    }
                }
            }

            return rebuiltBones;
        }

        /// <summary>
        /// 根据保存的数据创建骨骼（修复坐标系问题）
        /// </summary>
        private Transform CreateBoneFromData(ClothBoneInfo boneInfo, Dictionary<string, Transform> createdBonesMap)
        {
            try
            {
                // 创建骨骼GameObject
                GameObject boneObj = new GameObject(boneInfo.boneName);
                Transform bone = boneObj.transform;

                // 设置父骨骼
                Transform parent = GetCorrectParentBone(boneInfo, createdBonesMap);
                bone.SetParent(parent);

                // 修复坐标系问题：使用智能坐标计算
                SetBoneTransformSmart(bone, boneInfo, parent);

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 创建骨骼 {boneInfo.boneName}, 父骨骼: {parent.name}");
                }

                return bone;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothBinder] 创建骨骼失败 {boneInfo.boneName}: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取正确的父骨骼
        /// </summary>
        private Transform GetCorrectParentBone(ClothBoneInfo boneInfo, Dictionary<string, Transform> createdBonesMap)
        {
            Transform parent = null;

            if (!string.IsNullOrEmpty(boneInfo.parentBoneName))
            {
                // 优先使用已创建的骨骼
                if (createdBonesMap.TryGetValue(boneInfo.parentBoneName, out parent))
                {
                    return parent;
                }
                // 其次尝试标准骨骼
                else if (boneMap.TryGetValue(boneInfo.parentBoneName, out parent))
                {
                    return parent;
                }
            }

            // 根据骨骼类型智能选择父骨骼
            return GetSmartParentBone(boneInfo.boneName);
        }

        /// <summary>
        /// 智能选择父骨骼
        /// </summary>
        private Transform GetSmartParentBone(string boneName)
        {
            // 根据骨骼名称特征选择最合适的父骨骼
            if (boneName.Contains("CoatSkirt") || boneName.Contains("Skirt"))
            {
                // 裙摆骨骼挂在腰部
                if (boneMap.TryGetValue("J_Bip_C_Hips", out Transform hips)) return hips;
                if (boneMap.TryGetValue("Hips", out Transform hips2)) return hips2;
            }
            else if (boneName.Contains("Bust"))
            {
                // 胸部骨骼挂在胸部
                if (boneMap.TryGetValue("J_Bip_C_Chest", out Transform chest)) return chest;
                if (boneMap.TryGetValue("Chest", out Transform chest2)) return chest2;
            }
            else if (boneName.Contains("Hair"))
            {
                // 头发骨骼挂在头部
                if (boneMap.TryGetValue("J_Bip_C_Head", out Transform head)) return head;
                if (boneMap.TryGetValue("Head", out Transform head2)) return head2;
            }
            else if (boneName.Contains("_L_") || boneName.Contains("Left"))
            {
                // 左侧骨骼
                if (boneMap.TryGetValue("J_Bip_L_Shoulder", out Transform leftShoulder)) return leftShoulder;
                if (boneMap.TryGetValue("LeftShoulder", out Transform leftShoulder2)) return leftShoulder2;
            }
            else if (boneName.Contains("_R_") || boneName.Contains("Right"))
            {
                // 右侧骨骼
                if (boneMap.TryGetValue("J_Bip_R_Shoulder", out Transform rightShoulder)) return rightShoulder;
                if (boneMap.TryGetValue("RightShoulder", out Transform rightShoulder2)) return rightShoulder2;
            }

            // 默认挂在动态骨骼父节点下
            return dynamicBonesParent != null ? dynamicBonesParent : avatarRoot;
        }

        /// <summary>
        /// 智能设置骨骼Transform（解决坐标系问题）
        /// </summary>
        private void SetBoneTransformSmart(Transform bone, ClothBoneInfo boneInfo, Transform parent)
        {
            // 方法1：尝试使用相对于根骨骼的坐标
            if (TrySetRelativeToRoot(bone, boneInfo, parent))
            {
                return;
            }

            // 方法2：使用保存的局部坐标
            if (TrySetLocalCoordinates(bone, boneInfo, parent))
            {
                return;
            }

            // 方法3：使用默认位置
            SetDefaultPosition(bone, boneInfo, parent);
        }

        /// <summary>
        /// 尝试使用相对于根骨骼的坐标
        /// </summary>
        private bool TrySetRelativeToRoot(Transform bone, ClothBoneInfo boneInfo, Transform parent)
        {
            try
            {
                // 找到当前的根骨骼
                Transform currentRoot = null;
                if (boneMap.TryGetValue("J_Bip_C_Hips", out currentRoot) ||
                    boneMap.TryGetValue("Hips", out currentRoot) ||
                    boneMap.TryGetValue("Root", out currentRoot))
                {
                    // 计算相对于当前根骨骼的位置
                    Vector3 worldPos = currentRoot.TransformPoint(boneInfo.relativeToRootPosition);
                    bone.position = worldPos;
                    bone.rotation = currentRoot.rotation * boneInfo.relativeToRootRotation;
                    bone.localScale = boneInfo.localScale;

                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 使用相对根骨骼坐标: {bone.name}");
                    }
                    return true;
                }
            }
            catch (System.Exception e)
            {
                if (debugMode)
                {
                    Debug.LogWarning($"[VRM10ClothBinder] 相对根骨骼坐标设置失败: {e.Message}");
                }
            }

            return false;
        }

        /// <summary>
        /// 尝试使用局部坐标
        /// </summary>
        private bool TrySetLocalCoordinates(Transform bone, ClothBoneInfo boneInfo, Transform parent)
        {
            try
            {
                bone.localPosition = boneInfo.localPosition;
                bone.localRotation = boneInfo.localRotation;
                bone.localScale = boneInfo.localScale;

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 使用局部坐标: {bone.name}");
                }
                return true;
            }
            catch (System.Exception e)
            {
                if (debugMode)
                {
                    Debug.LogWarning($"[VRM10ClothBinder] 局部坐标设置失败: {e.Message}");
                }
            }

            return false;
        }

        /// <summary>
        /// 设置默认位置
        /// </summary>
        private void SetDefaultPosition(Transform bone, ClothBoneInfo boneInfo, Transform parent)
        {
            // 使用默认的相对位置
            bone.localPosition = Vector3.zero;
            bone.localRotation = Quaternion.identity;
            bone.localScale = Vector3.one;

            if (debugMode)
            {
                Debug.LogWarning($"[VRM10ClothBinder] 使用默认位置: {bone.name}");
            }
        }

        /// <summary>
        /// 更新绑定姿势（修复坐标系问题）
        /// </summary>
        private void UpdateBindposes(SkinnedMeshRenderer renderer, VRM10ClothBoneData boneData)
        {
            try
            {
                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 开始更新绑定姿势");
                }

                // 总是重新计算绑定姿势，不使用保存的（这是关键修复）
                var newBindposes = new Matrix4x4[renderer.bones.Length];

                for (int i = 0; i < renderer.bones.Length; i++)
                {
                    if (renderer.bones[i] != null)
                    {
                        // 重新计算绑定姿势矩阵
                        newBindposes[i] = renderer.bones[i].worldToLocalMatrix * renderer.transform.localToWorldMatrix;
                    }
                    else
                    {
                        newBindposes[i] = Matrix4x4.identity;
                    }
                }

                // 创建新的网格实例并设置绑定姿势
                if (renderer.sharedMesh != null)
                {
                    var originalMesh = renderer.sharedMesh;
                    var newMesh = Object.Instantiate(originalMesh);
                    newMesh.name = originalMesh.name + "_Rebound";

                    // 设置新的绑定姿势
                    newMesh.bindposes = newBindposes;

                    // 应用新网格
                    renderer.sharedMesh = newMesh;

                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 重新计算并应用绑定姿势: {newBindposes.Length} 个");
                    }
                }
                else
                {
                    Debug.LogWarning($"[VRM10ClothBinder] 渲染器没有网格，无法更新绑定姿势");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothBinder] 更新绑定姿势失败: {e.Message}");
            }
        }

        /// <summary>
        /// 延迟优化服装骨骼协程
        /// </summary>
        private System.Collections.IEnumerator DelayedOptimizeClothBones(VRM10WornCloth wornCloth, VRM10ClothBoneData boneData)
        {
            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 开始延迟优化，等待 {bindingDelay} 秒");
            }

            // 等待指定时间，让Transform系统稳定
            yield return new WaitForSeconds(bindingDelay);

            // 确保在下一帧执行
            yield return null;

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 执行延迟优化");
            }

            // 执行优化操作
            if (wornCloth?.renderer != null)
            {
                // 强制更新Transform
                foreach (var bone in wornCloth.renderer.bones)
                {
                    if (bone != null)
                    {
                        // 强制更新Transform矩阵
                        bone.hasChanged = true;
                    }
                }

                // 强制更新渲染器
                var renderer = wornCloth.renderer;
                renderer.enabled = false;
                yield return null;

                // 重新计算边界
                if (autoUpdateBounds && renderer.sharedMesh != null)
                {
                    renderer.localBounds = renderer.sharedMesh.bounds;
                }

                renderer.enabled = true;

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 延迟优化完成，强制刷新渲染器");
                }
            }
        }

        /// <summary>
        /// 强制刷新服装渲染
        /// </summary>
        public void RefreshClothRendering(VRM10WornCloth wornCloth)
        {
            if (wornCloth?.renderer != null)
            {
                var renderer = wornCloth.renderer;
                renderer.enabled = false;

                // 强制重新计算边界
                if (autoUpdateBounds && renderer.sharedMesh != null)
                {
                    renderer.localBounds = renderer.sharedMesh.bounds;
                }

                renderer.enabled = true;

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 强制刷新服装渲染: {wornCloth.name}");
                }
            }
        }

        /// <summary>
        /// 强制刷新所有服装渲染
        /// </summary>
        [ContextMenu("强制刷新所有服装")]
        public void RefreshAllClothRendering()
        {
            foreach (var cloth in wornClothes)
            {
                RefreshClothRendering(cloth);
            }

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 强制刷新了 {wornClothes.Count} 件服装的渲染");
            }
        }

        /// <summary>
        /// 强制更新服装的Transform
        /// </summary>
        private void ForceUpdateTransforms(VRM10WornCloth wornCloth)
        {
            if (wornCloth?.renderer?.bones == null) return;

            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 强制更新Transform: {wornCloth.name}");
            }

            // 强制更新所有骨骼的Transform
            foreach (var bone in wornCloth.renderer.bones)
            {
                if (bone != null)
                {
                    // 标记Transform已改变，强制Unity更新
                    bone.hasChanged = true;

                    // 强制重新计算世界矩阵
                    var position = bone.position;
                    var rotation = bone.rotation;
                    bone.position = position;
                    bone.rotation = rotation;
                }
            }

            // 强制更新SkinnedMeshRenderer
            var renderer = wornCloth.renderer;
            if (renderer != null)
            {
                // 强制重新计算绑定姿势
                renderer.forceMatrixRecalculationPerRender = true;

                // 触发重新绑定
                var bones = renderer.bones;
                renderer.bones = null;
                renderer.bones = bones;
            }
        }

        /// <summary>
        /// 预初始化动态骨骼（关键修复方法）
        /// </summary>
        private void PreInitializeDynamicBones(GameObject clothPrefab)
        {
            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 开始预初始化动态骨骼: {clothPrefab.name}");
            }

            try
            {
                // 检查是否有骨骼数据
                var boneData = clothPrefab.GetComponent<VRM10ClothBoneData>();
                if (boneData != null)
                {
                    // 预创建所有VRoid专用骨骼
                    var vroidBones = boneData.GetVRoidSpecificBones();
                    foreach (var boneInfo in vroidBones)
                    {
                        if (!createdBones.ContainsKey(boneInfo.boneName))
                        {
                            CreateBoneFromDataPreInit(boneInfo);
                        }
                    }

                    if (debugMode)
                    {
                        Debug.Log($"[VRM10ClothBinder] 预初始化了 {vroidBones.Count} 个VRoid专用骨骼");
                    }
                }
                else
                {
                    // 没有骨骼数据，预创建动态骨骼父节点
                    EnsureDynamicBonesParent();
                }

                // 强制更新Transform系统
                Canvas.ForceUpdateCanvases();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothBinder] 预初始化动态骨骼失败: {e.Message}");
            }
        }

        /// <summary>
        /// 预初始化时创建骨骼
        /// </summary>
        private void CreateBoneFromDataPreInit(ClothBoneInfo boneInfo)
        {
            try
            {
                // 确保有动态骨骼父节点
                EnsureDynamicBonesParent();

                // 创建骨骼GameObject
                GameObject boneObj = new GameObject(boneInfo.boneName);
                Transform bone = boneObj.transform;

                // 设置父骨骼
                Transform parent = GetSmartParentBone(boneInfo.boneName);
                bone.SetParent(parent);

                // 设置初始Transform（使用保守的设置）
                bone.localPosition = Vector3.zero;
                bone.localRotation = Quaternion.identity;
                bone.localScale = Vector3.one;

                // 添加到创建列表
                createdBones[boneInfo.boneName] = bone;

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 预创建骨骼: {boneInfo.boneName}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothBinder] 预创建骨骼失败 {boneInfo.boneName}: {e.Message}");
            }
        }

        /// <summary>
        /// 确保动态骨骼父节点存在
        /// </summary>
        private void EnsureDynamicBonesParent()
        {
            if (dynamicBonesParent == null)
            {
                GameObject dynamicParent = new GameObject("DynamicClothBones");
                dynamicParent.transform.SetParent(avatarRoot);
                dynamicParent.transform.localPosition = Vector3.zero;
                dynamicParent.transform.localRotation = Quaternion.identity;
                dynamicParent.transform.localScale = Vector3.one;
                dynamicBonesParent = dynamicParent.transform;

                if (debugMode)
                {
                    Debug.Log("[VRM10ClothBinder] 创建动态骨骼父节点");
                }
            }
        }

        /// <summary>
        /// 稳定的骨骼绑定协程
        /// </summary>
        private System.Collections.IEnumerator StabilizedBoneBinding(VRM10WornCloth wornCloth, System.Action<bool> onComplete = null)
        {
            if (debugMode)
            {
                Debug.Log($"[VRM10ClothBinder] 开始稳定骨骼绑定: {wornCloth.name}");
            }

            // 等待几帧让Transform系统稳定
            for (int i = 0; i < stabilizationFrames; i++)
            {
                yield return null;
            }

            // 执行骨骼绑定
            string bindingError;
            bool success = BindClothBones(wornCloth, out bindingError);

            if (success)
            {
                // 再等待一帧确保绑定完成
                yield return null;

                // 强制刷新渲染器
                if (forceSyncBinding)
                {
                    RefreshClothRendering(wornCloth);
                    ForceUpdateTransforms(wornCloth);
                }

                if (debugMode)
                {
                    Debug.Log($"[VRM10ClothBinder] 稳定骨骼绑定完成: {wornCloth.name}");
                }
            }
            else
            {
                Debug.LogError($"[VRM10ClothBinder] 稳定骨骼绑定失败: {bindingError}");
            }

            onComplete?.Invoke(success);
        }

        /// <summary>
        /// 清理动态创建的骨骼
        /// </summary>
        public void CleanupDynamicBones()
        {
            if (dynamicBonesParent != null)
            {
                DestroyImmediate(dynamicBonesParent.gameObject);
                dynamicBonesParent = null;
            }
            createdBones.Clear();

            if (debugMode)
            {
                Debug.Log("[VRM10ClothBinder] 已清理所有动态创建的骨骼");
            }
        }
    }
}
