# 🧪 测试系统文档

## 📋 **系统概述**

测试系统为VRM 1.0捏脸换装系统提供全面的自动化测试支持，包括单元测试、集成测试和编译验证。确保系统功能的正确性、稳定性和可靠性，支持持续集成和回归测试。

### 🏗️ **测试架构**
```
测试系统架构
├── 功能测试层
│   ├── VRM10FaceControllerTest - 面部控制器功能测试
│   └── FullSystemIntegrationTest - 完整系统集成测试
│
├── 编译验证层
│   └── CompilationTest - 编译完整性验证
│
└── 测试支持层
    ├── Unity Test Framework
    ├── NUnit Testing Framework
    └── Custom Test Utilities
```

---

## 🎯 **核心测试组件详解**

### 1. **VRM10FaceControllerTest.cs** - 面部控制器功能测试
**文件位置**: `Assets/Scripts/Testing/VRM10FaceControllerTest.cs`  
**脚本行数**: 135行  
**主要职责**: 验证VRM10FaceController的所有功能和API

#### **测试覆盖范围**
- 🎭 **表情设置测试** - 验证各种表情参数的设置和获取
- 📊 **参数范围测试** - 测试参数值的边界条件和异常值
- 🔄 **状态管理测试** - 测试初始化、重置和销毁流程
- ⚡ **性能测试** - 测试表情切换的性能表现
- 🛡️ **异常处理测试** - 验证错误输入的处理机制
- 🎨 **批量操作测试** - 测试多个表情的同时设置

#### **核心测试方法**
```csharp
[TestFixture]
public class VRM10FaceControllerTest
{
    private VRM10FaceController faceController;
    private Vrm10Instance mockVrmInstance;
    
    [SetUp]
    public void SetUp()
    {
        // 创建测试环境
        var testGameObject = new GameObject("TestVRM");
        mockVrmInstance = testGameObject.AddComponent<Vrm10Instance>();
        faceController = testGameObject.AddComponent<VRM10FaceController>();
        
        // 初始化面部控制器
        faceController.InitializeFaceController();
    }
    
    [TearDown]
    public void TearDown()
    {
        // 清理测试环境
        if (faceController != null)
        {
            Object.DestroyImmediate(faceController.gameObject);
        }
    }
    
    /// <summary>测试基础表情设置功能</summary>
    [Test]
    public void TestBasicExpressionSetting()
    {
        // 测试设置基础表情
        bool result = faceController.SetExpression("happy", 0.8f);
        Assert.IsTrue(result, "设置happy表情应该成功");
        
        // 验证表情值
        float actualValue = faceController.GetExpression("happy");
        Assert.AreEqual(0.8f, actualValue, 0.01f, "happy表情值应该为0.8");
    }
    
    /// <summary>测试表情参数边界值</summary>
    [Test]
    public void TestExpressionBoundaryValues()
    {
        // 测试最小值
        bool result1 = faceController.SetExpression("happy", 0.0f);
        Assert.IsTrue(result1, "设置最小值应该成功");
        Assert.AreEqual(0.0f, faceController.GetExpression("happy"), 0.01f);
        
        // 测试最大值
        bool result2 = faceController.SetExpression("happy", 1.0f);
        Assert.IsTrue(result2, "设置最大值应该成功");
        Assert.AreEqual(1.0f, faceController.GetExpression("happy"), 0.01f);
        
        // 测试超出范围的值
        bool result3 = faceController.SetExpression("happy", 1.5f);
        Assert.IsTrue(result3, "超出范围的值应该被自动限制");
        Assert.AreEqual(1.0f, faceController.GetExpression("happy"), 0.01f, "值应该被限制为1.0");
    }
    
    /// <summary>测试无效表情名称处理</summary>
    [Test]
    public void TestInvalidExpressionNames()
    {
        // 测试null名称
        bool result1 = faceController.SetExpression(null, 0.5f);
        Assert.IsFalse(result1, "null表情名称应该返回false");
        
        // 测试空字符串
        bool result2 = faceController.SetExpression("", 0.5f);
        Assert.IsFalse(result2, "空字符串表情名称应该返回false");
        
        // 测试不存在的表情
        bool result3 = faceController.SetExpression("nonexistent", 0.5f);
        Assert.IsFalse(result3, "不存在的表情应该返回false");
    }
    
    /// <summary>测试批量表情设置</summary>
    [Test]
    public void TestMultipleExpressionSetting()
    {
        var expressions = new Dictionary<string, float>
        {
            {"happy", 0.8f},
            {"sad", 0.3f},
            {"angry", 0.1f}
        };
        
        int successCount = faceController.SetMultipleExpressions(expressions);
        Assert.AreEqual(3, successCount, "应该成功设置3个表情");
        
        // 验证每个表情值
        Assert.AreEqual(0.8f, faceController.GetExpression("happy"), 0.01f);
        Assert.AreEqual(0.3f, faceController.GetExpression("sad"), 0.01f);
        Assert.AreEqual(0.1f, faceController.GetExpression("angry"), 0.01f);
    }
    
    /// <summary>测试表情重置功能</summary>
    [Test]
    public void TestExpressionReset()
    {
        // 设置一些表情
        faceController.SetExpression("happy", 0.8f);
        faceController.SetExpression("sad", 0.5f);
        
        // 重置所有表情
        faceController.ResetFacialParameters();
        
        // 验证所有表情都被重置为0
        Assert.AreEqual(0.0f, faceController.GetExpression("happy"), 0.01f);
        Assert.AreEqual(0.0f, faceController.GetExpression("sad"), 0.01f);
    }
    
    /// <summary>性能测试 - 大量表情设置</summary>
    [Test, Performance]
    public void TestExpressionSettingPerformance()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        // 执行1000次表情设置
        for (int i = 0; i < 1000; i++)
        {
            faceController.SetExpression("happy", Random.Range(0f, 1f));
        }
        
        stopwatch.Stop();
        
        // 验证性能 - 1000次操作应该在100ms内完成
        Assert.Less(stopwatch.ElapsedMilliseconds, 100, 
                   $"1000次表情设置耗时过长: {stopwatch.ElapsedMilliseconds}ms");
    }
}
```

#### **测试数据和Mock对象**
```csharp
/// <summary>测试数据提供器</summary>
public static class FaceControllerTestData
{
    /// <summary>标准表情名称列表</summary>
    public static readonly string[] StandardExpressions = 
    {
        "happy", "angry", "sad", "relaxed", "surprised",
        "blink", "blinkL", "blinkR", "lookUp", "lookDown",
        "lookLeft", "lookRight", "neutral"
    };
    
    /// <summary>无效表情名称列表</summary>
    public static readonly string[] InvalidExpressions = 
    {
        null, "", "   ", "invalid", "123", "test_expression"
    };
    
    /// <summary>边界测试值</summary>
    public static readonly float[] BoundaryValues = 
    {
        -1.0f, -0.1f, 0.0f, 0.5f, 1.0f, 1.1f, 2.0f, float.NaN, float.PositiveInfinity
    };
}

/// <summary>Mock VRM实例</summary>
public class MockVrm10Instance : Vrm10Instance
{
    public bool IsInitialized { get; set; } = true;
    public Dictionary<string, float> BlendShapeWeights { get; set; } = new Dictionary<string, float>();
    
    public override float GetBlendShapeWeight(string expressionName)
    {
        return BlendShapeWeights.TryGetValue(expressionName, out float weight) ? weight : 0f;
    }
    
    public override void SetBlendShapeWeight(string expressionName, float weight)
    {
        BlendShapeWeights[expressionName] = Mathf.Clamp01(weight);
    }
}
```

---

### 2. **CompilationTest.cs** - 编译完整性验证
**文件位置**: `Assets/Scripts/Testing/CompilationTest.cs`  
**脚本行数**: 61行  
**主要职责**: 验证所有脚本的编译完整性和类型正确性

#### **验证内容**
- 🔧 **类型检查** - 验证所有关键类型是否可以正确加载
- 📦 **程序集验证** - 检查程序集依赖关系
- 🎯 **接口完整性** - 验证接口实现的完整性
- 🔗 **引用有效性** - 检查外部引用的有效性
- ⚙️ **组件依赖** - 验证MonoBehaviour组件的依赖关系

#### **核心验证方法**
```csharp
[TestFixture]
public class CompilationTest
{
    /// <summary>验证所有核心类型可以正确加载</summary>
    [Test]
    public void TestCoreTypesCompilation()
    {
        var coreTypes = new System.Type[]
        {
            typeof(VRM10UnifiedManager),
            typeof(VRM10FaceController),
            typeof(VRM10ClothBinder),
            typeof(CharacterDataManager),
            typeof(VRMThirdPersonAdapter)
        };
        
        foreach (var type in coreTypes)
        {
            Assert.IsNotNull(type, $"核心类型 {type.Name} 应该可以正确加载");
            Assert.IsFalse(type.IsAbstract && !type.IsInterface, 
                          $"核心类型 {type.Name} 不应该是抽象类");
        }
    }
    
    /// <summary>验证所有数据结构类型</summary>
    [Test]
    public void TestDataStructureTypes()
    {
        var dataTypes = new System.Type[]
        {
            typeof(CharacterData),
            typeof(VRM10FacialParameters),
            typeof(VRM10ClothBoneData),
            typeof(VRMControllerSettings)
        };
        
        foreach (var type in dataTypes)
        {
            Assert.IsNotNull(type, $"数据类型 {type.Name} 应该可以正确加载");
            
            // 验证可序列化
            bool isSerializable = type.IsSerializable || 
                                 System.Attribute.IsDefined(type, typeof(System.SerializableAttribute));
            Assert.IsTrue(isSerializable, $"数据类型 {type.Name} 应该是可序列化的");
        }
    }
    
    /// <summary>验证枚举类型定义</summary>
    [Test]
    public void TestEnumTypes()
    {
        var enumTypes = new System.Type[]
        {
            typeof(VRM10ClothType),
            typeof(SystemState),
            typeof(StorageType)
        };
        
        foreach (var enumType in enumTypes)
        {
            Assert.IsNotNull(enumType, $"枚举类型 {enumType.Name} 应该可以正确加载");
            Assert.IsTrue(enumType.IsEnum, $"{enumType.Name} 应该是枚举类型");
            
            var enumValues = System.Enum.GetValues(enumType);
            Assert.Greater(enumValues.Length, 0, $"枚举 {enumType.Name} 应该有至少一个值");
        }
    }
    
    /// <summary>验证MonoBehaviour组件</summary>
    [Test]
    public void TestMonoBehaviourComponents()
    {
        var componentTypes = new System.Type[]
        {
            typeof(VRM10UnifiedManager),
            typeof(VRM10FaceController),
            typeof(VRM10ClothBinder),
            typeof(VRMThirdPersonAdapter),
            typeof(SceneTransitionManager)
        };
        
        foreach (var componentType in componentTypes)
        {
            Assert.IsTrue(typeof(MonoBehaviour).IsAssignableFrom(componentType),
                         $"{componentType.Name} 应该继承自MonoBehaviour");
            
            // 验证可以创建实例
            var gameObject = new GameObject("TestObject");
            var component = gameObject.AddComponent(componentType);
            Assert.IsNotNull(component, $"应该能够创建 {componentType.Name} 组件");
            
            Object.DestroyImmediate(gameObject);
        }
    }
    
    /// <summary>验证ScriptableObject类型</summary>
    [Test]
    public void TestScriptableObjectTypes()
    {
        var scriptableObjectTypes = new System.Type[]
        {
            typeof(VRMControllerSettings),
            typeof(VRM10FacialParameters)
        };
        
        foreach (var soType in scriptableObjectTypes)
        {
            if (typeof(ScriptableObject).IsAssignableFrom(soType))
            {
                var instance = ScriptableObject.CreateInstance(soType);
                Assert.IsNotNull(instance, $"应该能够创建 {soType.Name} 实例");
                Object.DestroyImmediate(instance);
            }
        }
    }
    
    /// <summary>验证外部依赖</summary>
    [Test]
    public void TestExternalDependencies()
    {
        var requiredTypes = new string[]
        {
            "UniVRM10.Vrm10Instance",
            "UnityEngine.UI.Button",
            "UnityEngine.UI.Slider",
            "Cinemachine.CinemachineVirtualCamera"
        };
        
        foreach (var typeName in requiredTypes)
        {
            var type = System.Type.GetType(typeName);
            if (type == null)
            {
                // 尝试在所有程序集中查找
                type = FindTypeInAssemblies(typeName);
            }
            
            Assert.IsNotNull(type, $"外部依赖类型 {typeName} 应该可用");
        }
    }
    
    /// <summary>在所有程序集中查找类型</summary>
    private System.Type FindTypeInAssemblies(string typeName)
    {
        foreach (var assembly in System.AppDomain.CurrentDomain.GetAssemblies())
        {
            var type = assembly.GetType(typeName);
            if (type != null) return type;
        }
        return null;
    }
}
```

---

### 3. **FullSystemIntegrationTest.cs** - 完整系统集成测试
**文件位置**: `Assets/Scripts/Testing/FullSystemIntegrationTest.cs`  
**脚本行数**: 84行  
**主要职责**: 验证各个系统模块之间的集成和协作

#### **集成测试范围**
- 🎭 **面部+换装集成** - 测试面部控制和换装系统的协作
- 💾 **数据管理集成** - 测试数据保存加载与核心系统的集成
- 🎮 **第三人称集成** - 测试VRM模型与第三人称控制的集成
- 🖥️ **UI系统集成** - 测试UI界面与后端系统的数据流
- 🔄 **场景切换集成** - 测试场景间的数据传递和状态保持

#### **核心集成测试**
```csharp
[TestFixture]
public class FullSystemIntegrationTest
{
    private GameObject testVrmObject;
    private VRM10UnifiedManager unifiedManager;
    private CharacterDataManager dataManager;
    
    [SetUp]
    public void SetUp()
    {
        // 创建完整的测试环境
        SetupTestEnvironment();
    }
    
    [TearDown]
    public void TearDown()
    {
        // 清理测试环境
        CleanupTestEnvironment();
    }
    
    /// <summary>测试VRM统一管理器的初始化</summary>
    [Test]
    public void TestUnifiedManagerInitialization()
    {
        Assert.IsNotNull(unifiedManager, "统一管理器应该存在");
        Assert.IsTrue(unifiedManager.IsSystemInitialized(), "系统应该已初始化");
        
        // 验证所有子系统都已正确初始化
        Assert.IsNotNull(unifiedManager.FaceController, "面部控制器应该已初始化");
        Assert.IsNotNull(unifiedManager.ClothBinder, "服装绑定器应该已初始化");
    }
    
    /// <summary>测试面部控制与数据管理的集成</summary>
    [Test]
    public void TestFaceControlAndDataIntegration()
    {
        // 设置面部表情
        unifiedManager.SetFaceExpression("happy", 0.8f);
        unifiedManager.SetFaceExpression("blink", 0.3f);
        
        // 获取当前角色数据
        var characterData = unifiedManager.GetCurrentCharacterData();
        Assert.IsNotNull(characterData.facialData, "面部数据应该存在");
        
        // 验证面部数据被正确保存
        var facialParams = characterData.facialData;
        var happyParam = System.Array.Find(facialParams.expressions, 
                                          e => e.name == "happy");
        Assert.IsNotNull(happyParam, "happy表情参数应该存在");
        Assert.AreEqual(0.8f, happyParam.currentWeight, 0.01f, "happy表情值应该正确");
    }
    
    /// <summary>测试换装与数据持久化的集成</summary>
    [Test]
    public async Task TestClothingAndPersistenceIntegration()
    {
        // 模拟穿戴服装
        var mockClothPrefab = CreateMockClothPrefab("TestShirt", VRM10ClothType.Top);
        bool wearResult = unifiedManager.WearCloth(mockClothPrefab);
        Assert.IsTrue(wearResult, "穿戴服装应该成功");
        
        // 保存角色数据
        var characterData = unifiedManager.GetCurrentCharacterData();
        bool saveResult = await dataManager.SaveCharacterAsync(characterData, "test_slot");
        Assert.IsTrue(saveResult, "保存角色数据应该成功");
        
        // 重置系统状态
        unifiedManager.RemoveAllClothes();
        Assert.AreEqual(0, unifiedManager.ClothBinder.GetWornClothes().Count, 
                       "服装应该已被移除");
        
        // 加载保存的数据
        var loadedData = await dataManager.LoadCharacterAsync("test_slot");
        Assert.IsTrue(loadedData.HasValue, "应该能够加载保存的数据");
        
        // 应用加载的数据
        bool loadResult = unifiedManager.LoadCharacterData(loadedData.Value);
        Assert.IsTrue(loadResult, "应用角色数据应该成功");
        
        // 验证服装状态已恢复
        var wornClothes = unifiedManager.ClothBinder.GetWornClothes();
        Assert.Greater(wornClothes.Count, 0, "服装状态应该已恢复");
    }
    
    /// <summary>测试系统状态的一致性</summary>
    [Test]
    public void TestSystemStateConsistency()
    {
        // 同时操作面部和服装
        unifiedManager.SetFaceExpression("happy", 0.7f);
        var mockCloth = CreateMockClothPrefab("TestPants", VRM10ClothType.Bottom);
        unifiedManager.WearCloth(mockCloth);
        
        // 获取系统状态
        var systemStatus = unifiedManager.GetSystemStatus();
        Assert.AreEqual(SystemState.Active, systemStatus.currentState, 
                       "系统状态应该是活跃的");
        
        // 验证各子系统状态一致
        Assert.IsTrue(unifiedManager.FaceController.IsInitialized(), 
                     "面部控制器应该已初始化");
        Assert.Greater(unifiedManager.ClothBinder.GetWornClothes().Count, 0, 
                      "应该有穿戴的服装");
    }
    
    /// <summary>测试错误恢复机制</summary>
    [Test]
    public void TestErrorRecoveryMechanism()
    {
        // 模拟错误情况 - 传入null数据
        bool result1 = unifiedManager.LoadCharacterData(new CharacterData());
        Assert.IsFalse(result1, "加载无效数据应该失败");
        
        // 验证系统仍然可用
        bool result2 = unifiedManager.SetFaceExpression("happy", 0.5f);
        Assert.IsTrue(result2, "系统应该仍然可用");
        
        // 模拟服装绑定失败
        var invalidCloth = new GameObject("InvalidCloth");
        bool result3 = unifiedManager.WearCloth(invalidCloth);
        Assert.IsFalse(result3, "无效服装应该绑定失败");
        
        // 验证系统状态正常
        var status = unifiedManager.GetSystemStatus();
        Assert.AreNotEqual(SystemState.Error, status.currentState, 
                          "系统不应该处于错误状态");
    }
    
    /// <summary>设置测试环境</summary>
    private void SetupTestEnvironment()
    {
        // 创建VRM测试对象
        testVrmObject = new GameObject("TestVRM");
        var mockVrmInstance = testVrmObject.AddComponent<MockVrm10Instance>();
        
        // 添加统一管理器
        unifiedManager = testVrmObject.AddComponent<VRM10UnifiedManager>();
        unifiedManager.InitializeSystem();
        
        // 获取数据管理器
        dataManager = CharacterDataManager.Instance;
    }
    
    /// <summary>清理测试环境</summary>
    private void CleanupTestEnvironment()
    {
        if (testVrmObject != null)
        {
            Object.DestroyImmediate(testVrmObject);
        }
        
        // 清理测试数据
        if (dataManager != null)
        {
            dataManager.DeleteCharacter("test_slot");
        }
    }
    
    /// <summary>创建模拟服装Prefab</summary>
    private GameObject CreateMockClothPrefab(string name, VRM10ClothType clothType)
    {
        var clothObject = new GameObject(name);
        var clothInfo = clothObject.AddComponent<VRM10ClothInfo>();
        clothInfo.clothType = clothType;
        clothInfo.clothName = name;
        
        var boneData = clothObject.AddComponent<VRM10ClothBoneData>();
        boneData.clothType = clothType;
        
        return clothObject;
    }
}
```

---

## 🚀 **测试执行和自动化**

### 测试运行配置
```csharp
/// <summary>测试配置类</summary>
[System.Serializable]
public class TestConfiguration
{
    [Header("测试设置")]
    public bool enablePerformanceTests = true;     // 启用性能测试
    public bool enableIntegrationTests = true;    // 启用集成测试
    public bool enableUITests = false;            // 启用UI测试 (需要特殊环境)
    public bool runInBatchMode = false;           // 批处理模式运行
    
    [Header("测试环境")]
    public string testDataPath = "Assets/TestData/";  // 测试数据路径
    public bool cleanupAfterTests = true;             // 测试后清理
    public float testTimeout = 30.0f;                 // 测试超时时间(秒)
    
    [Header("报告设置")]
    public bool generateTestReport = true;         // 生成测试报告
    public string reportOutputPath = "TestResults/"; // 报告输出路径
    public TestReportFormat reportFormat = TestReportFormat.XML; // 报告格式
}

public enum TestReportFormat
{
    XML,
    JSON,
    HTML
}
```

### 测试套件管理
```csharp
/// <summary>测试套件管理器</summary>
public static class TestSuiteManager
{
    /// <summary>运行所有测试</summary>
    public static TestResult RunAllTests()
    {
        var result = new TestResult();
        
        // 运行编译测试
        result.CompilationTests = RunTestSuite<CompilationTest>();
        
        // 运行功能测试
        result.FunctionTests = RunTestSuite<VRM10FaceControllerTest>();
        
        // 运行集成测试
        result.IntegrationTests = RunTestSuite<FullSystemIntegrationTest>();
        
        // 生成报告
        GenerateTestReport(result);
        
        return result;
    }
    
    /// <summary>运行特定测试套件</summary>
    public static TestSuiteResult RunTestSuite<T>() where T : class
    {
        var suite = System.Activator.CreateInstance<T>();
        var methods = typeof(T).GetMethods()
            .Where(m => m.GetCustomAttribute<TestAttribute>() != null);
        
        var result = new TestSuiteResult
        {
            SuiteName = typeof(T).Name,
            TestResults = new List<SingleTestResult>()
        };
        
        foreach (var method in methods)
        {
            var testResult = RunSingleTest(suite, method);
            result.TestResults.Add(testResult);
        }
        
        result.CalculateStatistics();
        return result;
    }
    
    /// <summary>生成测试报告</summary>
    private static void GenerateTestReport(TestResult result)
    {
        var report = new TestReport
        {
            Timestamp = System.DateTime.Now,
            TotalTests = result.GetTotalTestCount(),
            PassedTests = result.GetPassedTestCount(),
            FailedTests = result.GetFailedTestCount(),
            TestSuites = new List<TestSuiteResult>
            {
                result.CompilationTests,
                result.FunctionTests,
                result.IntegrationTests
            }
        };
        
        // 保存报告
        var reportJson = JsonUtility.ToJson(report, true);
        System.IO.File.WriteAllText("TestResults/latest_report.json", reportJson);
        
        Debug.Log($"测试完成: {report.PassedTests}/{report.TotalTests} 通过");
    }
}
```

---

## 📊 **持续集成支持**

### CI/CD配置
```yaml
# .github/workflows/unity-tests.yml
name: Unity Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - uses: game-ci/unity-test-runner@v2
      env:
        UNITY_LICENSE: ${{ secrets.UNITY_LICENSE }}
      with:
        testMode: all
        artifactsPath: test-results
        githubToken: ${{ secrets.GITHUB_TOKEN }}
        
    - uses: actions/upload-artifact@v2
      if: always()
      with:
        name: Test results
        path: test-results
```

### 自动化测试脚本
```csharp
/// <summary>CI/CD自动化测试执行器</summary>
public static class CITestRunner
{
    /// <summary>命令行测试执行入口</summary>
    public static void RunTestsFromCommandLine()
    {
        var args = System.Environment.GetCommandLineArgs();
        var config = ParseCommandLineArgs(args);
        
        Debug.Log("开始自动化测试执行...");
        
        try
        {
            var result = TestSuiteManager.RunAllTests();
            
            // 根据测试结果设置退出代码
            if (result.GetFailedTestCount() > 0)
            {
                Debug.LogError($"测试失败: {result.GetFailedTestCount()} 个测试未通过");
                EditorApplication.Exit(1);
            }
            else
            {
                Debug.Log("所有测试通过！");
                EditorApplication.Exit(0);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"测试执行异常: {ex.Message}");
            EditorApplication.Exit(2);
        }
    }
    
    /// <summary>解析命令行参数</summary>
    private static TestConfiguration ParseCommandLineArgs(string[] args)
    {
        var config = new TestConfiguration();
        
        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i])
            {
                case "-enablePerformanceTests":
                    config.enablePerformanceTests = true;
                    break;
                case "-batchmode":
                    config.runInBatchMode = true;
                    break;
                case "-testTimeout":
                    if (i + 1 < args.Length && float.TryParse(args[i + 1], out float timeout))
                    {
                        config.testTimeout = timeout;
                        i++;
                    }
                    break;
            }
        }
        
        return config;
    }
}
```

---

## 🔍 **测试数据和Mock对象**

### 测试数据管理
```csharp
/// <summary>测试数据工厂</summary>
public static class TestDataFactory
{
    /// <summary>创建测试用角色数据</summary>
    public static CharacterData CreateTestCharacterData()
    {
        return new CharacterData
        {
            characterName = "TestCharacter",
            description = "测试用角色数据",
            createdTime = System.DateTime.Now,
            version = "1.0.0",
            facialData = CreateTestFacialData(),
            clothingData = CreateTestClothingData()
        };
    }
    
    /// <summary>创建测试用面部数据</summary>
    public static VRM10FacialParameters CreateTestFacialData()
    {
        var facialData = ScriptableObject.CreateInstance<VRM10FacialParameters>();
        facialData.expressions = new List<VRM10ExpressionParameter>
        {
            new VRM10ExpressionParameter { name = "happy", currentWeight = 0.8f },
            new VRM10ExpressionParameter { name = "sad", currentWeight = 0.2f },
            new VRM10ExpressionParameter { name = "blink", currentWeight = 0.5f }
        };
        return facialData;
    }
    
    /// <summary>创建测试用服装数据</summary>
    public static ClothingData CreateTestClothingData()
    {
        return new ClothingData
        {
            currentOutfit = new Dictionary<string, string>
            {
                { "Top", "TestShirt" },
                { "Bottom", "TestPants" }
            },
            ownedClothes = new List<string> { "TestShirt", "TestPants", "TestShoes" },
            clothVisibility = new Dictionary<string, bool>
            {
                { "TestShirt", true },
                { "TestPants", true }
            }
        };
    }
}

/// <summary>Mock对象工厂</summary>
public static class MockObjectFactory
{
    /// <summary>创建Mock VRM实例</summary>
    public static MockVrm10Instance CreateMockVrmInstance()
    {
        var gameObject = new GameObject("MockVRM");
        var mockVrm = gameObject.AddComponent<MockVrm10Instance>();
        mockVrm.IsInitialized = true;
        return mockVrm;
    }
    
    /// <summary>创建Mock服装对象</summary>
    public static GameObject CreateMockClothObject(string name, VRM10ClothType clothType)
    {
        var clothObject = new GameObject(name);
        
        // 添加服装信息组件
        var clothInfo = clothObject.AddComponent<VRM10ClothInfo>();
        clothInfo.clothName = name;
        clothInfo.clothType = clothType;
        
        // 添加骨骼数据组件
        var boneData = clothObject.AddComponent<VRM10ClothBoneData>();
        boneData.clothType = clothType;
        boneData.boneBindings = CreateMockBoneBindings();
        
        return clothObject;
    }
    
    /// <summary>创建Mock骨骼绑定数据</summary>
    private static List<BoneBindingInfo> CreateMockBoneBindings()
    {
        return new List<BoneBindingInfo>
        {
            new BoneBindingInfo
            {
                boneName = "Hips",
                bindingWeight = 1.0f,
                isRequired = true
            },
            new BoneBindingInfo
            {
                boneName = "Spine",
                bindingWeight = 0.8f,
                isRequired = false
            }
        };
    }
}
```

---

## ⚠️ **测试最佳实践**

### 测试编写指南
1. **测试命名**: 使用描述性的测试方法名称
2. **测试独立性**: 每个测试应该独立运行，不依赖其他测试
3. **测试数据**: 使用工厂模式创建测试数据
4. **断言清晰**: 使用有意义的断言消息
5. **性能考虑**: 避免在测试中进行大量计算

### 测试组织结构
```
Tests/
├── Unit/ (单元测试)
│   ├── VRM10FaceControllerTest.cs
│   └── CharacterDataManagerTest.cs
├── Integration/ (集成测试)
│   └── FullSystemIntegrationTest.cs
├── Performance/ (性能测试)
│   └── SystemPerformanceTest.cs
└── Utilities/ (测试工具)
    ├── TestDataFactory.cs
    └── MockObjectFactory.cs
```

### 测试维护
1. **定期运行**: 每次代码提交前运行完整测试套件
2. **持续更新**: 新功能开发时同步更新测试
3. **测试覆盖率**: 维持良好的代码覆盖率
4. **性能监控**: 定期检查测试执行时间

---

## 📚 **相关文档**
- [VRoid核心系统文档](01_VRoid核心系统文档.md) - 了解被测试的核心功能
- [数据管理系统文档](02_数据管理系统文档.md) - 数据相关测试参考
- [API参考文档](07_API参考文档.md) - 测试API使用参考
- [FAQ问题解答](08_FAQ问题解答.md) - 测试常见问题

---

**版本**: 1.0.0  
**最后更新**: 2025-01-22  
**测试脚本数量**: 3个核心测试脚本  
**维护者**: 质量保证团队 