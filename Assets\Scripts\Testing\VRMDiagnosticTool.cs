using UnityEngine;
using UnityEditor;
using UniVRM10;
using System.IO;
using System.Collections.Generic;
using VRoidFaceCustomization.Data;
using VRoidFaceCustomization.UI;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM诊断工具 - 帮助识别和解决VRM适配系统的常见问题
    /// </summary>
    public class VRMDiagnosticTool : MonoBehaviour
    {
        [Header("诊断设置")]
        [SerializeField] private bool autoRunOnStart = true;
        [SerializeField] private bool detailedLogging = true;
        
        [Header("诊断结果")]
        [SerializeField] private int foundVRMFiles = 0;
        [SerializeField] private int foundVRMInstances = 0;
        [SerializeField] private int missingScripts = 0;
        [SerializeField] private bool hasVRMStateManager = false;
        [SerializeField] private bool hasVRMRuntimeLoader = false;
        
        private void Start()
        {
            if (autoRunOnStart)
            {
                RunDiagnostics();
            }
        }
        
        /// <summary>
        /// 运行完整诊断
        /// </summary>
        [ContextMenu("运行VRM诊断")]
        public void RunDiagnostics()
        {
            LogDebug("🔧 开始VRM系统诊断...");
            LogDebug("==============================");
            
            // 1. 检查VRM文件
            CheckVRMFiles();
            
            // 2. 检查场景中的VRM实例
            CheckVRMInstances();
            
            // 3. 检查管理器组件
            CheckManagerComponents();
            
            // 4. 检查脚本完整性
            CheckScriptIntegrity();
            
            // 5. 输出诊断总结
            OutputDiagnosticSummary();
            
            LogDebug("==============================");
            LogDebug("🎯 VRM系统诊断完成！");
        }
        
        /// <summary>
        /// 检查VRM文件
        /// </summary>
        private void CheckVRMFiles()
        {
            LogDebug("📁 [诊断] 检查VRM文件...");
            
            try
            {
                string[] vrmFiles = Directory.GetFiles("Assets", "*.vrm", System.IO.SearchOption.AllDirectories);
                foundVRMFiles = vrmFiles.Length;
                
                if (vrmFiles.Length > 0)
                {
                    LogDebug($"✅ 找到 {vrmFiles.Length} 个VRM文件:");
                    foreach (var file in vrmFiles)
                    {
                        LogDebug($"   📄 {file}");
                        
                        // 检查文件大小
                        var fileInfo = new FileInfo(file);
                        LogDebug($"      大小: {fileInfo.Length / 1024 / 1024}MB");
                    }
                }
                else
                {
                    LogDebug("❌ 未找到任何VRM文件");
                    LogDebug("💡 解决方案: 请将VRM文件导入到Assets文件夹中");
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 检查VRM文件时出错: {e.Message}");
            }
        }
        
        /// <summary>
        /// 检查场景中的VRM实例
        /// </summary>
        private void CheckVRMInstances()
        {
            LogDebug("🎭 [诊断] 检查场景中的VRM实例...");
            
            var vrmInstances = FindObjectsOfType<Vrm10Instance>();
            foundVRMInstances = vrmInstances.Length;
            
            if (vrmInstances.Length > 0)
            {
                LogDebug($"✅ 找到 {vrmInstances.Length} 个VRM实例:");
                foreach (var instance in vrmInstances)
                {
                    LogDebug($"   🎭 {instance.name}");
                    LogDebug($"      活跃状态: {instance.gameObject.activeInHierarchy}");
                    LogDebug($"      位置: {instance.transform.position}");
                    
                    // 检查VRM组件完整性
                    var skinnedRenderers = instance.GetComponentsInChildren<SkinnedMeshRenderer>();
                    var meshRenderers = instance.GetComponentsInChildren<MeshRenderer>();
                    LogDebug($"      SkinnedMeshRenderer: {skinnedRenderers.Length}");
                    LogDebug($"      MeshRenderer: {meshRenderers.Length}");
                    
                    // 检查BlendShape
                    int totalBlendShapes = 0;
                    foreach (var renderer in skinnedRenderers)
                    {
                        if (renderer.sharedMesh != null)
                        {
                            totalBlendShapes += renderer.sharedMesh.blendShapeCount;
                        }
                    }
                    LogDebug($"      BlendShape总数: {totalBlendShapes}");
                }
            }
            else
            {
                LogDebug("❌ 场景中没有VRM实例");
                LogDebug("💡 解决方案: 请确保VRM模型已正确加载到场景中");
            }
        }
        
        /// <summary>
        /// 检查管理器组件
        /// </summary>
        private void CheckManagerComponents()
        {
            LogDebug("🔧 [诊断] 检查管理器组件...");
            
            // 检查VRMStateManager
            var vrmStateManager = FindObjectOfType<VRMStateManager>();
            hasVRMStateManager = vrmStateManager != null;
            LogDebug($"VRMStateManager: {(hasVRMStateManager ? "✅ 存在" : "❌ 缺失")}");
            
            // 检查VRMRuntimeLoader
            var vrmRuntimeLoader = FindObjectOfType<VRMRuntimeLoader>();
            hasVRMRuntimeLoader = vrmRuntimeLoader != null;
            LogDebug($"VRMRuntimeLoader: {(hasVRMRuntimeLoader ? "✅ 存在" : "❌ 缺失")}");
            
            // 检查VRM10UnifiedManager
            var unifiedManager = FindObjectOfType<VRM10UnifiedManager>();
            LogDebug($"VRM10UnifiedManager: {(unifiedManager != null ? "✅ 存在" : "❌ 缺失")}");
            
            // 检查CharacterDataManager
            var dataManager = FindObjectOfType<CharacterDataManager>();
            LogDebug($"CharacterDataManager: {(dataManager != null ? "✅ 存在" : "❌ 缺失")}");
            
            // 检查SceneTransitionManager
            var sceneTransitionManager = FindObjectOfType<SceneTransitionManager>();
            LogDebug($"SceneTransitionManager: {(sceneTransitionManager != null ? "✅ 存在" : "❌ 缺失")}");
        }
        
        /// <summary>
        /// 检查脚本完整性
        /// </summary>
        private void CheckScriptIntegrity()
        {
            LogDebug("📜 [诊断] 检查脚本完整性...");
            
            var allObjects = FindObjectsOfType<MonoBehaviour>();
            missingScripts = 0;
            
            foreach (var obj in allObjects)
            {
                if (obj == null)
                {
                    missingScripts++;
                }
            }
            
            if (missingScripts > 0)
            {
                LogDebug($"❌ 发现 {missingScripts} 个丢失的脚本引用");
                LogDebug("💡 解决方案: 请检查Console中的'Missing Script'错误并重新分配脚本");
            }
            else
            {
                LogDebug("✅ 未发现丢失的脚本引用");
            }
        }
        
        /// <summary>
        /// 输出诊断总结
        /// </summary>
        private void OutputDiagnosticSummary()
        {
            LogDebug("📊 [诊断总结]");
            LogDebug($"📁 VRM文件: {foundVRMFiles} 个");
            LogDebug($"🎭 VRM实例: {foundVRMInstances} 个");
            LogDebug($"📜 缺失脚本: {missingScripts} 个");
            LogDebug($"🔧 VRMStateManager: {(hasVRMStateManager ? "✅" : "❌")}");
            LogDebug($"🔧 VRMRuntimeLoader: {(hasVRMRuntimeLoader ? "✅" : "❌")}");
            
            // 给出建议
            if (foundVRMFiles == 0)
            {
                LogDebug("🚨 关键问题: 没有VRM文件！");
                LogDebug("💡 请将VRM文件拖拽到Assets文件夹中");
            }
            else if (foundVRMInstances == 0)
            {
                LogDebug("🚨 关键问题: 场景中没有VRM模型！");
                LogDebug("💡 请在场景中加载VRM模型");
            }
            else if (!hasVRMStateManager || !hasVRMRuntimeLoader)
            {
                LogDebug("⚠️ 警告: 缺少必要的管理器组件！");
                LogDebug("💡 请确保VRMAdaptationTester已添加到场景中");
            }
            else if (missingScripts > 0)
            {
                LogDebug("⚠️ 警告: 存在脚本引用丢失！");
                LogDebug("💡 请修复Console中的Missing Script错误");
            }
            else
            {
                LogDebug("🎉 系统状态良好，可以进行测试！");
            }
        }
        
        /// <summary>
        /// 自动修复常见问题
        /// </summary>
        [ContextMenu("自动修复常见问题")]
        public void AutoFixCommonIssues()
        {
            LogDebug("🔧 开始自动修复...");
            
            // 检查并添加缺失的管理器
            if (!hasVRMStateManager)
            {
                var go = new GameObject("VRMStateManager");
                go.AddComponent<VRMStateManager>();
                LogDebug("✅ 已添加VRMStateManager");
            }
            
            if (!hasVRMRuntimeLoader)
            {
                var go = new GameObject("VRMRuntimeLoader");
                go.AddComponent<VRMRuntimeLoader>();
                LogDebug("✅ 已添加VRMRuntimeLoader");
            }
            
            LogDebug("🎯 自动修复完成！");
        }
        
        private void LogDebug(string message)
        {
            if (detailedLogging)
            {
                Debug.Log($"[VRMDiagnostic] {message}");
            }
        }
    }
} 