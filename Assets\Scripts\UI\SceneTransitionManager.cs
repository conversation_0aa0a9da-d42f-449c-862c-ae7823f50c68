using UnityEngine;
using UnityEngine.SceneManagement;
using System.Threading.Tasks;
using UniVRM10;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization.UI
{
    /// <summary>
    /// 场景切换管理器 - 处理场景间的数据传递和切换
    /// </summary>
    public class SceneTransitionManager : MonoBehaviour
    {
        [Header("场景切换设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool autoSaveVRMData = true;
        [SerializeField] private bool showLoadingProgress = true;
        
        [Header("目标场景")]
        [SerializeField] private string thirdPersonSceneName = "ThirdPersonTestScene";
        [SerializeField] private string avatarRuntimeSceneName = "AvatarRuntime";
        
        // 事件系统
        public System.Action<string> OnSceneTransitionStart;
        public System.Action<string> OnSceneTransitionComplete;
        public System.Action<float> OnLoadingProgress;
        public System.Action<string> OnTransitionError;
        
        // 内部状态
        private bool isTransitioning = false;
        
        /// <summary>
        /// 切换到指定场景
        /// </summary>
        public async Task<bool> TransitionToScene(string sceneName, bool preserveVRMData = true)
        {
            if (isTransitioning)
            {
                LogDebug("⏳ 场景切换正在进行中，请稍候...");
                return false;
            }
            
            isTransitioning = true;
            
            try
            {
                LogDebug($"🔄 开始切换到场景: {sceneName}");
                OnSceneTransitionStart?.Invoke(sceneName);
                
                // 如果需要保存VRM数据
                if (preserveVRMData && autoSaveVRMData)
                {
                    await SaveCurrentVRMData();
                }
                
                // 异步加载场景
                var loadOperation = SceneManager.LoadSceneAsync(sceneName);
                if (loadOperation == null)
                {
                    LogError($"❌ 无法加载场景: {sceneName}");
                    return false;
                }
                
                // 监控加载进度
                while (!loadOperation.isDone)
                {
                    float progress = loadOperation.progress;
                    if (showLoadingProgress)
                    {
                        OnLoadingProgress?.Invoke(progress);
                    }
                    
                    await Task.Yield();
                }
                
                LogDebug($"✅ 场景切换完成: {sceneName}");
                OnSceneTransitionComplete?.Invoke(sceneName);
                return true;
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 场景切换时发生错误: {ex.Message}");
                OnTransitionError?.Invoke(ex.Message);
                return false;
            }
            finally
            {
                isTransitioning = false;
            }
        }
        
        /// <summary>
        /// 切换到第三人称场景
        /// </summary>
        public async Task<bool> TransitionToThirdPersonScene()
        {
            return await TransitionToScene(thirdPersonSceneName, true);
        }
        
        /// <summary>
        /// 切换到Avatar Runtime场景
        /// </summary>
        public async Task<bool> TransitionToAvatarRuntimeScene()
        {
            return await TransitionToScene(avatarRuntimeSceneName, true);
        }
        
        /// <summary>
        /// 携带角色数据切换场景
        /// </summary>
        public async Task<bool> TransitionWithData(string sceneName, GameObject vrmObject)
        {
            if (vrmObject == null)
            {
                LogError("❌ VRM对象为空，无法携带数据切换");
                return false;
            }
            
            try
            {
                LogDebug($"📦 携带VRM数据切换到场景: {sceneName}");
                
                // 保存VRM数据和状态
                var stateManager = VRMStateManager.Instance;
                var runtimeLoader = VRMRuntimeLoader.Instance;
                
                if (stateManager != null)
                {
                    stateManager.CaptureVRMRenderState(vrmObject);
                }
                
                // 如果有VRM文件路径信息，保存文件数据
                // 注意：这里需要VRM对象有文件路径信息
                // 实际实现可能需要根据项目具体情况调整
                
                // 执行场景切换
                return await TransitionToScene(sceneName, true);
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 携带数据切换场景时发生错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 保存当前VRM数据
        /// </summary>
        private async Task SaveCurrentVRMData()
        {
            try
            {
                LogDebug("💾 保存当前VRM数据...");
                
                var vrm10Instance = FindObjectOfType<Vrm10Instance>();
                if (vrm10Instance == null)
                {
                    LogDebug("⚠️ 当前场景中没有VRM对象，跳过数据保存");
                    return;
                }
                
                // 保存VRM渲染状态
                var stateManager = VRMStateManager.Instance;
                if (stateManager != null)
                {
                    stateManager.CaptureVRMRenderState(vrm10Instance.gameObject);
                    LogDebug("✅ VRM渲染状态已保存");
                }
                
                await Task.Yield();
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 保存VRM数据时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查场景是否存在
        /// </summary>
        public bool IsSceneAvailable(string sceneName)
        {
            for (int i = 0; i < SceneManager.sceneCountInBuildSettings; i++)
            {
                string scenePath = SceneUtility.GetScenePathByBuildIndex(i);
                string sceneNameFromPath = System.IO.Path.GetFileNameWithoutExtension(scenePath);
                if (sceneNameFromPath == sceneName)
                {
                    return true;
                }
            }
            return false;
        }
        
        /// <summary>
        /// 获取当前场景名称
        /// </summary>
        public string GetCurrentSceneName()
        {
            return SceneManager.GetActiveScene().name;
        }
        
        /// <summary>
        /// 检查是否正在切换场景
        /// </summary>
        public bool IsTransitioning()
        {
            return isTransitioning;
        }
        
        /// <summary>
        /// 获取场景切换状态信息
        /// </summary>
        public string GetStatusInfo()
        {
            if (isTransitioning)
                return "正在切换场景...";
            
            string currentScene = GetCurrentSceneName();
            bool hasVRMData = VRMStateManager.Instance?.HasSavedRenderState() ?? false;
            
            return $"当前场景: {currentScene}, VRM数据: {(hasVRMData ? "有" : "无")}";
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[SceneTransitionManager] {message}");
            }
        }
        
        private void LogError(string message)
        {
            Debug.LogError($"[SceneTransitionManager] {message}");
        }
        
        #region Unity编辑器支持
        
#if UNITY_EDITOR
        [ContextMenu("测试切换到第三人称场景")]
        private void TestTransitionToThirdPerson()
        {
            if (Application.isPlaying)
            {
                _ = TransitionToThirdPersonScene();
            }
            else
            {
                Debug.Log("请在运行时测试场景切换");
            }
        }
        
        [ContextMenu("检查场景可用性")]
        private void CheckSceneAvailability()
        {
            Debug.Log($"场景可用性检查:");
            Debug.Log($"  第三人称场景 ({thirdPersonSceneName}): {IsSceneAvailable(thirdPersonSceneName)}");
            Debug.Log($"  Avatar Runtime场景 ({avatarRuntimeSceneName}): {IsSceneAvailable(avatarRuntimeSceneName)}");
            Debug.Log($"  当前场景: {GetCurrentSceneName()}");
        }
#endif
        
        #endregion
    }
}
