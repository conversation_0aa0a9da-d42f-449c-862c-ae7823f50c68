%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.18028352, g: 0.22571376, b: 0.30692244, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &488833692 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 1946061980}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1036041765
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1036041767}
  - component: {fileID: 1036041766}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1036041766
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036041765}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &1036041767
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036041765}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821794, y: -0.23456973, z: 0.10938166, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &1521057085
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1521057088}
  - component: {fileID: 1521057087}
  - component: {fileID: 1521057086}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1521057086
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1521057085}
  m_Enabled: 1
--- !u!20 &1521057087
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1521057085}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1521057088
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1521057085}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1687346128
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1687346132}
  - component: {fileID: 1687346131}
  - component: {fileID: 1687346130}
  - component: {fileID: 1687346129}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &1687346129
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1687346128}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37ad00496249b8b4dbf377199d92907d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &1687346130
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1687346128}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!33 &1687346131
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1687346128}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1687346132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1687346128}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 10, y: 1, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1946061980
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6996810063856314682}
    m_Modifications:
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 0.7917716
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: -0.0050140023
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.69447684
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.7917717
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.13295257
      objectReference: {fileID: 0}
    - target: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Name
      value: LoadedCharacter
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.x
      value: 0.00012496486
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 1.4568677
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: -0.03516958
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.123292
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.13479024
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.13434336
      objectReference: {fileID: 0}
    - target: {fileID: 4870552773557632513, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4870552773557632513, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Controller
      value: 
      objectReference: {fileID: 9100000, guid: 40db3173a05ae3242b1c182a09b0a183, type: 2}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.x
      value: 0.000000044703484
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 1.4541492
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: 0.016835578
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.10882426
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.11667144
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.0724268
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1946061987}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1946061986}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1946061985}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1946061984}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1946061983}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1946061982}
  m_SourcePrefab: {fileID: -3956505258223990651, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
--- !u!4 &1946061981 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 1946061980}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1946061982
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 488833692}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90afcea9d92a25640ae8da0481cbec01, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1946061983
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 488833692}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b4fecd0777d4394ca1504bf94b5fbff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  faceController: {fileID: 0}
--- !u!114 &1946061984
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 488833692}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97ddb992f53b8cd49b513c1e2627e654, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  vrmInstance: {fileID: 0}
  faceController: {fileID: 0}
  clothBinder: {fileID: 0}
  autoInitialize: 1
  debugMode: 1
  availableClothes: []
  outfitPresets: []
  clothLibraryPath: Assets/ExtractedClothes/
  autoScanOnStart: 0
  useFilePrefix: 1
  topPrefix: Top_
  bottomPrefix: Bottom_
  dressPrefix: Dress_
  shoesPrefix: Shoes_
  accessoryPrefix: Acc_
  hatPrefix: Hat_
  glovesPrefix: Gloves_
  socksPrefix: Socks_
--- !u!114 &1946061985
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 488833692}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 532e33e5b5ded134796d5e461e49d720, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debugMode: 1
  autoUpdateBounds: 1
  preserveSpringBones: 1
  strictBoneMapping: 0
  enableVRoidMode: 1
  createMissingBones: 1
  usePhysicsBonesAsStatic: 1
  minimumSuccessRate: 0.6
  delayedBoneBinding: 0
  bindingDelay: 0.1
  forceSyncBinding: 1
  preInitializeBones: 1
  stabilizationFrames: 3
  vrmInstance: {fileID: 0}
  avatarRoot: {fileID: 0}
  wornClothes: []
--- !u!114 &1946061986
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 488833692}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f7d2adfd955af7145b342c75b97ab63a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  vrmInstance: {fileID: 0}
  facialParameters:
    standardExpressions: []
    customExpressions: []
    blendShapeParameters: []
    eyeParameters: []
    mouthParameters: []
    browParameters: []
    noseParameters: []
    cheekParameters: []
    faceShapeParameters: []
    expressionParameters: []
    otherParameters: []
  autoInitializeOnStart: 1
  enableInspectorRealtime: 1
  debugMode: 0
  showDetailedLogs: 0
--- !u!114 &1946061987
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 488833692}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b93d6bc3686832342b1220f68b4da34e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  analyzeOnStart: 0
  showDetailedInfo: 1
  exportToFile: 0
  analysisResult:
    modelName: 
    modelTitle: 
    modelAuthor: 
    vrmVersion: 
    vrmInstanceName: 
    hasVrmInstance: 0
    hasVrmExpression: 0
    hasVrmRuntime: 0
    standardExpressions: []
    customExpressions: []
    blendShapes: []
--- !u!114 &482472688569489776
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7661318966341071744}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 909d917d73a63f940ac158d02e936645, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  pushLayers:
    serializedVersion: 2
    m_Bits: 0
  canPush: 0
  strength: 1.1
--- !u!95 &4033401701199606592
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7661318966341071744}
  m_Enabled: 1
  m_Avatar: {fileID: -7015762578403652589, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_Controller: {fileID: 9100000, guid: 40db3173a05ae3242b1c182a09b0a183, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &6267926764193722364
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7661318966341071744}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e087ecce43ebbff45a1b360637807d93, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  move: {x: 0, y: 0}
  look: {x: 0, y: 0}
  jump: 0
  sprint: 0
  analogMovement: 0
  cursorLocked: 1
  cursorInputForLook: 1
--- !u!4 &6996810063856314682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7661318966341071744}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1946061981}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7661318966341071744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6996810063856314682}
  - component: {fileID: 4033401701199606592}
  - component: {fileID: 7661318966341071754}
  - component: {fileID: 7661318966341071755}
  - component: {fileID: 482472688569489776}
  - component: {fileID: 6267926764193722364}
  - component: {fileID: 7661318966341071756}
  m_Layer: 8
  m_Name: ThirdController
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!143 &7661318966341071754
CharacterController:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7661318966341071744}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Height: 1.8
  m_Radius: 0.28
  m_SlopeLimit: 45
  m_StepOffset: 0.25
  m_SkinWidth: 0.02
  m_MinMoveDistance: 0
  m_Center: {x: 0, y: 0.93, z: 0}
--- !u!114 &7661318966341071755
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7661318966341071744}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 26e54e5a728a9234ab24fcf1460ed8a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MoveSpeed: 2
  SprintSpeed: 5.335
  RotationSmoothTime: 0.12
  SpeedChangeRate: 10
  LandingAudioClip: {fileID: 8300000, guid: ff697d3070687ce4583faa0561a145a2, type: 3}
  FootstepAudioClips:
  - {fileID: 8300000, guid: 72f526a6a9890f643a88e85a61c86c8a, type: 3}
  - {fileID: 8300000, guid: 85016e0f2b01da248b9663dd49a161b0, type: 3}
  - {fileID: 8300000, guid: 186de84b3207156479abe98f4958fed0, type: 3}
  - {fileID: 8300000, guid: 1a91fcd19acf1e54bba0945d9f390849, type: 3}
  - {fileID: 8300000, guid: 14e8a8d2158bec840b56c54f5266e692, type: 3}
  - {fileID: 8300000, guid: 29841e7d5bbfb5b419c9ad16ca8bc4c1, type: 3}
  - {fileID: 8300000, guid: dd1af302b8902684d9381de1f2d3a5af, type: 3}
  - {fileID: 8300000, guid: 67c8b33e424ccdc4486edf538ab91c5a, type: 3}
  - {fileID: 8300000, guid: 274649b0e221539409070ebf6c18918b, type: 3}
  - {fileID: 8300000, guid: a3194b8bbc96ef84fab1f98f4b7dae3e, type: 3}
  FootstepAudioVolume: 0.3
  JumpHeight: 1.2
  Gravity: -15
  JumpTimeout: 0.3
  FallTimeout: 0.15
  Grounded: 1
  GroundedOffset: -0.14
  GroundedRadius: 0.28
  GroundLayers:
    serializedVersion: 2
    m_Bits: 1
  CinemachineCameraTarget: {fileID: 0}
  TopClamp: 70
  BottomClamp: -30
  CameraAngleOverride: 0
  LockCameraPosition: 0
--- !u!114 &7661318966341071756
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7661318966341071744}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62899f850307741f2a39c98a8b639597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Actions: {fileID: -944628639613478452, guid: 4419d82f33d36e848b3ed5af4c8da37e, type: 3}
  m_NotificationBehavior: 0
  m_UIInputModule: {fileID: 0}
  m_DeviceLostEvent:
    m_PersistentCalls:
      m_Calls: []
  m_DeviceRegainedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ControlsChangedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ActionEvents:
  - m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7661318966341071755}
        m_TargetAssemblyTypeName: 
        m_MethodName: InputMove
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
    m_ActionId: 6bc1aaf4-b110-4ff7-891e-5b9fe6f32c4d
    m_ActionName: Player/Move[/Keyboard/w,/Keyboard/s,/Keyboard/a,/Keyboard/d,/Keyboard/upArrow,/Keyboard/downArrow,/Keyboard/leftArrow,/Keyboard/rightArrow,/XInputControllerWindows/leftStick]
  - m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7661318966341071755}
        m_TargetAssemblyTypeName: 
        m_MethodName: InputLook
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
    m_ActionId: 2690c379-f54d-45be-a724-414123833eb4
    m_ActionName: Player/Look[/Mouse/delta,/XInputControllerWindows/rightStick]
  - m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7661318966341071755}
        m_TargetAssemblyTypeName: 
        m_MethodName: InputJump
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
    m_ActionId: 8c4abdf8-4099-493a-aa1a-129acec7c3df
    m_ActionName: Player/Jump[/Keyboard/space,/XInputControllerWindows/buttonSouth]
  - m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7661318966341071755}
        m_TargetAssemblyTypeName: 
        m_MethodName: InputSprint
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
    m_ActionId: 980e881e-182c-404c-8cbf-3d09fdb48fef
    m_ActionName: Player/Sprint[/Keyboard/leftShift,/XInputControllerWindows/leftTrigger]
  - m_PersistentCalls:
      m_Calls: []
    m_ActionId: e4ce1614-c754-48c1-9103-33130441661f
    m_ActionName: UI/New action
  m_NeverAutoSwitchControlSchemes: 0
  m_DefaultControlScheme: 
  m_DefaultActionMap: Player
  m_SplitScreenIndex: -1
  m_Camera: {fileID: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 1521057088}
  - {fileID: 1036041767}
  - {fileID: 1687346132}
  - {fileID: 6996810063856314682}
