# 🛠️ 编辑器工具系统文档

## 📋 **系统概述**

编辑器工具系统是专为Unity编辑器设计的扩展工具集，包含18个专业化脚本，涵盖服装提取、系统设置、骨骼分析、调试诊断等功能。这些工具大大简化了VRM 1.0模型的处理流程，提高开发效率。

### 🏗️ **工具分类架构**
```
编辑器工具系统
├── 核心设置工具 (6个)
│   ├── VRM10SystemSetup - 系统一键设置
│   ├── VRM10FaceSetupTool - 面部系统设置
│   ├── VRM10FaceControllerEditor - 面部控制编辑器
│   ├── VRM10UnifiedManagerEditor - 统一管理器编辑器
│   ├── VRM10DualModeClothEditor - 双模式服装编辑器
│   └── ThirdPersonSceneRecoveryTool - 场景恢复工具
│
├── 服装处理工具 (4个)
│   ├── VRM10ClothExtractor - 服装提取器 (核心)
│   ├── VRM10ClothDebugger - 服装调试器
│   ├── VRM10ClothBindingDiagnostic - 绑定诊断
│   └── VRoidClothProcessor - VRoid服装处理器
│
├── 骨骼分析工具 (6个)
│   ├── VRM10BoneExtractor - 骨骼提取器
│   ├── VRM10BoneAutoFixer - 骨骼自动修复
│   ├── VRM10BoneStateRecorder - 骨骼状态记录
│   ├── VRM10BoneCoordinateAnalyzer - 坐标分析器
│   └── VRoidBoneAnalyzer - VRoid骨骼分析
│
└── 调试辅助工具 (2个)
    ├── ConsoleOutputSimplifier - 控制台简化器
    ├── QuickConsoleFilter - 控制台过滤器
    └── VRM10LogExtractor - 日志提取器
```

---

## 🎯 **核心设置工具**

### 1. **VRM10SystemSetup.cs** - 系统一键设置工具
**文件位置**: `Assets/Scripts/Editor/VRM10SystemSetup.cs`  
**脚本行数**: 288行  
**菜单位置**: `Tools → VRM 1.0 → System Setup`

#### **主要功能**
- 🚀 **一键系统设置** - 自动为VRM模型添加所有必需组件
- 🔍 **组件检测** - 自动检测现有组件状态
- 📊 **系统状态显示** - 直观显示系统配置状态
- 🧹 **批量组件清理** - 移除无用或冲突的组件
- ℹ️ **VRM信息显示** - 显示模型的详细信息

#### **使用方法**
```csharp
// 在编辑器中使用菜单
Tools → VRM 1.0 → System Setup

// 或者通过代码调用
VRM10SystemSetup.SetupVRM10System(selectedVrmModel);
```

#### **功能详解**
```csharp
public class VRM10SystemSetup : EditorWindow
{
    // 核心设置方法
    public static void SetupVRM10System(GameObject vrmModel)
    {
        // 1. 验证VRM模型
        if (!ValidateVRMModel(vrmModel)) return;
        
        // 2. 添加VRM10UnifiedManager
        EnsureUnifiedManager(vrmModel);
        
        // 3. 添加VRM10FaceController
        EnsureFaceController(vrmModel);
        
        // 4. 添加VRM10ClothBinder
        EnsureClothBinder(vrmModel);
        
        // 5. 配置默认设置
        ConfigureDefaultSettings(vrmModel);
        
        Debug.Log("✅ VRM 1.0系统设置完成！");
    }
}
```

---

### 2. **VRM10ClothExtractor.cs** - 服装提取器 (核心工具)
**文件位置**: `Assets/Scripts/Editor/VRM10ClothExtractor.cs`  
**脚本行数**: 1756行  
**菜单位置**: `Tools → VRM 1.0 → Cloth Extractor`

#### **主要功能**
- 🎨 **智能服装检测** - 自动识别VRM模型中的服装组件
- 📁 **批量提取处理** - 支持一键提取多个服装
- 🏷️ **自动分类命名** - 根据前缀规则自动分类
- 🔄 **批量模型处理** - 自动扫描文件夹中的所有VRM模型
- 🌸 **SpringBone保留** - 保持物理效果完整性
- 🎭 **材质副本创建** - 避免材质引用冲突

#### **支持的服装分类**
```csharp
public enum ClothType
{
    Top,        // 上衣 (衬衫、T恤、外套)
    Bottom,     // 下装 (裤子、裙子、短裤)
    Dress,      // 连衣裙 (与上衣下装互斥)
    Shoes,      // 鞋子 (运动鞋、高跟鞋、靴子)
    Accessory,  // 配饰 (包包、饰品、首饰)
    Hat,        // 帽子 (棒球帽、贝雷帽)
    Gloves,     // 手套
    Socks,      // 袜子
    Hair,       // 发型 (可选)
    Face        // 面部配件 (可选)
}
```

#### **使用流程**
```
第一步: 打开提取器
Tools → VRM 1.0 → Cloth Extractor

第二步: 基础设置
- VRM 1.0模型: 拖入包含服装的完整VRM模型
- 输出路径: 设置提取的服装Prefab保存位置
- 服装前缀: 为提取的服装添加统一前缀

第三步: 检测选项
✅ 自动检测服装     ✅ 包含配饰
✅ 保留SpringBone   ✅ 创建材质副本

第四步: 执行提取
点击 "🎨 提取所选服装" 开始处理
```

#### **高级功能**
```csharp
// 批量处理模式
[Header("批量处理")]
public bool enableBatchMode = false;
public string vrmFolderPath = "";
public bool processAllInFolder = false;

// 智能命名系统
[Header("智能命名")]
public bool enableSmartPrefix = true;
public bool includeSourceModelName = true;
public bool includeClothType = true;
public bool includeTimestamp = false;
public string separator = "_";

// VRoid专用优化
[Header("VRoid优化")]
public bool enableVRoidMode = true;
public bool fixVRoidBoneIssues = true;
public bool createMissingBones = true;
```

---

### 3. **VRM10FaceSetupTool.cs** - 面部系统设置工具
**文件位置**: `Assets/Scripts/Editor/VRM10FaceSetupTool.cs`  
**脚本行数**: 314行  
**菜单位置**: `Tools → VRM 1.0 → Face Setup Tool`

#### **主要功能**
- 🎭 **面部系统自动设置** - 一键配置完整的面部控制系统
- 🔍 **BlendShape检测** - 自动检测并配置可用的表情参数
- ⚙️ **参数优化** - 自动优化表情参数的范围和权重
- 🧪 **实时测试** - 在编辑器中实时测试表情效果
- 📊 **兼容性检查** - 验证VRM模型的表情兼容性

#### **设置流程**
```csharp
public class VRM10FaceSetupTool : EditorWindow
{
    public void SetupFaceSystem()
    {
        // 1. 验证VRM模型
        if (!ValidateVRMModel()) return;
        
        // 2. 检测BlendShape
        var blendShapes = DetectAvailableBlendShapes();
        
        // 3. 创建面部参数配置
        CreateFacialParametersConfig(blendShapes);
        
        // 4. 设置VRM10FaceController
        SetupFaceController();
        
        // 5. 配置默认表情
        ConfigureDefaultExpressions();
        
        EditorUtility.DisplayDialog("完成", "面部系统设置完成！", "确定");
    }
}
```

---

## 🦴 **骨骼分析工具**

### 4. **VRM10BoneAutoFixer.cs** - 骨骼自动修复工具
**文件位置**: `Assets/Scripts/Editor/VRM10BoneAutoFixer.cs`  
**脚本行数**: 463行  
**主要功能**: 自动修复VRM模型的骨骼结构问题

#### **修复功能**
- 🔧 **缺失骨骼补全** - 自动创建缺失的中间骨骼节点
- 📐 **骨骼层次修复** - 修正错误的骨骼父子关系
- 🎯 **位置校正** - 自动校正骨骼的位置和旋转
- 🔗 **命名标准化** - 统一骨骼命名规范
- ⚖️ **权重优化** - 优化顶点权重分布

#### **使用方法**
```csharp
// 自动修复所有问题
VRM10BoneAutoFixer.AutoFixAllIssues(vrmModel);

// 修复特定问题
VRM10BoneAutoFixer.FixMissingBones(vrmModel);
VRM10BoneAutoFixer.FixBoneHierarchy(vrmModel);
VRM10BoneAutoFixer.NormalizeBoneNames(vrmModel);
```

---

### 5. **VRoidBoneAnalyzer.cs** - VRoid专用骨骼分析器
**文件位置**: `Assets/Scripts/Editor/VRoidBoneAnalyzer.cs`  
**脚本行数**: 332行  
**主要功能**: 专门分析VRoid Studio导出模型的骨骼结构

#### **分析功能**
- 🔍 **VRoid骨骼检测** - 识别VRoid特有的骨骼结构
- 📊 **兼容性评估** - 评估与换装系统的兼容性
- ⚠️ **问题诊断** - 识别潜在的骨骼问题
- 📋 **报告生成** - 生成详细的分析报告
- 🛠️ **修复建议** - 提供针对性的修复建议

#### **分析报告示例**
```csharp
[System.Serializable]
public class VRoidBoneAnalysisReport
{
    [Header("基本信息")]
    public string modelName;
    public string vrmVersion;
    public int totalBoneCount;
    public bool isVRoidModel;
    
    [Header("兼容性")]
    public float compatibilityScore; // 0.0-1.0
    public List<string> compatibilityIssues;
    public List<string> recommendations;
    
    [Header("骨骼结构")]
    public BoneStructureInfo boneStructure;
    public List<MissingBoneInfo> missingBones;
    public List<ExtraBoneInfo> extraBones;
}
```

---

## 🔍 **调试诊断工具**

### 6. **VRM10ClothDebugger.cs** - 服装绑定调试器
**文件位置**: `Assets/Scripts/Editor/VRM10ClothDebugger.cs`  
**脚本行数**: 301行  
**主要功能**: 调试服装绑定过程中的问题

#### **调试功能**
- 🐛 **绑定过程跟踪** - 实时跟踪服装绑定过程
- 📊 **成功率统计** - 统计骨骼绑定成功率
- 🔍 **失败原因分析** - 分析绑定失败的具体原因
- 📝 **详细日志** - 生成详细的调试日志
- 🎯 **可视化显示** - 在Scene视图中可视化骨骼绑定

#### **使用方法**
```csharp
// 开启调试模式
VRM10ClothDebugger.EnableDebugMode(true);

// 调试特定服装绑定
VRM10ClothDebugger.DebugClothBinding(clothPrefab, targetVrmModel);

// 生成调试报告
var report = VRM10ClothDebugger.GenerateDebugReport();
```

---

### 7. **ConsoleOutputSimplifier.cs** - 控制台输出简化器
**文件位置**: `Assets/Scripts/Editor/ConsoleOutputSimplifier.cs`  
**脚本行数**: 335行  
**主要功能**: 简化和过滤Unity控制台输出

#### **简化功能**
- 🔽 **日志分类** - 按重要程度自动分类日志
- 🚫 **噪音过滤** - 过滤重复和无关的日志信息
- 🎨 **颜色编码** - 使用颜色区分不同类型的日志
- 📊 **统计信息** - 显示日志统计和性能指标
- 💾 **导出功能** - 导出过滤后的日志到文件

#### **过滤规则配置**
```csharp
[System.Serializable]
public class LogFilterSettings
{
    [Header("过滤设置")]
    public bool filterWarnings = false;
    public bool filterErrors = false;
    public bool filterInfo = true;
    
    [Header("关键词过滤")]
    public List<string> ignoreKeywords = new List<string>();
    public List<string> highlightKeywords = new List<string>();
    
    [Header("性能设置")]
    public int maxLogCount = 1000;
    public bool autoScroll = true;
    public bool timestampDisplay = true;
}
```

---

## 🚀 **工具使用指南**

### 快速开始流程

#### 步骤1: 系统初始化设置
```
1. 导入VRM 1.0模型到Unity项目
2. 选择VRM模型，打开 Tools → VRM 1.0 → System Setup
3. 点击 "⚙️ 设置VRM 1.0捏脸换装系统"
4. 等待自动设置完成
```

#### 步骤2: 服装提取
```
1. 打开 Tools → VRM 1.0 → Cloth Extractor
2. 拖入源VRM模型到"源VRM实例"字段
3. 设置输出路径和前缀
4. 配置检测选项
5. 点击 "🎨 提取所选服装"
```

#### 步骤3: 面部系统设置
```
1. 打开 Tools → VRM 1.0 → Face Setup Tool
2. 选择目标VRM模型
3. 点击 "🎭 设置VRM 1.0捏脸系统"
4. 在Inspector中测试表情效果
```

### 高级用法示例

#### 批量处理多个VRM模型
```csharp
// 批量处理文件夹中的所有VRM模型
public static void BatchProcessVRMModels(string folderPath)
{
    var vrmFiles = Directory.GetFiles(folderPath, "*.vrm", SearchOption.AllDirectories);
    
    foreach (var vrmFile in vrmFiles)
    {
        // 加载VRM模型
        var vrmModel = LoadVRMModel(vrmFile);
        
        // 自动设置系统
        VRM10SystemSetup.SetupVRM10System(vrmModel);
        
        // 提取服装
        VRM10ClothExtractor.ExtractAllClothes(vrmModel);
        
        // 设置面部系统
        VRM10FaceSetupTool.SetupFaceSystem(vrmModel);
        
        Debug.Log($"✅ 处理完成: {Path.GetFileName(vrmFile)}");
    }
}
```

#### 自定义编辑器工具
```csharp
public class CustomVRM10Tool : EditorWindow
{
    [MenuItem("Tools/VRM 1.0/Custom Tool")]
    public static void ShowWindow()
    {
        GetWindow<CustomVRM10Tool>("自定义VRM工具");
    }
    
    private void OnGUI()
    {
        EditorGUILayout.LabelField("自定义VRM 1.0工具", EditorStyles.boldLabel);
        
        if (GUILayout.Button("执行自定义操作"))
        {
            PerformCustomOperation();
        }
    }
    
    private void PerformCustomOperation()
    {
        // 自定义操作逻辑
        var selectedVRM = Selection.activeGameObject;
        if (selectedVRM != null && selectedVRM.GetComponent<Vrm10Instance>() != null)
        {
            // 执行VRM相关操作
            Debug.Log("执行自定义VRM操作");
        }
    }
}
```

---

## ⚠️ **注意事项和最佳实践**

### 工具使用建议
1. **按顺序使用工具**: 建议按SystemSetup → ClothExtractor → FaceSetupTool的顺序使用
2. **备份原始模型**: 在使用工具前备份原始VRM模型
3. **检查输出结果**: 工具执行后检查生成的Prefab和配置
4. **测试功能**: 使用测试工具验证设置是否正确

### 性能优化
1. **批量处理**: 处理多个模型时使用批量模式提高效率
2. **关闭实时预览**: 大量处理时关闭实时预览功能
3. **清理临时文件**: 定期清理工具生成的临时文件
4. **合理设置缓存**: 根据内存大小调整缓存设置

### 常见问题解决
1. **工具无法找到VRM模型**: 确保模型有Vrm10Instance组件
2. **服装提取失败**: 检查模型骨骼结构完整性
3. **面部系统不工作**: 验证模型包含必要的BlendShape
4. **编辑器卡死**: 减少批量处理的数量，分批执行

### 开发扩展
1. **自定义工具**: 继承现有工具类创建自定义功能
2. **钩子函数**: 利用工具提供的钩子函数扩展功能
3. **配置文件**: 创建配置文件保存常用设置
4. **插件集成**: 与其他Unity插件集成使用

---

## 📚 **相关文档**
- [VRoid核心系统文档](01_VRoid核心系统文档.md) - 了解核心系统架构
- [数据管理系统文档](02_数据管理系统文档.md) - 数据处理相关
- [API参考文档](07_API参考文档.md) - 完整API参考
- [FAQ问题解答](08_FAQ问题解答.md) - 常见问题解决

---

**版本**: 1.0.0  
**最后更新**: 2025-01-22  
**工具数量**: 18个编辑器工具  
**维护者**: 编辑器工具开发团队 