using UnityEngine;
using UnityEditor;
using VRoidFaceCustomization;
using UniVRM10;
using System.Collections.Generic;
using System.Linq;
using System.IO;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// VRM 1.0 服装提取器
    /// 从VRM 1.0模型中提取服装组件并创建可重用的Prefab
    /// </summary>
    public class VRM10ClothExtractor : EditorWindow
    {
        [Header("提取设置")]
        private Vrm10Instance sourceVrmInstance;
        private string outputPath = "Assets/Prefabs/clorh model/ExtractedClothes/";
        private string clothPrefix = "";
        private bool useSmartPrefix = true;
        private bool includeSourceModelName = false;
        private bool includeClothType = true;
        private bool includeTimestamp = false;
        private string customSeparator = "_";
        private bool autoDetectClothes = true;
        private bool includeAccessories = true;
        private bool preserveSpringBones = true;
        private bool createMaterialCopies = true;



        [Header("VRoid骨骼修复")]
        private bool enableVRoidBoneFix = true;  // 启用VRoid骨骼修复功能
        private GameObject targetCharacter;

        [Header("批量处理设置")]
        private string batchSourcePath = "Assets/Models/VRM";
        private bool enableBatchMode = false;
        private List<GameObject> batchVrmModels = new List<GameObject>();
        private int currentBatchIndex = 0;
        private bool showBatchResults = false;

        [Header("服装分类")]
        private readonly string[] excludeKeywords = { "Body", "Face", "Hair", "InnerBottom", "InnerTop" };
        private readonly string[] topKeywords = { "Top", "Shirt", "Jacket", "Coat", "Blouse" };
        private readonly string[] bottomKeywords = { "Bottom", "Pants", "Skirt", "Shorts" };
        private readonly string[] dressKeywords = { "Dress", "Onepiece", "Gown", "Robe" };
        private readonly string[] shoesKeywords = { "Shoes", "Boots", "Sandals", "Heels" };
        private readonly string[] hatKeywords = { "Hat", "Cap", "Helmet", "Crown", "Headband" };
        private readonly string[] glovesKeywords = { "Gloves", "Glove", "Mittens", "Gauntlets" };
        private readonly string[] socksKeywords = { "Socks", "Sock", "Stockings", "Tights", "Pantyhose" };
        private readonly string[] accessoryKeywords = { "Glasses", "Necklace", "Earring", "Bracelet", "Ring", "Watch", "Bag" };
        
        private Vector2 scrollPosition;
        private Vector2 batchScrollPosition = Vector2.zero;
        private Vector2 clothListScrollPosition = Vector2.zero;
        private List<ClothingItem> detectedClothes = new List<ClothingItem>();

        // 折叠面板状态
        private bool showBasicSettings = true;
        private bool showVRoidBoneSettings = true;
        private bool showPrefixSettings = false;
        
        [System.Serializable]
        public class ClothingItem
        {
            public string name;
            public SkinnedMeshRenderer renderer;
            public VRoidFaceCustomization.VRM10ClothType type;
            public bool selected = true;
            public string customName = "";
            public bool fixVRoidBones = false; // 新增：是否修复VRoid骨骼
            public Vrm10Instance sourceModel; // 源VRM模型实例
            public string sourceModelName = ""; // 源模型名称
        }
        

        
        [MenuItem("Tools/VRM 1.0/Cloth Extractor")]
        public static void ShowWindow()
        {
            var window = GetWindow<VRM10ClothExtractor>("VRM 1.0 服装提取器");
            window.minSize = new Vector2(600, 400); // 设置最小窗口大小
            window.maxSize = new Vector2(1200, 1000); // 设置最大窗口大小
        }
        
        private void OnGUI()
        {
            GUILayout.Label("VRM 1.0 服装提取器", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // 开始主滚动区域
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // 基础设置
            DrawBasicSettings();
            EditorGUILayout.Space();

            // 检测按钮
            string buttonText = enableBatchMode && batchVrmModels.Count > 0
                ? $"检测所有模型的服装组件 ({batchVrmModels.Count}个模型)"
                : "检测服装组件";

            if (GUILayout.Button(buttonText, GUILayout.Height(30)))
            {
                if (enableBatchMode && batchVrmModels.Count > 0)
                {
                    DetectAllModelsClothingComponents();
                }
                else
                {
                    DetectClothingComponents();
                }
            }

            EditorGUILayout.Space();

            // 显示检测到的服装
            if (detectedClothes.Count > 0)
            {
                DrawDetectedClothes();
                EditorGUILayout.Space();

                // 提取按钮
                if (GUILayout.Button("提取选中的服装", GUILayout.Height(30)))
                {
                    ExtractSelectedClothes();
                }
            }

            // 结束主滚动区域
            EditorGUILayout.EndScrollView();
        }
        
        private void DrawBasicSettings()
        {
            showBasicSettings = EditorGUILayout.Foldout(showBasicSettings, "基础设置", true, EditorStyles.foldoutHeader);
            if (!showBasicSettings) return;

            // 模式选择
            enableBatchMode = EditorGUILayout.Toggle("启用批量处理模式", enableBatchMode);

            if (enableBatchMode)
            {
                DrawBatchModeSettings();
            }
            else
            {
                sourceVrmInstance = EditorGUILayout.ObjectField("VRM 1.0 模型", sourceVrmInstance, typeof(Vrm10Instance), true) as Vrm10Instance;
            }
            
            EditorGUILayout.BeginHorizontal();
            outputPath = EditorGUILayout.TextField("输出路径", outputPath);
            if (GUILayout.Button("选择", GUILayout.Width(50)))
            {
                string selectedPath = EditorUtility.OpenFolderPanel("选择输出文件夹", "Assets", "");
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    outputPath = "Assets" + selectedPath.Substring(Application.dataPath.Length) + "/";
                }
            }
            EditorGUILayout.EndHorizontal();
            
            // 智能前缀设置
            DrawPrefixSettings();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("提取选项", EditorStyles.boldLabel);
            autoDetectClothes = EditorGUILayout.Toggle("自动检测服装", autoDetectClothes);
            includeAccessories = EditorGUILayout.Toggle("包含配饰", includeAccessories);
            preserveSpringBones = EditorGUILayout.Toggle("保留SpringBone", preserveSpringBones);
            createMaterialCopies = EditorGUILayout.Toggle("创建材质副本", createMaterialCopies);



            EditorGUILayout.Space();
            showVRoidBoneSettings = EditorGUILayout.Foldout(showVRoidBoneSettings, "VRoid骨骼修复", true, EditorStyles.foldoutHeader);

            if (showVRoidBoneSettings)
            {
                EditorGUI.indentLevel++;
                enableVRoidBoneFix = EditorGUILayout.Toggle("🔧 启用VRoid骨骼修复", enableVRoidBoneFix);

                if (enableVRoidBoneFix)
            {
                EditorGUI.indentLevel++;

                if (sourceVrmInstance == null)
                {
                    EditorGUILayout.HelpBox("请先选择VRM 1.0模型以启用骨骼修复功能", MessageType.Warning);
                }
                else
                {
                    EditorGUILayout.HelpBox(
                        "VRoid骨骼修复功能:\n" +
                        "• 标记需要修复的服装\n" +
                        "• 提取完成后自动添加骨骼数据\n" +
                        "• 修复后的服装可以正常穿戴",
                        MessageType.Info);


                }

                EditorGUI.indentLevel--;
            }
            EditorGUI.indentLevel--;
            }
        }
        
        private void DrawDetectedClothes()
        {
            EditorGUILayout.LabelField($"检测到的服装组件 ({detectedClothes.Count})", EditorStyles.boldLabel);

            // 计算自适应高度
            float windowHeight = position.height;
            float availableHeight = windowHeight - 300f; // 为其他UI元素预留空间
            float maxHeight = Mathf.Max(150f, Mathf.Min(availableHeight, 400f)); // 最小150，最大400

            clothListScrollPosition = EditorGUILayout.BeginScrollView(clothListScrollPosition, GUILayout.Height(maxHeight));
            
            for (int i = 0; i < detectedClothes.Count; i++)
            {
                var item = detectedClothes[i];
                
                EditorGUILayout.BeginHorizontal();

                // 提取复选框
                item.selected = EditorGUILayout.Toggle(item.selected, GUILayout.Width(20));

                // 源模型名称（批量模式下显示）
                if (enableBatchMode && !string.IsNullOrEmpty(item.sourceModelName))
                {
                    EditorGUILayout.LabelField($"[{item.sourceModelName}]", GUILayout.Width(80));
                }

                // 服装名称
                EditorGUILayout.LabelField(item.name, GUILayout.Width(enableBatchMode ? 100 : 120));

                // 服装类型
                item.type = (VRoidFaceCustomization.VRM10ClothType)EditorGUILayout.EnumPopup(item.type, GUILayout.Width(80));

                // 自定义名称
                string newName = EditorGUILayout.TextField(item.customName, GUILayout.Width(enableBatchMode ? 120 : 100));
                if (newName != item.customName)
                {
                    item.customName = newName;
                }

                // VRoid骨骼修复复选框
                if (enableVRoidBoneFix && sourceVrmInstance != null)
                {
                    bool shouldRecommend = ShouldRecommendVRoidFix(item);
                    if (shouldRecommend && !item.fixVRoidBones)
                    {
                        GUI.color = Color.yellow;
                    }

                    item.fixVRoidBones = EditorGUILayout.Toggle(new GUIContent("🔧", "修复VRoid骨骼"), item.fixVRoidBones, GUILayout.Width(30));

                    GUI.color = Color.white;
                }

                // 预览按钮
                if (GUILayout.Button("预览", GUILayout.Width(50)))
                {
                    Selection.activeGameObject = item.renderer.gameObject;
                    EditorGUIUtility.PingObject(item.renderer.gameObject);
                }

                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("全选"))
            {
                detectedClothes.ForEach(item => item.selected = true);
            }
            if (GUILayout.Button("全不选"))
            {
                detectedClothes.ForEach(item => item.selected = false);
            }
            if (GUILayout.Button("反选"))
            {
                detectedClothes.ForEach(item => item.selected = !item.selected);
            }
            EditorGUILayout.EndHorizontal();

            // VRoid骨骼修复批量操作
            if (enableVRoidBoneFix && sourceVrmInstance != null)
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("全部修复"))
                {
                    detectedClothes.ForEach(item => item.fixVRoidBones = true);
                }
                if (GUILayout.Button("全不修复"))
                {
                    detectedClothes.ForEach(item => item.fixVRoidBones = false);
                }
                if (GUILayout.Button("智能推荐"))
                {
                    SmartRecommendVRoidFix();
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        /// <summary>
        /// 绘制前缀设置
        /// </summary>
        private void DrawPrefixSettings()
        {
            showPrefixSettings = EditorGUILayout.Foldout(showPrefixSettings, "命名设置", true, EditorStyles.foldoutHeader);
            if (!showPrefixSettings) return;

            useSmartPrefix = EditorGUILayout.Toggle("启用智能前缀", useSmartPrefix);

            if (useSmartPrefix)
            {
                EditorGUI.indentLevel++;

                includeSourceModelName = EditorGUILayout.Toggle("包含源模型名", includeSourceModelName);
                includeClothType = EditorGUILayout.Toggle("包含服装类型", includeClothType);
                includeTimestamp = EditorGUILayout.Toggle("包含时间戳", includeTimestamp);

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("分隔符", GUILayout.Width(60));
                customSeparator = EditorGUILayout.TextField(customSeparator, GUILayout.Width(30));
                EditorGUILayout.LabelField("自定义前缀", GUILayout.Width(80));
                clothPrefix = EditorGUILayout.TextField(clothPrefix);
                EditorGUILayout.EndHorizontal();

                // 预览命名格式
                if (sourceVrmInstance != null)
                {
                    string preview = GenerateSmartClothName("ExampleTop", VRM10ClothType.Top);
                    EditorGUILayout.HelpBox($"预览格式: {preview}", MessageType.Info);
                }

                EditorGUI.indentLevel--;
            }
            else
            {
                clothPrefix = EditorGUILayout.TextField("服装前缀", clothPrefix);
            }
        }

        /// <summary>
        /// 检测所有模型的服装组件（批量模式）
        /// </summary>
        private void DetectAllModelsClothingComponents()
        {
            if (!enableBatchMode || batchVrmModels.Count == 0)
            {
                EditorUtility.DisplayDialog("错误", "批量模式未启用或没有扫描到模型", "确定");
                return;
            }

            detectedClothes.Clear();

            try
            {
                int totalClothes = 0;

                // 显示进度条
                for (int modelIndex = 0; modelIndex < batchVrmModels.Count; modelIndex++)
                {
                    var vrmModel = batchVrmModels[modelIndex];
                    if (vrmModel == null) continue;

                    var vrmInstance = vrmModel.GetComponent<Vrm10Instance>();
                    if (vrmInstance == null) continue;

                    // 显示进度
                    float progress = (float)modelIndex / batchVrmModels.Count;
                    bool cancelled = EditorUtility.DisplayCancelableProgressBar(
                        "检测服装组件",
                        $"正在检测: {vrmModel.name} ({modelIndex + 1}/{batchVrmModels.Count})",
                        progress);

                    if (cancelled)
                    {
                        EditorUtility.ClearProgressBar();
                        return;
                    }

                    // 检测当前模型的服装
                    var modelClothes = DetectSingleModelClothes(vrmInstance, vrmModel.name);
                    detectedClothes.AddRange(modelClothes);
                    totalClothes += modelClothes.Count;
                }

                EditorUtility.ClearProgressBar();

                Debug.Log($"[VRM10ClothExtractor] 批量检测完成: 从 {batchVrmModels.Count} 个模型中检测到 {totalClothes} 个服装组件");

                // 如果启用了VRoid骨骼修复，应用智能推荐
                if (enableVRoidBoneFix)
                {
                    foreach (var item in detectedClothes)
                    {
                        item.fixVRoidBones = ShouldRecommendVRoidFix(item);
                    }

                    int recommendCount = detectedClothes.Count(item => item.fixVRoidBones);
                    Debug.Log($"[VRM10ClothExtractor] 智能推荐 {recommendCount}/{detectedClothes.Count} 个服装进行VRoid骨骼修复");
                }

                EditorUtility.DisplayDialog("检测完成",
                    $"成功检测到 {totalClothes} 个服装组件\n来自 {batchVrmModels.Count} 个VRM模型", "确定");
            }
            catch (System.Exception e)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError($"[VRM10ClothExtractor] 批量检测失败: {e.Message}");
                EditorUtility.DisplayDialog("检测失败", $"批量检测过程出错: {e.Message}", "确定");
            }
        }

        /// <summary>
        /// 检测单个模型的服装组件
        /// </summary>
        private List<ClothingItem> DetectSingleModelClothes(Vrm10Instance vrmInstance, string modelName)
        {
            var modelClothes = new List<ClothingItem>();

            if (vrmInstance == null) return modelClothes;

            var renderers = vrmInstance.GetComponentsInChildren<SkinnedMeshRenderer>();
            int clothIndex = 0; // 为每个模型的服装添加序号

            foreach (var renderer in renderers)
            {
                if (renderer == null || renderer.sharedMesh == null) continue;

                string rendererName = renderer.name;

                // 跳过排除的组件
                if (excludeKeywords.Any(keyword => rendererName.Contains(keyword))) continue;

                // 分类服装类型
                VRM10ClothType clothType = ClassifyClothingType(rendererName);

                // 如果不包含配饰且是配饰类型，跳过
                if (!includeAccessories && clothType == VRM10ClothType.Accessory) continue;

                // 生成智能名称（传入特定的模型实例和序号）
                string finalName = GenerateSmartClothName(rendererName, clothType, vrmInstance, modelName, clothIndex);

                var clothItem = new ClothingItem
                {
                    name = rendererName,
                    customName = finalName,
                    type = clothType,
                    renderer = renderer,
                    selected = true, // 默认选中
                    sourceModel = vrmInstance, // 记录源模型
                    sourceModelName = modelName // 记录源模型名称
                };

                // 应用VRoid骨骼修复推荐
                if (enableVRoidBoneFix)
                {
                    clothItem.fixVRoidBones = ShouldRecommendVRoidFix(clothItem);
                }

                modelClothes.Add(clothItem);
                clothIndex++; // 增加序号
            }

            return modelClothes;
        }

        /// <summary>
        /// 智能推荐VRoid骨骼修复
        /// </summary>
        private void SmartRecommendVRoidFix()
        {
            foreach (var item in detectedClothes)
            {
                item.fixVRoidBones = ShouldRecommendVRoidFix(item);
            }

            int recommendCount = detectedClothes.Count(item => item.fixVRoidBones);
            Debug.Log($"[VRM10ClothExtractor] 智能推荐: {recommendCount}/{detectedClothes.Count} 个服装需要VRoid骨骼修复");
        }

        /// <summary>
        /// 判断是否应该推荐VRoid骨骼修复
        /// </summary>
        private bool ShouldRecommendVRoidFix(ClothingItem item)
        {
            if (item.renderer == null || item.renderer.bones == null) return false;

            int vroidBoneCount = 0;
            int totalBones = item.renderer.bones.Length;

            foreach (var bone in item.renderer.bones)
            {
                if (bone != null && IsVRoidSpecificBone(bone.name))
                {
                    vroidBoneCount++;
                }
            }

            // 如果VRoid专用骨骼占比超过20%，推荐修复
            float vroidRatio = totalBones > 0 ? (float)vroidBoneCount / totalBones : 0f;
            return vroidRatio > 0.2f;
        }

        /// <summary>
        /// 判断是否为VRoid专用骨骼
        /// </summary>
        private bool IsVRoidSpecificBone(string boneName)
        {
            if (string.IsNullOrEmpty(boneName)) return false;

            // VRoid专用骨骼特征
            return boneName.Contains("transferable_") ||
                   boneName.Contains("J_Sec_") ||
                   boneName.Contains("CoatSkirt") ||
                   boneName.Contains("_Adj_") ||
                   boneName.Contains("_Sub_");
        }

        /// <summary>
        /// 检测VRM模型中的服装组件
        /// </summary>
        private void DetectClothingComponents()
        {
            if (sourceVrmInstance == null)
            {
                EditorUtility.DisplayDialog("错误", "请先选择VRM 1.0模型", "确定");
                return;
            }
            
            detectedClothes.Clear();
            
            // 获取所有SkinnedMeshRenderer
            var renderers = sourceVrmInstance.GetComponentsInChildren<SkinnedMeshRenderer>();
            
            foreach (var renderer in renderers)
            {
                string meshName = renderer.name.ToLower();
                
                // 跳过身体基础部件
                if (excludeKeywords.Any(keyword => meshName.Contains(keyword.ToLower())))
                    continue;
                
                // 跳过配饰（如果设置了不包含）
                if (!includeAccessories && accessoryKeywords.Any(keyword => meshName.Contains(keyword.ToLower())))
                    continue;
                
                var clothItem = new ClothingItem
                {
                    name = renderer.name,
                    renderer = renderer,
                    type = ClassifyClothingType(renderer.name),
                    customName = GenerateClothName(renderer.name),
                    fixVRoidBones = enableVRoidBoneFix && ShouldRecommendVRoidFix(new ClothingItem { renderer = renderer })
                };

                detectedClothes.Add(clothItem);
            }
            
            Debug.Log($"[VRM10ClothExtractor] 检测到 {detectedClothes.Count} 个服装组件");

            // 如果启用了VRoid骨骼修复，显示推荐统计
            if (enableVRoidBoneFix)
            {
                int recommendCount = detectedClothes.Count(item => item.fixVRoidBones);
                Debug.Log($"[VRM10ClothExtractor] 智能推荐 {recommendCount}/{detectedClothes.Count} 个服装进行VRoid骨骼修复");
            }
        }






        
        /// <summary>
        /// 分类服装类型
        /// </summary>
        private VRoidFaceCustomization.VRM10ClothType ClassifyClothingType(string name)
        {
            string lowerName = name.ToLower();

            // 按优先级检查类型
            if (dressKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Dress;
            if (topKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Top;
            if (bottomKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Bottom;
            if (shoesKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Shoes;
            if (hatKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Hat;
            if (glovesKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Gloves;
            if (socksKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Socks;
            if (accessoryKeywords.Any(keyword => lowerName.Contains(keyword.ToLower())))
                return VRoidFaceCustomization.VRM10ClothType.Accessory;

            return VRoidFaceCustomization.VRM10ClothType.Other;
        }
        
        /// <summary>
        /// 生成服装名称（智能版本）
        /// </summary>
        private string GenerateClothName(string originalName)
        {
            // 获取服装类型
            VRM10ClothType clothType = ClassifyClothingType(originalName);
            return GenerateSmartClothName(originalName, clothType);
        }

        /// <summary>
        /// 生成智能服装名称（重载版本，用于批量模式）
        /// </summary>
        private string GenerateSmartClothName(string originalName, VRM10ClothType clothType, Vrm10Instance specificModel, string modelName, int clothIndex = -1)
        {
            if (!useSmartPrefix)
            {
                return GenerateSimpleClothName(originalName);
            }

            List<string> nameParts = new List<string>();

            // 添加自定义前缀
            if (!string.IsNullOrEmpty(clothPrefix))
            {
                nameParts.Add(clothPrefix);
            }

            // 添加源模型名（使用传入的模型名称）
            if (includeSourceModelName && !string.IsNullOrEmpty(modelName))
            {
                string cleanedModelName = CleanModelName(modelName);
                if (!string.IsNullOrEmpty(cleanedModelName))
                {
                    nameParts.Add(cleanedModelName);
                }
            }

            // 添加服装类型
            if (includeClothType)
            {
                nameParts.Add(GetClothTypeString(clothType));
            }

            // 添加原始名称（清理后）
            string cleanedName = CleanClothName(originalName);
            if (!string.IsNullOrEmpty(cleanedName))
            {
                nameParts.Add(cleanedName);
            }

            // 添加序号（用于区分同一模型的不同服装）
            if (clothIndex >= 0)
            {
                nameParts.Add($"{clothIndex:D2}"); // 两位数格式，如 00, 01, 02
            }

            // 添加时间戳
            if (includeTimestamp)
            {
                nameParts.Add(System.DateTime.Now.ToString("MMdd_HHmm"));
            }

            // 组合名称
            string result = string.Join("_", nameParts.Where(p => !string.IsNullOrEmpty(p)));
            return string.IsNullOrEmpty(result) ? originalName : result;
        }

        /// <summary>
        /// 生成智能服装名称（原版本，用于单个模式）
        /// </summary>
        private string GenerateSmartClothName(string originalName, VRM10ClothType clothType)
        {
            if (!useSmartPrefix)
            {
                return GenerateSimpleClothName(originalName);
            }

            List<string> nameParts = new List<string>();

            // 添加自定义前缀
            if (!string.IsNullOrEmpty(clothPrefix))
            {
                nameParts.Add(clothPrefix);
            }

            // 添加源模型名
            if (includeSourceModelName && sourceVrmInstance != null)
            {
                string modelName = CleanModelName(sourceVrmInstance.name);
                if (!string.IsNullOrEmpty(modelName))
                {
                    nameParts.Add(modelName);
                }
            }

            // 添加服装类型
            if (includeClothType)
            {
                nameParts.Add(GetClothTypeString(clothType));
            }

            // 添加原始名称（清理后）
            string cleanedName = CleanClothName(originalName);
            if (!string.IsNullOrEmpty(cleanedName))
            {
                nameParts.Add(cleanedName);
            }

            // 添加时间戳
            if (includeTimestamp)
            {
                nameParts.Add(System.DateTime.Now.ToString("MMdd_HHmm"));
            }

            // 组合名称
            string finalName = string.Join(customSeparator, nameParts.Where(p => !string.IsNullOrEmpty(p)));

            // 确保名称不为空
            if (string.IsNullOrEmpty(finalName))
            {
                finalName = originalName;
            }

            return finalName;
        }

        /// <summary>
        /// 生成简单服装名称（兼容旧版本）
        /// </summary>
        private string GenerateSimpleClothName(string originalName)
        {
            string baseName = originalName;

            // 移除常见的前缀和后缀
            baseName = baseName.Replace("_", " ").Replace("-", " ");

            // 添加用户定义的前缀
            if (!string.IsNullOrEmpty(clothPrefix))
            {
                return $"{clothPrefix}{customSeparator}{baseName}";
            }

            return baseName;
        }

        /// <summary>
        /// 清理模型名称
        /// </summary>
        private string CleanModelName(string modelName)
        {
            if (string.IsNullOrEmpty(modelName)) return "";

            // 移除常见的后缀
            string cleaned = modelName;
            string[] suffixesToRemove = { "(Clone)", "_Instance", ".prefab", ".vrm" };

            foreach (string suffix in suffixesToRemove)
            {
                if (cleaned.EndsWith(suffix, System.StringComparison.OrdinalIgnoreCase))
                {
                    cleaned = cleaned.Substring(0, cleaned.Length - suffix.Length);
                }
            }

            // 移除特殊字符，保留中文
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"[^\w\u4e00-\u9fff]", "");

            return cleaned;
        }

        /// <summary>
        /// 清理服装名称
        /// </summary>
        private string CleanClothName(string clothName)
        {
            if (string.IsNullOrEmpty(clothName)) return "";

            string cleaned = clothName;

            // 移除常见的前缀
            string[] prefixesToRemove = { "Cloth_", "Costume_", "Outfit_", "Item_" };
            foreach (string prefix in prefixesToRemove)
            {
                if (cleaned.StartsWith(prefix, System.StringComparison.OrdinalIgnoreCase))
                {
                    cleaned = cleaned.Substring(prefix.Length);
                }
            }

            // 替换分隔符
            cleaned = cleaned.Replace("_", " ").Replace("-", " ");

            // 移除多余的空格
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\s+", " ").Trim();

            // 转换为合适的格式
            if (!string.IsNullOrEmpty(cleaned))
            {
                cleaned = cleaned.Replace(" ", customSeparator);
            }

            return cleaned;
        }

        /// <summary>
        /// 获取服装类型字符串
        /// </summary>
        private string GetClothTypeString(VRM10ClothType clothType)
        {
            switch (clothType)
            {
                case VRM10ClothType.Top: return "Top";
                case VRM10ClothType.Bottom: return "Bottom";
                case VRM10ClothType.Dress: return "Dress";
                case VRM10ClothType.Shoes: return "Shoes";
                case VRM10ClothType.Hat: return "Hat";
                case VRM10ClothType.Gloves: return "Gloves";
                case VRM10ClothType.Socks: return "Socks";
                case VRM10ClothType.Accessory: return "Accessory";
                case VRM10ClothType.Hair: return "Hair";
                default: return "Other";
            }
        }

        /// <summary>
        /// 提取选中的服装
        /// </summary>
        private void ExtractSelectedClothes()
        {
            var selectedItems = detectedClothes.Where(item => item.selected).ToList();
            
            if (selectedItems.Count == 0)
            {
                EditorUtility.DisplayDialog("提示", "请至少选择一个服装组件", "确定");
                return;
            }
            
            // 确保输出目录存在
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }
            
            int extractedCount = 0;
            var clothesToFix = new List<ClothingItem>(); // 需要修复的服装列表

            foreach (var item in selectedItems)
            {
                try
                {
                    ExtractSingleCloth(item);

                    // 如果启用了VRoid骨骼修复且该服装需要修复，记录到待修复列表
                    if (enableVRoidBoneFix && item.fixVRoidBones)
                    {
                        clothesToFix.Add(item);
                    }

                    extractedCount++;
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[VRM10ClothExtractor] 提取 {item.name} 失败: {e.Message}");
                }
            }
            
            AssetDatabase.Refresh();

            // 如果有需要修复的服装，自动调用VRM10BoneExtractor进行修复
            if (clothesToFix.Count > 0)
            {
                Debug.Log($"[VRM10ClothExtractor] 开始自动修复 {clothesToFix.Count} 个服装的骨骼");
                AutoFixExtractedClothes(clothesToFix);
            }

            EditorUtility.DisplayDialog("完成", $"成功提取 {extractedCount}/{selectedItems.Count} 个服装组件", "确定");
        }

        /// <summary>
        /// 绘制批量模式设置
        /// </summary>
        private void DrawBatchModeSettings()
        {
            EditorGUILayout.LabelField("批量处理模式", EditorStyles.miniBoldLabel);

            EditorGUILayout.BeginHorizontal();
            batchSourcePath = EditorGUILayout.TextField("VRM模型路径", batchSourcePath);
            if (GUILayout.Button("浏览", GUILayout.Width(60)))
            {
                string selectedPath = EditorUtility.OpenFolderPanel("选择VRM模型文件夹", batchSourcePath, "");
                if (!string.IsNullOrEmpty(selectedPath))
                {
                    // 转换为相对路径
                    if (selectedPath.StartsWith(Application.dataPath))
                    {
                        batchSourcePath = "Assets" + selectedPath.Substring(Application.dataPath.Length);
                    }
                    else
                    {
                        batchSourcePath = selectedPath;
                    }
                }
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("🔍 扫描VRM模型", GUILayout.Height(25)))
            {
                ScanVrmModels();
            }
            if (GUILayout.Button("🔍 深度扫描", GUILayout.Width(80), GUILayout.Height(25)))
            {
                DeepScanVrmModels();
            }
            if (GUILayout.Button("❓", GUILayout.Width(25), GUILayout.Height(25)))
            {
                ShowScanHelp();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.HelpBox(
                "扫描说明:\n" +
                "• 🔍 扫描VRM模型: 扫描.vrm和.prefab文件，查找VRM模型\n" +
                "• 🔍 深度扫描: 全面扫描所有可能的VRM文件，包含详细日志\n" +
                "• ❓ 点击查看详细帮助",
                MessageType.Info);
            if (batchVrmModels.Count > 0)
            {
                if (GUILayout.Button($"清空列表 ({batchVrmModels.Count})", GUILayout.Width(120), GUILayout.Height(25)))
                {
                    batchVrmModels.Clear();
                    currentBatchIndex = 0;
                }
            }
            EditorGUILayout.EndHorizontal();

            // 显示扫描结果
            if (batchVrmModels.Count > 0)
            {
                EditorGUILayout.Space();
                showBatchResults = EditorGUILayout.Foldout(showBatchResults, $"扫描到 {batchVrmModels.Count} 个VRM模型");

                if (showBatchResults)
                {
                    EditorGUI.indentLevel++;

                    // 为批量模型列表添加滚动区域
                    float maxHeight = 200f; // 最大高度
                    float actualHeight = Mathf.Min(batchVrmModels.Count * 20f, maxHeight);

                    batchScrollPosition = EditorGUILayout.BeginScrollView(
                        batchScrollPosition,
                        GUILayout.Height(actualHeight));

                    for (int i = 0; i < batchVrmModels.Count; i++)
                    {
                        EditorGUILayout.BeginHorizontal();

                        // 当前处理的模型高亮显示
                        if (i == currentBatchIndex)
                        {
                            GUI.backgroundColor = Color.yellow;
                        }

                        EditorGUILayout.ObjectField($"[{i + 1}]", batchVrmModels[i], typeof(GameObject), false);

                        if (GUILayout.Button("选择", GUILayout.Width(50)))
                        {
                            sourceVrmInstance = batchVrmModels[i]?.GetComponent<Vrm10Instance>();
                            currentBatchIndex = i;
                        }

                        GUI.backgroundColor = Color.white;
                        EditorGUILayout.EndHorizontal();
                    }

                    EditorGUILayout.EndScrollView();
                    EditorGUI.indentLevel--;
                }

                // 批量处理控制
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("批量处理控制", EditorStyles.miniBoldLabel);

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("⏮️ 上一个", GUILayout.Height(25)))
                {
                    if (currentBatchIndex > 0)
                    {
                        currentBatchIndex--;
                        sourceVrmInstance = batchVrmModels[currentBatchIndex]?.GetComponent<Vrm10Instance>();
                    }
                }

                EditorGUILayout.LabelField($"{currentBatchIndex + 1} / {batchVrmModels.Count}", EditorStyles.centeredGreyMiniLabel);

                if (GUILayout.Button("⏭️ 下一个", GUILayout.Height(25)))
                {
                    if (currentBatchIndex < batchVrmModels.Count - 1)
                    {
                        currentBatchIndex++;
                        sourceVrmInstance = batchVrmModels[currentBatchIndex]?.GetComponent<Vrm10Instance>();
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (GUILayout.Button("🚀 批量处理所有模型", GUILayout.Height(30)))
                {
                    StartBatchProcessing();
                }
            }
        }

        /// <summary>
        /// 扫描VRM模型（支持.vrm和.prefab文件）
        /// </summary>
        private void ScanVrmModels()
        {
            batchVrmModels.Clear();

            if (!Directory.Exists(batchSourcePath))
            {
                EditorUtility.DisplayDialog("错误", $"路径不存在: {batchSourcePath}", "确定");
                return;
            }

            try
            {
                Debug.Log($"[VRM10ClothExtractor] 开始扫描路径: {batchSourcePath}");

                // 搜索.vrm文件和.prefab文件
                List<string> allFiles = new List<string>();
                allFiles.AddRange(Directory.GetFiles(batchSourcePath, "*.vrm", SearchOption.AllDirectories));
                allFiles.AddRange(Directory.GetFiles(batchSourcePath, "*.prefab", SearchOption.AllDirectories));

                Debug.Log($"[VRM10ClothExtractor] 找到 {allFiles.Count} 个可能的VRM文件");

                foreach (string filePath in allFiles)
                {
                    try
                    {
                        // 转换为Unity资源路径
                        string assetPath = filePath.Replace('\\', '/');
                        if (assetPath.StartsWith(Application.dataPath))
                        {
                            assetPath = "Assets" + assetPath.Substring(Application.dataPath.Length);
                        }

                        Debug.Log($"[VRM10ClothExtractor] 检查文件: {assetPath}");

                        GameObject vrmObject = null;

                        if (assetPath.EndsWith(".vrm"))
                        {
                            // 对于.vrm文件，尝试加载为GameObject
                            vrmObject = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                            if (vrmObject == null)
                            {
                                // 如果直接加载失败，尝试获取导入后的主资源
                                var mainAsset = AssetDatabase.LoadMainAssetAtPath(assetPath);
                                if (mainAsset is GameObject)
                                {
                                    vrmObject = mainAsset as GameObject;
                                }
                            }
                        }
                        else if (assetPath.EndsWith(".prefab"))
                        {
                            // 对于.prefab文件，直接加载
                            vrmObject = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                        }

                        if (vrmObject != null)
                        {
                            // 检查是否包含Vrm10Instance组件
                            var vrmInstance = vrmObject.GetComponent<Vrm10Instance>();
                            if (vrmInstance != null)
                            {
                                batchVrmModels.Add(vrmObject);
                                Debug.Log($"[VRM10ClothExtractor] 找到VRM模型: {vrmObject.name} ({Path.GetExtension(assetPath)})");
                            }
                            else
                            {
                                Debug.Log($"[VRM10ClothExtractor] {vrmObject.name} 不是VRM模型（没有Vrm10Instance组件）");
                            }
                        }
                        else
                        {
                            Debug.LogWarning($"[VRM10ClothExtractor] 无法加载文件: {assetPath}");
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"[VRM10ClothExtractor] 处理文件失败 {filePath}: {e.Message}");
                    }
                }

                Debug.Log($"[VRM10ClothExtractor] 扫描完成，找到 {batchVrmModels.Count} 个VRM模型");

                if (batchVrmModels.Count > 0)
                {
                    currentBatchIndex = 0;
                    sourceVrmInstance = batchVrmModels[0]?.GetComponent<Vrm10Instance>();
                    showBatchResults = true;

                    EditorUtility.DisplayDialog("扫描完成",
                        $"成功找到 {batchVrmModels.Count} 个VRM模型！\n\n" +
                        $"包括 .vrm 和 .prefab 文件", "确定");
                }
                else
                {
                    // 提供更详细的诊断信息
                    int vrmFileCount = Directory.GetFiles(batchSourcePath, "*.vrm", SearchOption.AllDirectories).Length;
                    int prefabFileCount = Directory.GetFiles(batchSourcePath, "*.prefab", SearchOption.AllDirectories).Length;

                    string diagnosticInfo = $"扫描路径: {batchSourcePath}\n";
                    diagnosticInfo += $"找到的.vrm文件数: {vrmFileCount}\n";
                    diagnosticInfo += $"找到的.prefab文件数: {prefabFileCount}\n";
                    diagnosticInfo += $"VRM模型数: {batchVrmModels.Count}\n\n";
                    diagnosticInfo += "可能的原因:\n";
                    diagnosticInfo += "1. .vrm文件尚未导入到Unity\n";
                    diagnosticInfo += "2. 文件不包含Vrm10Instance组件\n";
                    diagnosticInfo += "3. 文件路径包含特殊字符\n\n";
                    diagnosticInfo += "建议:\n";
                    diagnosticInfo += "• 确保.vrm文件已导入到Unity项目\n";
                    diagnosticInfo += "• 检查Console窗口查看详细日志\n";
                    diagnosticInfo += "• 尝试手动拖拽一个文件测试";

                    EditorUtility.DisplayDialog("未找到VRM模型", diagnosticInfo, "确定");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothExtractor] 扫描VRM模型失败: {e.Message}");
                EditorUtility.DisplayDialog("错误", $"扫描失败: {e.Message}", "确定");
            }
        }

        /// <summary>
        /// 深度扫描VRM模型（更全面的检测）
        /// </summary>
        private void DeepScanVrmModels()
        {
            batchVrmModels.Clear();

            if (!Directory.Exists(batchSourcePath))
            {
                EditorUtility.DisplayDialog("错误", $"路径不存在: {batchSourcePath}", "确定");
                return;
            }

            try
            {
                Debug.Log($"[VRM10ClothExtractor] 开始深度扫描路径: {batchSourcePath}");

                // 搜索所有可能的文件类型
                List<string> allFiles = new List<string>();

                // 添加.prefab文件
                allFiles.AddRange(Directory.GetFiles(batchSourcePath, "*.prefab", SearchOption.AllDirectories));

                // 添加.asset文件（有些VRM可能是ScriptableObject）
                allFiles.AddRange(Directory.GetFiles(batchSourcePath, "*.asset", SearchOption.AllDirectories));

                Debug.Log($"[VRM10ClothExtractor] 找到 {allFiles.Count} 个可能的文件");

                foreach (string filePath in allFiles)
                {
                    try
                    {
                        // 转换为Unity资源路径
                        string assetPath = filePath.Replace('\\', '/');
                        if (assetPath.StartsWith(Application.dataPath))
                        {
                            assetPath = "Assets" + assetPath.Substring(Application.dataPath.Length);
                        }

                        Debug.Log($"[VRM10ClothExtractor] 深度检查文件: {assetPath}");

                        // 尝试加载为GameObject
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                        if (prefab != null)
                        {
                            // 检查多种可能的VRM组件
                            bool isVrmModel = false;

                            // 检查Vrm10Instance
                            var vrm10Instance = prefab.GetComponent<Vrm10Instance>();
                            if (vrm10Instance != null)
                            {
                                isVrmModel = true;
                                Debug.Log($"[VRM10ClothExtractor] 找到VRM 1.0模型: {prefab.name}");
                            }

                            // 检查是否有SkinnedMeshRenderer（可能是服装模型）
                            var renderers = prefab.GetComponentsInChildren<SkinnedMeshRenderer>();
                            if (renderers.Length > 0 && !isVrmModel)
                            {
                                // 检查是否有骨骼结构，可能是提取出来的服装
                                foreach (var renderer in renderers)
                                {
                                    if (renderer.bones != null && renderer.bones.Length > 5) // 至少有5个骨骼
                                    {
                                        Debug.Log($"[VRM10ClothExtractor] 找到可能的服装模型: {prefab.name}");
                                        // 可以选择是否包含服装模型
                                        break;
                                    }
                                }
                            }

                            if (isVrmModel)
                            {
                                batchVrmModels.Add(prefab);
                            }
                        }
                        else
                        {
                            // 尝试加载为其他类型的资源
                            UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(assetPath);
                            if (asset != null)
                            {
                                Debug.Log($"[VRM10ClothExtractor] 找到其他类型资源: {asset.name} ({asset.GetType().Name})");
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"[VRM10ClothExtractor] 深度扫描文件失败 {filePath}: {e.Message}");
                    }
                }

                Debug.Log($"[VRM10ClothExtractor] 深度扫描完成，找到 {batchVrmModels.Count} 个VRM模型");

                if (batchVrmModels.Count > 0)
                {
                    currentBatchIndex = 0;
                    sourceVrmInstance = batchVrmModels[0]?.GetComponent<Vrm10Instance>();
                    showBatchResults = true;

                    EditorUtility.DisplayDialog("深度扫描完成",
                        $"成功找到 {batchVrmModels.Count} 个VRM模型！", "确定");
                }
                else
                {
                    // 提供更详细的诊断信息
                    string diagnosticInfo = $"深度扫描路径: {batchSourcePath}\n";
                    diagnosticInfo += $"检查的文件总数: {allFiles.Count}\n";
                    diagnosticInfo += $"VRM模型数: {batchVrmModels.Count}\n\n";
                    diagnosticInfo += "深度扫描结果:\n";
                    diagnosticInfo += "• 未找到包含Vrm10Instance组件的Prefab\n";
                    diagnosticInfo += "• 请确认文件是VRM 1.0格式\n";
                    diagnosticInfo += "• 检查Console窗口查看详细扫描日志\n\n";
                    diagnosticInfo += "建议:\n";
                    diagnosticInfo += "1. 确认Prefab包含Vrm10Instance组件\n";
                    diagnosticInfo += "2. 尝试重新导入VRM文件\n";
                    diagnosticInfo += "3. 检查文件是否损坏";

                    EditorUtility.DisplayDialog("深度扫描结果", diagnosticInfo, "确定");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothExtractor] 深度扫描失败: {e.Message}");
                EditorUtility.DisplayDialog("错误", $"深度扫描失败: {e.Message}", "确定");
            }
        }

        /// <summary>
        /// 显示扫描帮助
        /// </summary>
        private void ShowScanHelp()
        {
            string helpText = "VRM模型扫描帮助\n\n";
            helpText += "🔍 普通扫描:\n";
            helpText += "• 快速扫描指定文件夹中的.prefab文件\n";
            helpText += "• 检查是否包含Vrm10Instance组件\n";
            helpText += "• 适合大多数情况\n\n";

            helpText += "🔍 深度扫描:\n";
            helpText += "• 扫描.prefab和.asset文件\n";
            helpText += "• 提供详细的扫描日志\n";
            helpText += "• 检查多种VRM组件类型\n";
            helpText += "• 适合排查问题\n\n";

            helpText += "常见问题:\n";
            helpText += "1. 路径包含中文或特殊字符\n";
            helpText += "   → 尝试使用英文路径\n\n";

            helpText += "2. Prefab不包含Vrm10Instance组件\n";
            helpText += "   → 确认是VRM 1.0格式\n";
            helpText += "   → 重新导入VRM文件\n\n";

            helpText += "3. 文件损坏或无法加载\n";
            helpText += "   → 检查Console窗口的错误信息\n";
            helpText += "   → 尝试重新导入资源\n\n";

            helpText += "当前扫描路径:\n";
            helpText += batchSourcePath + "\n\n";

            helpText += "建议:\n";
            helpText += "• 先尝试普通扫描\n";
            helpText += "• 如果没找到模型，使用深度扫描\n";
            helpText += "• 查看Console窗口的详细日志";

            EditorUtility.DisplayDialog("扫描帮助", helpText, "确定");
        }

        /// <summary>
        /// 开始批量处理
        /// </summary>
        private void StartBatchProcessing()
        {
            if (batchVrmModels.Count == 0)
            {
                EditorUtility.DisplayDialog("错误", "没有可处理的VRM模型", "确定");
                return;
            }

            bool confirmed = EditorUtility.DisplayDialog("确认批量处理",
                $"即将批量处理 {batchVrmModels.Count} 个VRM模型\n\n这可能需要较长时间，是否继续？",
                "开始处理", "取消");

            if (!confirmed) return;

            try
            {
                int processedCount = 0;
                int totalCount = batchVrmModels.Count;

                for (int i = 0; i < batchVrmModels.Count; i++)
                {
                    // 显示进度
                    float progress = (float)i / totalCount;
                    bool cancelled = EditorUtility.DisplayCancelableProgressBar(
                        "批量处理VRM模型",
                        $"正在处理: {batchVrmModels[i].name} ({i + 1}/{totalCount})",
                        progress);

                    if (cancelled)
                    {
                        EditorUtility.ClearProgressBar();
                        EditorUtility.DisplayDialog("已取消", $"批量处理已取消，已处理 {processedCount} 个模型", "确定");
                        return;
                    }

                    try
                    {
                        // 设置当前模型
                        currentBatchIndex = i;
                        sourceVrmInstance = batchVrmModels[i]?.GetComponent<Vrm10Instance>();

                        if (sourceVrmInstance != null)
                        {
                            // 检测服装
                            DetectClothingComponents();

                            // 如果有检测到的服装，自动选择所有服装进行提取
                            if (detectedClothes.Count > 0)
                            {
                                // 自动选择所有服装
                                foreach (var item in detectedClothes)
                                {
                                    item.selected = true;
                                    // 如果启用了VRoid骨骼修复，应用智能推荐
                                    if (enableVRoidBoneFix)
                                    {
                                        item.fixVRoidBones = ShouldRecommendVRoidFix(item);
                                    }
                                }

                                // 提取服装
                                ExtractSelectedClothes();
                                processedCount++;

                                Debug.Log($"[VRM10ClothExtractor] 批量处理: {batchVrmModels[i].name} 完成，提取了 {detectedClothes.Count} 个服装");
                            }
                            else
                            {
                                Debug.Log($"[VRM10ClothExtractor] 批量处理: {batchVrmModels[i].name} 没有检测到服装");
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"[VRM10ClothExtractor] 处理模型 {batchVrmModels[i].name} 失败: {e.Message}");
                    }
                }

                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("批量处理完成",
                    $"批量处理完成！\n\n成功处理: {processedCount}/{totalCount} 个模型", "确定");

                Debug.Log($"[VRM10ClothExtractor] 批量处理完成: {processedCount}/{totalCount}");
            }
            catch (System.Exception e)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError($"[VRM10ClothExtractor] 批量处理失败: {e.Message}");
                EditorUtility.DisplayDialog("处理失败", $"批量处理过程出错: {e.Message}", "确定");
            }
        }

        /// <summary>
        /// 自动修复提取的服装
        /// </summary>
        private void AutoFixExtractedClothes(List<ClothingItem> clothesToFix)
        {
            try
            {
                // 获取VRM10BoneExtractor的类型
                var boneExtractorType = System.Type.GetType("VRoidFaceCustomization.Editor.VRM10BoneExtractor");
                if (boneExtractorType == null)
                {
                    Debug.LogError("[VRM10ClothExtractor] 找不到VRM10BoneExtractor类");
                    return;
                }

                foreach (var item in clothesToFix)
                {
                    try
                    {
                        // 获取提取后的服装Prefab路径
                        string clothPath = GetExtractedClothPath(item);
                        if (string.IsNullOrEmpty(clothPath))
                        {
                            Debug.LogWarning($"[VRM10ClothExtractor] 找不到提取的服装: {item.customName}");
                            continue;
                        }

                        // 加载提取的服装Prefab
                        GameObject extractedCloth = AssetDatabase.LoadAssetAtPath<GameObject>(clothPath);
                        if (extractedCloth == null)
                        {
                            Debug.LogWarning($"[VRM10ClothExtractor] 无法加载服装Prefab: {clothPath}");
                            continue;
                        }

                        // 调用VRM10BoneExtractor的修复逻辑
                        CallBoneExtractorFix(extractedCloth, item);

                        Debug.Log($"[VRM10ClothExtractor] 自动修复完成: {item.customName}");
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"[VRM10ClothExtractor] 自动修复失败 {item.customName}: {e.Message}");
                    }
                }

                // 刷新资源
                AssetDatabase.Refresh();

                Debug.Log($"[VRM10ClothExtractor] 自动修复完成，共处理 {clothesToFix.Count} 个服装");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothExtractor] 自动修复过程出错: {e.Message}");
            }
        }

        /// <summary>
        /// 获取提取的服装Prefab路径
        /// </summary>
        private string GetExtractedClothPath(ClothingItem item)
        {
            string fileName = string.IsNullOrEmpty(item.customName) ? item.name : item.customName;
            return Path.Combine(outputPath, $"{fileName}.prefab");
        }

        /// <summary>
        /// 调用VRM10BoneExtractor的修复逻辑
        /// </summary>
        private void CallBoneExtractorFix(GameObject extractedCloth, ClothingItem originalItem)
        {
            try
            {
                // 获取服装的SkinnedMeshRenderer
                var renderer = extractedCloth.GetComponent<SkinnedMeshRenderer>();
                if (renderer == null || renderer.bones == null)
                {
                    Debug.LogWarning($"[VRM10ClothExtractor] 服装没有有效的SkinnedMeshRenderer: {extractedCloth.name}");
                    return;
                }

                // 创建骨骼数据
                var boneInfos = new List<VRoidFaceCustomization.ClothBoneInfo>();

                // 获取正确的源模型（优先使用item中记录的源模型）
                Vrm10Instance currentSourceVrm = originalItem.sourceModel ?? sourceVrmInstance;
                if (currentSourceVrm == null)
                {
                    Debug.LogWarning($"[VRM10ClothExtractor] 无法找到源VRM模型: {extractedCloth.name}");
                    return;
                }

                // 获取源模型的骨骼映射
                var sourceBones = currentSourceVrm.GetComponentsInChildren<Transform>();
                var sourceBoneMap = sourceBones.ToDictionary(b => b.name, b => b);

                Transform rootBone = renderer.rootBone;
                if (rootBone == null && renderer.bones.Length > 0)
                {
                    // 查找根骨骼
                    string[] rootBoneNames = { "J_Bip_C_Hips", "Hips", "Root", "Armature" };
                    foreach (string rootName in rootBoneNames)
                    {
                        if (sourceBoneMap.TryGetValue(rootName, out Transform root))
                        {
                            rootBone = root;
                            break;
                        }
                    }
                }

                for (int i = 0; i < renderer.bones.Length; i++)
                {
                    var bone = renderer.bones[i];
                    if (bone == null) continue;

                    // 在源模型中查找对应的骨骼
                    if (!sourceBoneMap.TryGetValue(bone.name, out Transform sourceBone))
                    {
                        Debug.LogWarning($"在源模型中找不到骨骼: {bone.name}");
                        continue;
                    }

                    // 创建骨骼信息
                    bool isVRoid = IsVRoidSpecificBone(bone.name);
                    var boneInfo = VRoidFaceCustomization.ClothBoneInfo.CreateFromTransform(sourceBone, rootBone, i, isVRoid);
                    boneInfos.Add(boneInfo);
                }

                // 添加VRM10ClothBoneData组件
                var boneData = extractedCloth.GetComponent<VRoidFaceCustomization.VRM10ClothBoneData>();
                if (boneData == null)
                {
                    boneData = extractedCloth.AddComponent<VRoidFaceCustomization.VRM10ClothBoneData>();
                }

                // 创建绑定姿势数据
                var bindposes = new List<Matrix4x4>();
                for (int i = 0; i < renderer.bones.Length; i++)
                {
                    if (renderer.sharedMesh != null && i < renderer.sharedMesh.bindposes.Length)
                    {
                        bindposes.Add(renderer.sharedMesh.bindposes[i]);
                    }
                    else
                    {
                        // 计算绑定姿势
                        var bone = renderer.bones[i];
                        if (bone != null)
                        {
                            var bindpose = bone.worldToLocalMatrix * renderer.transform.localToWorldMatrix;
                            bindposes.Add(bindpose);
                        }
                        else
                        {
                            bindposes.Add(Matrix4x4.identity);
                        }
                    }
                }

                // 设置骨骼数据
                boneData.SetBoneData(boneInfos, bindposes,
                    rootBone != null ? rootBone.name : "",
                    currentSourceVrm.name);

                // 验证数据
                if (!boneData.ValidateBoneData())
                {
                    Debug.LogWarning($"[VRM10ClothExtractor] 骨骼数据验证失败: {extractedCloth.name}");
                }

                // 标记为已修改
                EditorUtility.SetDirty(extractedCloth);

                Debug.Log($"[VRM10ClothExtractor] 为 {extractedCloth.name} 添加了 {boneInfos.Count} 个骨骼数据");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[VRM10ClothExtractor] 调用骨骼修复失败: {e.Message}");
            }
        }

        /// <summary>
        /// 智能选择父骨骼
        /// </summary>
        private Transform GetSmartParentBone(string boneName, Dictionary<string, Transform> targetBoneMap, Transform defaultParent)
        {
            // 根据骨骼名称特征选择最合适的父骨骼
            if (boneName.Contains("CoatSkirt") || boneName.Contains("Skirt"))
            {
                if (targetBoneMap.TryGetValue("J_Bip_C_Hips", out Transform hips)) return hips;
                if (targetBoneMap.TryGetValue("Hips", out Transform hips2)) return hips2;
            }
            else if (boneName.Contains("Bust"))
            {
                if (targetBoneMap.TryGetValue("J_Bip_C_Chest", out Transform chest)) return chest;
                if (targetBoneMap.TryGetValue("Chest", out Transform chest2)) return chest2;
            }
            else if (boneName.Contains("Hair"))
            {
                if (targetBoneMap.TryGetValue("J_Bip_C_Head", out Transform head)) return head;
                if (targetBoneMap.TryGetValue("Head", out Transform head2)) return head2;
            }
            else if (boneName.Contains("_L_") || boneName.Contains("Left"))
            {
                if (targetBoneMap.TryGetValue("J_Bip_L_Shoulder", out Transform leftShoulder)) return leftShoulder;
                if (targetBoneMap.TryGetValue("LeftShoulder", out Transform leftShoulder2)) return leftShoulder2;
            }
            else if (boneName.Contains("_R_") || boneName.Contains("Right"))
            {
                if (targetBoneMap.TryGetValue("J_Bip_R_Shoulder", out Transform rightShoulder)) return rightShoulder;
                if (targetBoneMap.TryGetValue("RightShoulder", out Transform rightShoulder2)) return rightShoulder2;
            }

            return defaultParent;
        }

        /// <summary>
        /// 重置骨骼映射
        /// </summary>
        private void ResetBoneMappings()
        {
            // 强制更新Transform系统
            Canvas.ForceUpdateCanvases();

            // 查找场景中的VRM10ClothBinder组件并重置骨骼映射
            var clothBinders = FindObjectsOfType<VRoidFaceCustomization.VRM10ClothBinder>();

            foreach (var clothBinder in clothBinders)
            {
                // 重置骨骼映射（这是关键的修复步骤）
                var resetMethod = clothBinder.GetType().GetMethod("ResetBoneMapping",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

                if (resetMethod != null)
                {
                    resetMethod.Invoke(clothBinder, null);
                    Debug.Log($"[VRM10ClothExtractor] 重置骨骼映射完成: {clothBinder.name}");
                }
            }

            if (clothBinders.Length == 0)
            {
                Debug.Log("[VRM10ClothExtractor] 场景中没有找到VRM10ClothBinder，骨骼状态已在源模型上修正");
            }
        }








        
        /// <summary>
        /// 提取单个服装组件
        /// </summary>
        private void ExtractSingleCloth(ClothingItem item)
        {
            // 创建新的GameObject
            GameObject clothObject = new GameObject(item.customName);

            // 确定源VRM实例（优先使用item中记录的源模型）
            Vrm10Instance currentSourceVrm = item.sourceModel ?? sourceVrmInstance;
            
            // 复制SkinnedMeshRenderer
            var originalRenderer = item.renderer;
            var newRenderer = clothObject.AddComponent<SkinnedMeshRenderer>();
            
            // 复制网格和材质
            newRenderer.sharedMesh = originalRenderer.sharedMesh;
            
            if (createMaterialCopies)
            {
                // 创建材质副本
                var newMaterials = new Material[originalRenderer.sharedMaterials.Length];
                for (int i = 0; i < originalRenderer.sharedMaterials.Length; i++)
                {
                    if (originalRenderer.sharedMaterials[i] != null)
                    {
                        string matPath = Path.Combine(outputPath, $"{item.customName}_Mat_{i}.mat");
                        newMaterials[i] = new Material(originalRenderer.sharedMaterials[i]);
                        AssetDatabase.CreateAsset(newMaterials[i], matPath);
                    }
                }
                newRenderer.sharedMaterials = newMaterials;
            }
            else
            {
                newRenderer.sharedMaterials = originalRenderer.sharedMaterials;
            }
            
            // 复制骨骼引用（简化处理）
            newRenderer.bones = originalRenderer.bones;
            newRenderer.rootBone = originalRenderer.rootBone;
            
            // 添加VRM 1.0服装信息组件
            var clothInfo = clothObject.AddComponent<VRM10ClothInfo>();
            clothInfo.clothType = item.type;
            clothInfo.originalName = item.name;
            clothInfo.sourceModel = sourceVrmInstance.name;
            
            // 保留SpringBone组件（如果需要）
            if (preserveSpringBones)
            {
                CopySpringBoneComponents(originalRenderer.gameObject, clothObject);
            }
            
            // 创建Prefab
            string prefabPath = Path.Combine(outputPath, $"{item.customName}.prefab");
            PrefabUtility.SaveAsPrefabAsset(clothObject, prefabPath);
            
            // 清理临时对象
            DestroyImmediate(clothObject);
            
            Debug.Log($"[VRM10ClothExtractor] 成功提取: {item.customName} -> {prefabPath}");
        }
        
        /// <summary>
        /// 复制SpringBone组件
        /// </summary>
        private void CopySpringBoneComponents(GameObject source, GameObject target)
        {
            // 复制VRM 1.0的SpringBone相关组件
            var springBoneJoints = source.GetComponentsInChildren<VRM10SpringBoneJoint>();
            var springBoneColliders = source.GetComponentsInChildren<VRM10SpringBoneCollider>();
            var springBoneColliderGroups = source.GetComponentsInChildren<VRM10SpringBoneColliderGroup>();
            
            // 这里需要根据实际需求实现SpringBone组件的复制逻辑
            // 由于SpringBone系统比较复杂，可能需要特殊处理
            
            if (springBoneJoints.Length > 0 || springBoneColliders.Length > 0)
            {
                Debug.Log($"[VRM10ClothExtractor] 检测到SpringBone组件，需要手动处理");
            }
        }
    }
}
