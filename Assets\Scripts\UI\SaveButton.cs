using UnityEngine;
using UnityEngine.UI;
using VRoidFaceCustomization.Core;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization.UI
{
    /// <summary>
    /// 保存按钮 - 处理角色数据保存功能
    /// </summary>
    [RequireComponent(typeof(Button))]
    public class SaveButton : MonoBehaviour
    {
        [Header("VRM设置")]
        [SerializeField] private GameObject targetVRMObject; // 目标VRM对象
        [SerializeField] private bool autoFindVRM = true; // 自动查找VRM对象
        
        [Header("保存设置")]
        [SerializeField] private string characterName = ""; // 角色名称
        [SerializeField] private bool saveToFile = true; // 是否保存到文件
        [SerializeField] private bool useTimestamp = true; // 是否使用时间戳
        
        [Header("UI反馈")]
        [SerializeField] private Text statusText; // 状态文本
        [SerializeField] private string savingText = "保存中...";
        [SerializeField] private string successText = "保存成功！";
        [SerializeField] private string failedText = "保存失败！";
        [SerializeField] private float statusDisplayTime = 2f; // 状态显示时间
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        
        private Button saveButton;
        private bool isSaving = false;
        private bool fileOperationSuccess = false;
        
        #region Unity生命周期
        private void Awake()
        {
            saveButton = GetComponent<Button>();
            
            if (saveButton == null)
            {
                LogDebug("❌ 未找到Button组件");
                return;
            }
            
            // 绑定点击事件
            saveButton.onClick.AddListener(OnSaveButtonClicked);
            LogDebug("✅ SaveButton 初始化完成");
        }
        
        private void Start()
        {
            // 自动查找VRM对象
            if (autoFindVRM && targetVRMObject == null)
            {
                FindVRMObject();
            }
            
            // 初始化UI状态
            UpdateButtonState();
        }
        
        private void OnDestroy()
        {
            if (saveButton != null)
            {
                saveButton.onClick.RemoveListener(OnSaveButtonClicked);
            }
        }
        #endregion
        
        #region 保存功能
        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void OnSaveButtonClicked()
        {
            if (isSaving)
            {
                LogDebug("⚠️ 正在保存中，请稍候");
                return;
            }
            
            if (targetVRMObject == null)
            {
                LogDebug("❌ 未设置目标VRM对象");
                ShowStatus(failedText);
                return;
            }
            
            LogDebug("🚀 开始保存角色数据");
            StartCoroutine(SaveCharacterDataCoroutine());
        }
        
        /// <summary>
        /// 保存角色数据的协程
        /// </summary>
        private System.Collections.IEnumerator SaveCharacterDataCoroutine()
        {
            isSaving = true;
            UpdateButtonState();
            ShowStatus(savingText);

            // 执行保存流程
            yield return StartCoroutine(ExecuteSaveProcess());

            isSaving = false;
            UpdateButtonState();
        }

        private System.Collections.IEnumerator ExecuteSaveProcess()
        {
            CharacterParameters parameters = null;
            string errorMessage = "";
            bool success = false;

            try
            {
                // 获取数据收集器
                var collector = GetOrCreateDataCollector();

                // 收集角色参数
                LogDebug("📊 收集角色参数...");
                parameters = collector.CollectFromVRM(targetVRMObject);

                if (parameters == null || !parameters.IsValid())
                {
                    LogDebug("❌ 角色参数收集失败");
                    ShowStatus(failedText);
                    yield break;
                }

                success = true;
            }
            catch (System.Exception e)
            {
                errorMessage = e.Message;
                LogDebug($"❌ 保存过程中发生错误: {errorMessage}");
                ShowStatus(failedText);
                yield break;
            }

            if (success && parameters != null)
            {
                // 继续处理参数
                yield return StartCoroutine(ProcessParametersCoroutine(parameters));
            }
        }

        private System.Collections.IEnumerator ProcessParametersCoroutine(CharacterParameters parameters)
        {
            // 设置角色名称
            if (!string.IsNullOrEmpty(characterName))
            {
                parameters.characterName = characterName;
            }
            else if (string.IsNullOrEmpty(parameters.characterName))
            {
                parameters.characterName = "Character";
            }

            // 添加时间戳
            if (useTimestamp)
            {
                parameters.characterName += "_" + System.DateTime.Now.ToString("MMdd_HHmm");
            }

            LogDebug($"✅ 角色参数收集完成: {parameters.characterName}");
            LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
            LogDebug($"   👔 服装数量: {parameters.clothingData.GetClothingCount()}件");

            // 保存到桥接器（用于场景切换）
            var bridge = CharacterDataBridge.Instance;
            bridge.SetCharacterParameters(parameters);
            LogDebug("📤 数据已保存到桥接器");

            // 可选：保存到文件
            if (saveToFile)
            {
                LogDebug("💾 保存到文件...");
                var persistence = DataPersistence.Instance;
                fileOperationSuccess = false;
                yield return StartCoroutine(SaveToFileCoroutine(persistence, parameters));

                if (fileOperationSuccess)
                {
                    LogDebug("✅ 文件保存成功");
                }
                else
                {
                    LogDebug("⚠️ 文件保存失败，但桥接器数据已保存");
                }
            }

            ShowStatus(successText);
            LogDebug("🎉 角色数据保存完成");

            // 触发保存完成事件
            OnSaveComplete?.Invoke(parameters);
        }
        
        /// <summary>
        /// 保存到文件的协程
        /// </summary>
        private System.Collections.IEnumerator SaveToFileCoroutine(DataPersistence persistence, CharacterParameters parameters)
        {
            bool completed = false;
            bool success = false;
            
            // 启动异步保存任务
            var saveTask = persistence.SaveCharacterAsync(parameters);
            
            // 等待任务完成
            while (!saveTask.IsCompleted)
            {
                yield return null;
            }
            
            fileOperationSuccess = saveTask.Result;
        }
        #endregion
        
        #region UI更新
        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonState()
        {
            if (saveButton != null)
            {
                saveButton.interactable = !isSaving && targetVRMObject != null;
            }
        }
        
        /// <summary>
        /// 显示状态信息
        /// </summary>
        private void ShowStatus(string message)
        {
            if (statusText != null)
            {
                statusText.text = message;
                
                // 自动清除状态文本
                if (statusDisplayTime > 0)
                {
                    StartCoroutine(ClearStatusAfterDelay());
                }
            }
            
            LogDebug($"📢 状态: {message}");
        }
        
        /// <summary>
        /// 延迟清除状态文本
        /// </summary>
        private System.Collections.IEnumerator ClearStatusAfterDelay()
        {
            yield return new WaitForSeconds(statusDisplayTime);
            
            if (statusText != null)
            {
                statusText.text = "";
            }
        }
        #endregion
        
        #region 辅助方法
        /// <summary>
        /// 自动查找VRM对象
        /// </summary>
        private void FindVRMObject()
        {
            // 查找场景中的VRM对象
            var vrmInstances = FindObjectsOfType<UniVRM10.Vrm10Instance>();
            
            if (vrmInstances.Length > 0)
            {
                targetVRMObject = vrmInstances[0].gameObject;
                LogDebug($"🔍 自动找到VRM对象: {targetVRMObject.name}");
            }
            else
            {
                LogDebug("⚠️ 未找到VRM对象");
            }
        }
        
        /// <summary>
        /// 获取或创建数据收集器
        /// </summary>
        private VRMDataCollector GetOrCreateDataCollector()
        {
            // VRMDataCollector现在是普通类，直接创建实例
            LogDebug("🔧 创建数据收集器");
            return new VRMDataCollector();
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[SaveButton] {message}");
            }
        }
        #endregion
        
        #region 公共接口
        /// <summary>
        /// 设置目标VRM对象
        /// </summary>
        public void SetTargetVRM(GameObject vrmObject)
        {
            targetVRMObject = vrmObject;
            UpdateButtonState();
            LogDebug($"🎯 设置目标VRM: {(vrmObject != null ? vrmObject.name : "null")}");
        }
        
        /// <summary>
        /// 设置角色名称
        /// </summary>
        public void SetCharacterName(string name)
        {
            characterName = name;
            LogDebug($"📝 设置角色名称: {name}");
        }
        
        /// <summary>
        /// 手动触发保存
        /// </summary>
        public void TriggerSave()
        {
            OnSaveButtonClicked();
        }
        
        /// <summary>
        /// 检查是否正在保存
        /// </summary>
        public bool IsSaving()
        {
            return isSaving;
        }
        #endregion
        
        #region 事件系统
        /// <summary>
        /// 保存完成事件
        /// </summary>
        public static event System.Action<CharacterParameters> OnSaveComplete;
        
        /// <summary>
        /// 保存开始事件
        /// </summary>
        public static event System.Action OnSaveStart;
        
        /// <summary>
        /// 保存失败事件
        /// </summary>
        public static event System.Action<string> OnSaveFailed;
        #endregion
        
        #region 调试功能
        /// <summary>
        /// 显示当前状态
        /// </summary>
        [ContextMenu("显示当前状态")]
        private void ShowCurrentStatus()
        {
            LogDebug($"目标VRM: {(targetVRMObject != null ? targetVRMObject.name : "null")}");
            LogDebug($"角色名称: {characterName}");
            LogDebug($"正在保存: {isSaving}");
            LogDebug($"按钮可用: {(saveButton != null ? saveButton.interactable.ToString() : "null")}");
        }
        
        /// <summary>
        /// 测试保存功能
        /// </summary>
        [ContextMenu("测试保存")]
        private void TestSave()
        {
            if (Application.isPlaying)
            {
                TriggerSave();
            }
            else
            {
                LogDebug("⚠️ 只能在运行时测试保存功能");
            }
        }
        #endregion
    }
}
