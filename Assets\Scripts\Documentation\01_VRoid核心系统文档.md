# 🎭 VRoid核心系统文档

## 📋 **系统概述**

VRoid核心系统是整个捏脸换装系统的心脏，包含了面部表情控制和动态换装的所有核心功能。系统基于VRM 1.0标准构建，专门优化了与VRoid Studio导出模型的兼容性。

### 🏗️ **架构图**
```
VRM10UnifiedManager (统一管理器)
    ├── VRM10FaceController (面部控制器)
    │   ├── VRM10FacialParameters (参数管理)
    │   └── VRM10FaceControllerWebGLBridge (WebGL桥接)
    │
    ├── VRM10ClothBinder (服装绑定器)
    │   ├── VRM10ClothBoneData (骨骼数据)
    │   ├── VRM10ClothInfo (服装信息)
    │   └── VRM10DualModeCloth (双模式服装)
    │
    └── 辅助组件
        ├── VRM10ModelAnalyzer (模型分析器)
        ├── VRM10SystemValidator (系统验证器)
        ├── VRM10ClothLibrary (服装库)
        └── VRM10SharedTypes (共享类型)
```

---

## 🎯 **核心组件详解**

### 1. **VRM10UnifiedManager.cs** - 系统统一管理器
**文件位置**: `Assets/Scripts/VRoid/VRM10UnifiedManager.cs`  
**脚本行数**: 1016行  
**主要职责**: 整个系统的核心控制器，单例模式设计

#### **主要功能**
- 🎭 **面部表情管理** - 统一的表情控制接口
- 👔 **动态换装管理** - 服装切换和绑定控制
- 📦 **服装库管理** - 自动分类和索引服装资源
- 🎨 **套装预设** - 预定义服装搭配管理
- 🌐 **WebGL支持** - 网页端兼容接口

#### **关键API方法**
```csharp
// 单例访问
VRM10UnifiedManager.Instance

// 面部表情控制
SetFaceExpression(string expressionName, float value)
ResetFacialParameters()

// 动态换装
WearClothByName(string clothName)
WearClothByType(VRM10ClothType type, string clothName)
RemoveClothByType(VRM10ClothType type)
RemoveAllClothes()

// 套装管理
WearOutfitByName(string outfitName)
SaveCurrentAsOutfit(string outfitName)

// 系统状态
IsSystemInitialized()
GetSystemStatus()
```

#### **Inspector配置选项**
- **VRM 1.0 组件**: vrmInstance, faceController, clothBinder
- **管理设置**: autoInitialize, debugMode
- **服装库**: 服装列表、套装预设、路径配置
- **前缀分类规则**: 各类服装的命名前缀

---

### 2. **VRM10FaceController.cs** - 面部表情控制器
**文件位置**: `Assets/Scripts/VRoid/VRM10FaceController.cs`  
**脚本行数**: 233行  
**主要职责**: VRM 1.0模型的面部表情控制

#### **支持的表情类型**
| 表情名称 | 英文标识 | 功能描述 | 参数范围 |
|---------|---------|---------|---------|
| 开心 | happy | 愉快的面部表情 | 0.0-1.0 |
| 愤怒 | angry | 生气的面部表情 | 0.0-1.0 |
| 悲伤 | sad | 悲伤的面部表情 | 0.0-1.0 |
| 放松 | relaxed | 平静放松的表情 | 0.0-1.0 |
| 惊讶 | surprised | 惊讶的面部表情 | 0.0-1.0 |
| 眨眼 | blink | 双眼眨眼动作 | 0.0-1.0 |
| 左眼眨眼 | blinkL | 左眼单独眨眼 | 0.0-1.0 |
| 右眼眨眼 | blinkR | 右眼单独眨眼 | 0.0-1.0 |

#### **使用示例**
```csharp
var faceController = GetComponent<VRM10FaceController>();

// 设置单个表情
faceController.SetExpression("happy", 1.0f);
faceController.SetExpression("blink", 0.5f);

// 批量设置表情
var expressions = new Dictionary<string, float>
{
    {"happy", 0.8f},
    {"surprised", 0.3f}
};
faceController.SetMultipleExpressions(expressions);

// 重置所有表情
faceController.ResetFacialParameters();

// 获取当前表情数据
var facialData = faceController.GetFacialParametersData();
```

#### **事件系统**
```csharp
// 订阅参数变化事件
faceController.OnParameterChanged += (paramName, value) => {
    Debug.Log($"表情参数 {paramName} 变更为 {value}");
};

// 订阅重置事件
faceController.OnParametersReset += () => {
    Debug.Log("所有表情参数已重置");
};
```

---

### 3. **VRM10ClothBinder.cs** - 动态换装绑定器
**文件位置**: `Assets/Scripts/VRoid/VRM10ClothBinder.cs`  
**脚本行数**: 1712行  
**主要职责**: VRM 1.0模型的动态换装系统

#### **支持的服装类型**
```csharp
public enum VRM10ClothType
{
    Top,        // 上衣类 (衬衫、T恤、外套)
    Bottom,     // 下装类 (裤子、裙子、短裤)
    Dress,      // 连衣裙类 (与Top/Bottom冲突)
    Shoes,      // 鞋子类 (运动鞋、高跟鞋、靴子)
    Accessory,  // 配饰类 (包包、饰品、首饰)
    Hat,        // 帽子类 (棒球帽、贝雷帽)
    Gloves,     // 手套类
    Socks       // 袜子类
}
```

#### **核心换装API**
```csharp
var clothBinder = GetComponent<VRM10ClothBinder>();

// 基础换装操作
clothBinder.WearCloth(clothPrefab);
clothBinder.RemoveClothByType(VRM10ClothType.Top);
clothBinder.RemoveAllClothes();

// 批量换装
var clothesToWear = new GameObject[] { shirt, pants, shoes };
clothBinder.WearMultipleClothes(clothesToWear);

// 查询穿戴状态
var wornClothes = clothBinder.GetWornClothes();
bool isWearingDress = clothBinder.IsWearingClothType(VRM10ClothType.Dress);
var topCloth = clothBinder.GetClothByType(VRM10ClothType.Top);

// 高级功能
clothBinder.SetClothVisibility(clothPrefab, false);
clothBinder.UpdateAllBounds();
clothBinder.ValidateAllBindings();
```

#### **VRoid专用设置**
- **enableVRoidMode**: 启用VRoid优化模式，提高兼容性
- **createMissingBones**: 自动创建缺失的中间骨骼
- **minimumSuccessRate**: 最低绑定成功率 (0.6)
- **forceSyncBinding**: 强制同步绑定处理
- **preInitializeBones**: 预初始化骨骼结构

#### **服装冲突规则**
```csharp
// 自动冲突检测
if (新服装类型 == VRM10ClothType.Dress)
{
    // 自动移除上衣和下装
    RemoveClothByType(VRM10ClothType.Top);
    RemoveClothByType(VRM10ClothType.Bottom);
}
else if (新服装类型 == VRM10ClothType.Top || 新服装类型 == VRM10ClothType.Bottom)
{
    // 自动移除连衣裙
    RemoveClothByType(VRM10ClothType.Dress);
}
```

---

### 4. **VRM10FacialParameters.cs** - 面部参数管理
**文件位置**: `Assets/Scripts/VRoid/VRM10FacialParameters.cs`  
**脚本行数**: 290行  
**主要职责**: 面部表情参数的数据结构和管理

#### **数据结构**
```csharp
[System.Serializable]
public class VRM10FacialParameters
{
    [Header("VRM标准表情")]
    public List<VRM10ExpressionParameter> expressions;
    
    [Header("自定义BlendShape")]
    public List<BlendShapeParameter> blendShapes;
    
    [Header("面部参数")]
    public List<FacialParameter> facialParameters;
}

[System.Serializable]
public class VRM10ExpressionParameter
{
    public string name;
    public float currentWeight;
    public float targetWeight;
    public float minValue;
    public float maxValue;
    public bool isActive;
    public string description;
}
```

#### **使用方法**
```csharp
var facialParams = GetComponent<VRM10FacialParameters>();

// 获取表情参数
var happyParam = facialParams.GetExpressionParameter("happy");

// 设置参数值
facialParams.SetExpressionWeight("happy", 0.8f);

// 批量操作
facialParams.ResetAllParameters();
facialParams.RandomizeParameters();

// 数据序列化
var paramData = facialParams.SerializeToJson();
facialParams.DeserializeFromJson(paramData);
```

---

### 5. **VRM10ClothBoneData.cs** - 服装骨骼数据
**文件位置**: `Assets/Scripts/VRoid/VRM10ClothBoneData.cs`  
**脚本行数**: 237行  
**主要职责**: 服装骨骼绑定数据的管理

#### **核心数据结构**
```csharp
[System.Serializable]
public class VRM10ClothBoneData
{
    [Header("骨骼绑定数据")]
    public List<BoneBindingInfo> boneBindings;
    public List<string> requiredBones;
    public List<string> optionalBones;
    public Transform rootBone;
    
    [Header("绑定设置")]
    public VRM10ClothType clothType;
    public float bindingSuccessRate;
    public bool usePhysicsBones;
    public bool preserveOriginalBones;
}

[System.Serializable]
public class BoneBindingInfo
{
    public string boneName;
    public Transform originalBone;
    public Transform targetBone;
    public Vector3 localPosition;
    public Quaternion localRotation;
    public Vector3 localScale;
    public bool isRequired;
    public float bindingWeight;
}
```

---

### 6. **辅助组件**

#### **VRM10ModelAnalyzer.cs** - 模型分析器
- **功能**: 自动分析VRM模型结构，检测骨骼和BlendShape
- **API**: `AnalyzeVRMModel()`, `GetModelInfo()`, `ValidateModel()`

#### **VRM10SystemValidator.cs** - 系统验证器
- **功能**: 验证系统组件完整性和配置正确性
- **API**: `ValidateSystem()`, `CheckComponents()`, `GetValidationReport()`

#### **VRM10ClothLibrary.cs** - 服装库管理
- **功能**: 管理和索引服装Prefab资源
- **API**: `LoadClothLibrary()`, `GetClothByName()`, `RefreshLibrary()`

#### **VRM10SharedTypes.cs** - 共享类型定义
- **功能**: 定义系统中使用的共享数据类型和枚举
- **包含**: `VRM10ClothType`, `BindingResult`, `SystemState`等

---

## 🚀 **快速开始指南**

### 步骤1: 系统设置
```csharp
// 1. 在场景中添加VRM10UnifiedManager
var manager = GameObject.FindObjectOfType<VRM10UnifiedManager>();
if (manager == null)
{
    var go = new GameObject("VRM10UnifiedManager");
    manager = go.AddComponent<VRM10UnifiedManager>();
}

// 2. 设置VRM模型引用
manager.vrmInstance = GetComponent<Vrm10Instance>();
```

### 步骤2: 面部表情控制
```csharp
// 基础表情设置
VRM10UnifiedManager.Instance.SetFaceExpression("happy", 1.0f);
VRM10UnifiedManager.Instance.SetFaceExpression("blink", 0.5f);

// 批量表情控制
var expressions = new Dictionary<string, float>
{
    {"happy", 0.8f},
    {"surprised", 0.2f}
};
VRM10UnifiedManager.Instance.SetMultipleFaceExpressions(expressions);
```

### 步骤3: 动态换装
```csharp
// 基础换装
VRM10UnifiedManager.Instance.WearClothByName("BlueDress");

// 分类换装
VRM10UnifiedManager.Instance.WearClothByType(VRM10ClothType.Top, "WhiteShirt");
VRM10UnifiedManager.Instance.WearClothByType(VRM10ClothType.Bottom, "JeansPants");

// 套装换装
VRM10UnifiedManager.Instance.WearOutfitByName("CasualOutfit");
```

---

## ⚠️ **注意事项和最佳实践**

### 性能优化建议
1. **批量操作**: 大量换装时使用`WearMultipleClothes()`而非单个调用
2. **延迟初始化**: 非必需时延迟加载服装库
3. **边界更新**: 换装后调用`UpdateAllBounds()`优化渲染
4. **SpringBone管理**: 根据需要选择性保留物理骨骼

### 兼容性注意
1. **VRoid模式**: 使用VRoid Studio模型时启用`enableVRoidMode`
2. **骨骼要求**: 确保服装Prefab包含完整的骨骼结构
3. **命名规范**: 使用标准的VRM骨骼命名约定
4. **版本兼容**: 确保使用VRM 1.0格式的模型

### 故障排除
1. **换装失败**: 检查骨骼映射成功率，降低`minimumSuccessRate`
2. **表情无效**: 验证VRM模型包含所需的表情BlendShape
3. **性能问题**: 减少同时穿戴的服装数量，优化SpringBone
4. **WebGL问题**: 使用WebGL专用接口，避免线程相关操作

---

## 📚 **相关文档**
- [数据管理系统文档](02_数据管理系统文档.md)
- [编辑器工具文档](03_编辑器工具文档.md)
- [API参考文档](07_API参考文档.md)
- [常见问题解答](08_FAQ问题解答.md)

---

**版本**: 1.0.0  
**最后更新**: 2025-01-22  
**维护者**: VRoid系统开发团队 