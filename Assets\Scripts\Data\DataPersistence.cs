using UnityEngine;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using VRoidFaceCustomization.Core;

namespace VRoidFaceCustomization.Data
{
    /// <summary>
    /// 数据持久化管理器 - 处理角色参数的保存和加载
    /// 使用JSON格式进行数据序列化
    /// </summary>
    public class DataPersistence : MonoBehaviour
    {
        [Header("存储设置")]
        [SerializeField] private string saveDirectory = "CharacterData";
        [SerializeField] private string fileExtension = ".json";
        [SerializeField] private bool useCompression = false;
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        
        [Header("当前状态")]
        [SerializeField] private string fullSavePath = "";
        
        #region 单例模式
        private static DataPersistence _instance;
        public static DataPersistence Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<DataPersistence>();
                    if (_instance == null)
                    {
                        GameObject persistenceObject = new GameObject("DataPersistence");
                        _instance = persistenceObject.AddComponent<DataPersistence>();
                        DontDestroyOnLoad(persistenceObject);
                        Debug.Log("✅ [DataPersistence] 创建新实例");
                    }
                }
                return _instance;
            }
        }
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSavePath();
                LogDebug("✅ DataPersistence 初始化完成");
            }
            else if (_instance != this)
            {
                LogDebug("🗑️ 销毁重复的DataPersistence实例");
                Destroy(gameObject);
            }
        }
        #endregion
        
        #region 初始化
        /// <summary>
        /// 初始化保存路径
        /// </summary>
        private void InitializeSavePath()
        {
            fullSavePath = Path.Combine(Application.persistentDataPath, saveDirectory);
            
            // 确保目录存在
            if (!Directory.Exists(fullSavePath))
            {
                Directory.CreateDirectory(fullSavePath);
                LogDebug($"📁 创建保存目录: {fullSavePath}");
            }
            else
            {
                LogDebug($"📁 使用现有保存目录: {fullSavePath}");
            }
        }
        #endregion
        
        #region 保存功能
        /// <summary>
        /// 保存角色参数到文件
        /// </summary>
        public async Task<bool> SaveCharacterAsync(CharacterParameters parameters, string fileName = null)
        {
            if (parameters == null || !parameters.IsValid())
            {
                LogDebug("❌ 参数数据无效，无法保存");
                return false;
            }
            
            try
            {
                // 生成文件名
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = GenerateFileName(parameters);
                }
                
                string filePath = Path.Combine(fullSavePath, fileName + fileExtension);
                
                LogDebug($"💾 开始保存角色参数: {parameters.characterName}");
                LogDebug($"   📁 文件路径: {filePath}");
                
                // 序列化为JSON
                string jsonData = await SerializeToJsonAsync(parameters);
                
                if (string.IsNullOrEmpty(jsonData))
                {
                    LogDebug("❌ JSON序列化失败");
                    return false;
                }
                
                // 写入文件
                await File.WriteAllTextAsync(filePath, jsonData);
                
                LogDebug($"✅ 角色参数保存成功");
                LogDebug($"   📊 文件大小: {new FileInfo(filePath).Length / 1024}KB");
                LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
                LogDebug($"   📊 服装数量: {parameters.clothingData.GetClothingCount()}件");
                
                return true;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 保存失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 同步保存角色参数
        /// </summary>
        public bool SaveCharacter(CharacterParameters parameters, string fileName = null)
        {
            var task = SaveCharacterAsync(parameters, fileName);
            task.Wait();
            return task.Result;
        }
        #endregion
        
        #region 加载功能
        /// <summary>
        /// 从文件加载角色参数
        /// </summary>
        public async Task<CharacterParameters> LoadCharacterAsync(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                LogDebug("❌ 文件名为空，无法加载");
                return null;
            }
            
            try
            {
                string filePath = Path.Combine(fullSavePath, fileName + fileExtension);
                
                if (!File.Exists(filePath))
                {
                    LogDebug($"❌ 文件不存在: {filePath}");
                    return null;
                }
                
                LogDebug($"📂 开始加载角色参数: {fileName}");
                LogDebug($"   📁 文件路径: {filePath}");
                
                // 读取文件
                string jsonData = await File.ReadAllTextAsync(filePath);
                
                if (string.IsNullOrEmpty(jsonData))
                {
                    LogDebug("❌ 文件内容为空");
                    return null;
                }
                
                // 反序列化JSON
                var parameters = await DeserializeFromJsonAsync(jsonData);
                
                if (parameters == null || !parameters.IsValid())
                {
                    LogDebug("❌ 数据反序列化失败或数据无效");
                    return null;
                }
                
                LogDebug($"✅ 角色参数加载成功: {parameters.characterName}");
                LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
                LogDebug($"   📊 服装数量: {parameters.clothingData.GetClothingCount()}件");
                
                return parameters;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 加载失败: {e.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 同步加载角色参数
        /// </summary>
        public CharacterParameters LoadCharacter(string fileName)
        {
            var task = LoadCharacterAsync(fileName);
            task.Wait();
            return task.Result;
        }
        #endregion
        
        #region 文件管理
        /// <summary>
        /// 获取所有保存的角色文件列表
        /// </summary>
        public string[] GetSavedCharacterFiles()
        {
            try
            {
                if (!Directory.Exists(fullSavePath))
                {
                    return new string[0];
                }
                
                var files = Directory.GetFiles(fullSavePath, "*" + fileExtension);
                var fileNames = new string[files.Length];
                
                for (int i = 0; i < files.Length; i++)
                {
                    fileNames[i] = Path.GetFileNameWithoutExtension(files[i]);
                }
                
                LogDebug($"📋 找到 {fileNames.Length} 个保存的角色文件");
                return fileNames;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 获取文件列表失败: {e.Message}");
                return new string[0];
            }
        }
        
        /// <summary>
        /// 删除角色文件
        /// </summary>
        public bool DeleteCharacterFile(string fileName)
        {
            try
            {
                string filePath = Path.Combine(fullSavePath, fileName + fileExtension);
                
                if (!File.Exists(filePath))
                {
                    LogDebug($"⚠️ 文件不存在，无需删除: {fileName}");
                    return true;
                }
                
                File.Delete(filePath);
                LogDebug($"🗑️ 角色文件已删除: {fileName}");
                return true;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 删除文件失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        public bool FileExists(string fileName)
        {
            string filePath = Path.Combine(fullSavePath, fileName + fileExtension);
            return File.Exists(filePath);
        }
        #endregion
        
        #region 序列化
        /// <summary>
        /// 异步序列化为JSON
        /// </summary>
        private async Task<string> SerializeToJsonAsync(CharacterParameters parameters)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var settings = new JsonSerializerSettings
                    {
                        Formatting = Formatting.Indented,
                        NullValueHandling = NullValueHandling.Ignore,
                        DefaultValueHandling = DefaultValueHandling.Ignore
                    };
                    
                    return JsonConvert.SerializeObject(parameters, settings);
                }
                catch (System.Exception e)
                {
                    LogDebug($"❌ JSON序列化失败: {e.Message}");
                    return null;
                }
            });
        }
        
        /// <summary>
        /// 异步从JSON反序列化
        /// </summary>
        private async Task<CharacterParameters> DeserializeFromJsonAsync(string jsonData)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var settings = new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore,
                        DefaultValueHandling = DefaultValueHandling.Ignore
                    };
                    
                    return JsonConvert.DeserializeObject<CharacterParameters>(jsonData, settings);
                }
                catch (System.Exception e)
                {
                    LogDebug($"❌ JSON反序列化失败: {e.Message}");
                    return null;
                }
            });
        }
        #endregion
        
        #region 辅助方法
        /// <summary>
        /// 生成文件名
        /// </summary>
        private string GenerateFileName(CharacterParameters parameters)
        {
            string baseName = string.IsNullOrEmpty(parameters.characterName) ? "Character" : parameters.characterName;
            string timestamp = System.DateTime.Now.ToString("yyyyMMdd_HHmmss");
            return $"{baseName}_{timestamp}";
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[DataPersistence] {message}");
            }
        }
        #endregion
        
        #region 调试功能
        /// <summary>
        /// 显示保存目录信息
        /// </summary>
        [ContextMenu("显示保存目录信息")]
        private void ShowSaveDirectoryInfo()
        {
            LogDebug($"保存目录: {fullSavePath}");
            LogDebug($"目录存在: {Directory.Exists(fullSavePath)}");
            
            var files = GetSavedCharacterFiles();
            LogDebug($"保存的文件数量: {files.Length}");
            
            foreach (var file in files)
            {
                LogDebug($"  - {file}");
            }
        }
        
        /// <summary>
        /// 清理所有保存文件
        /// </summary>
        [ContextMenu("清理所有保存文件")]
        private void ClearAllSaveFiles()
        {
            var files = GetSavedCharacterFiles();
            int deletedCount = 0;
            
            foreach (var file in files)
            {
                if (DeleteCharacterFile(file))
                {
                    deletedCount++;
                }
            }
            
            LogDebug($"🗑️ 已清理 {deletedCount} 个保存文件");
        }
        #endregion
    }
}
