using UnityEngine;
using VRoidFaceCustomization;

namespace VRoidFaceCustomization.Tests
{
    /// <summary>
    /// VRM10FaceController的测试脚本
    /// 用于验证编辑模式下的材质警告修复
    /// </summary>
    public class VRM10FaceControllerTest : MonoBehaviour
    {
        [Header("测试设置")]
        public VRM10FaceController faceController;
        public bool testInEditMode = true;
        public bool showDebugLogs = true;

        [Header("测试结果")]
        [SerializeField] private bool initializationSuccessful = false;
        [SerializeField] private int expressionCount = 0;
        [SerializeField] private string lastError = "";

        void Start()
        {
            if (Application.isPlaying)
            {
                RunTests();
            }
        }

        [ContextMenu("运行测试")]
        public void RunTests()
        {
            Debug.Log("[VRM10FaceControllerTest] 开始测试...");
            
            if (faceController == null)
            {
                faceController = GetComponent<VRM10FaceController>();
                if (faceController == null)
                {
                    lastError = "未找到VRM10FaceController组件";
                    Debug.LogError($"[VRM10FaceControllerTest] {lastError}");
                    return;
                }
            }

            TestInitialization();
            TestExpressionSetting();
            
            Debug.Log("[VRM10FaceControllerTest] 测试完成");
        }

        private void TestInitialization()
        {
            Debug.Log("[VRM10FaceControllerTest] 测试初始化...");
            
            try
            {
                faceController.InitializeFaceController();
                initializationSuccessful = faceController.IsInitialized();
                
                if (initializationSuccessful)
                {
                    var facialParams = faceController.GetFacialParameters();
                    if (facialParams != null)
                    {
                        expressionCount = facialParams.standardExpressions.Count;
                        Debug.Log($"[VRM10FaceControllerTest] 初始化成功，表情数量: {expressionCount}");
                    }
                }
                else
                {
                    lastError = "初始化失败";
                    Debug.LogWarning($"[VRM10FaceControllerTest] {lastError}");
                }
            }
            catch (System.Exception e)
            {
                lastError = e.Message;
                Debug.LogError($"[VRM10FaceControllerTest] 初始化异常: {e.Message}");
            }
        }

        private void TestExpressionSetting()
        {
            if (!initializationSuccessful)
            {
                Debug.LogWarning("[VRM10FaceControllerTest] 跳过表情测试，初始化未成功");
                return;
            }

            Debug.Log("[VRM10FaceControllerTest] 测试表情设置...");
            
            try
            {
                // 测试标准表情
                string[] testExpressions = { "happy", "sad", "angry", "surprised" };
                
                foreach (var expr in testExpressions)
                {
                    faceController.SetExpression(expr, 0.5f);
                    Debug.Log($"[VRM10FaceControllerTest] 设置表情 {expr} = 0.5");
                }
                
                // 重置
                faceController.ResetFacialParameters();
                Debug.Log("[VRM10FaceControllerTest] 重置表情参数");
            }
            catch (System.Exception e)
            {
                lastError = e.Message;
                Debug.LogError($"[VRM10FaceControllerTest] 表情设置异常: {e.Message}");
            }
        }

        [ContextMenu("强制完整初始化测试")]
        public void TestForceFullInitialization()
        {
            if (faceController == null) return;
            
            Debug.Log("[VRM10FaceControllerTest] 测试强制完整初始化...");
            
            try
            {
                faceController.ForceFullInitialization();
                Debug.Log("[VRM10FaceControllerTest] 强制完整初始化完成");
            }
            catch (System.Exception e)
            {
                lastError = e.Message;
                Debug.LogError($"[VRM10FaceControllerTest] 强制初始化异常: {e.Message}");
            }
        }

        void OnValidate()
        {
            if (testInEditMode && !Application.isPlaying)
            {
                // 在编辑模式下自动运行测试
                if (faceController != null)
                {
                    RunTests();
                }
            }
        }
    }
}
