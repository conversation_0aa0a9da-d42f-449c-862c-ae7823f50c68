using UnityEngine;
using UnityEngine.UI;
using System.IO;
using TMPro;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM文件选择器UI
    /// 帮助用户手动指定VRM文件路径
    /// </summary>
    public class VRMFileSelector : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private Button selectFileButton;
        [SerializeField] private TMP_InputField filePathInput;
        [SerializeField] private TextMeshProUGUI statusText;
        [SerializeField] private Button confirmButton;
        [SerializeField] private GameObject selectorPanel;
        
        [Header("设置")]
        [SerializeField] private bool autoHideOnSuccess = true;
        [SerializeField] private bool debugMode = true;
        
        void Start()
        {
            SetupUI();
            CheckIfVRMDataExists();
        }
        
        void SetupUI()
        {
            if (selectFileButton != null)
            {
                selectFileButton.onClick.AddListener(OnSelectFileClicked);
            }
            
            if (confirmButton != null)
            {
                confirmButton.onClick.AddListener(OnConfirmClicked);
            }
            
            if (filePathInput != null)
            {
                filePathInput.onValueChanged.AddListener(OnFilePathChanged);
            }
            
            UpdateUI();
        }
        
        void CheckIfVRMDataExists()
        {
            if (VRMRuntimeLoader.Instance.HasSavedVRMData())
            {
                var vrmDataInfo = VRMRuntimeLoader.Instance.GetVRMDataInfo();
                UpdateStatus($"✅ VRM文件已准备: {vrmDataInfo.fileName}", Color.green);
                
                if (autoHideOnSuccess && selectorPanel != null)
                {
                    selectorPanel.SetActive(false);
                }
            }
            else
            {
                UpdateStatus("⚠️ 需要指定VRM文件", Color.yellow);
                
                if (selectorPanel != null)
                {
                    selectorPanel.SetActive(true);
                }
            }
        }
        
        void OnSelectFileClicked()
        {
            LogDebug("🔍 打开文件选择对话框...");
            
            #if UNITY_EDITOR
            string selectedPath = UnityEditor.EditorUtility.OpenFilePanel(
                "选择VRM文件", 
                Application.dataPath, 
                "vrm"
            );
            
            if (!string.IsNullOrEmpty(selectedPath))
            {
                if (filePathInput != null)
                {
                    filePathInput.text = selectedPath;
                }
                OnFilePathChanged(selectedPath);
            }
            #else
            UpdateStatus("❌ 运行时不支持文件对话框，请手动输入路径", Color.red);
            #endif
        }
        
        void OnFilePathChanged(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                UpdateStatus("请选择或输入VRM文件路径", Color.white);
                SetConfirmButtonEnabled(false);
                return;
            }
            
            if (File.Exists(path))
            {
                if (path.ToLower().EndsWith(".vrm"))
                {
                    UpdateStatus($"✅ 找到VRM文件: {Path.GetFileName(path)}", Color.green);
                    SetConfirmButtonEnabled(true);
                }
                else
                {
                    UpdateStatus("❌ 请选择.vrm文件", Color.red);
                    SetConfirmButtonEnabled(false);
                }
            }
            else
            {
                UpdateStatus("❌ 文件不存在", Color.red);
                SetConfirmButtonEnabled(false);
            }
        }
        
        async void OnConfirmClicked()
        {
            if (filePathInput == null || string.IsNullOrEmpty(filePathInput.text))
            {
                UpdateStatus("❌ 请先选择VRM文件", Color.red);
                return;
            }
            
            string filePath = filePathInput.text;
            
            UpdateStatus("🔄 正在加载VRM文件...", Color.yellow);
            SetConfirmButtonEnabled(false);
            
            try
            {
                bool success = await VRMRuntimeLoader.Instance.SaveVRMFromFile(filePath);
                
                if (success)
                {
                    UpdateStatus("✅ VRM文件加载成功！", Color.green);
                    LogDebug($"✅ VRM文件已保存: {Path.GetFileName(filePath)}");
                    
                    if (autoHideOnSuccess && selectorPanel != null)
                    {
                        selectorPanel.SetActive(false);
                    }
                }
                else
                {
                    UpdateStatus("❌ VRM文件加载失败", Color.red);
                    SetConfirmButtonEnabled(true);
                }
            }
            catch (System.Exception e)
            {
                UpdateStatus($"❌ 加载错误: {e.Message}", Color.red);
                SetConfirmButtonEnabled(true);
                LogDebug($"❌ VRM加载异常: {e.Message}");
            }
        }
        
        void UpdateStatus(string message, Color color)
        {
            if (statusText != null)
            {
                statusText.text = message;
                statusText.color = color;
            }
            
            LogDebug(message);
        }
        
        void SetConfirmButtonEnabled(bool enabled)
        {
            if (confirmButton != null)
            {
                confirmButton.interactable = enabled;
            }
        }
        
        void UpdateUI()
        {
            // 初始状态设置
            SetConfirmButtonEnabled(false);
            
            if (filePathInput != null && !string.IsNullOrEmpty(filePathInput.text))
            {
                OnFilePathChanged(filePathInput.text);
            }
        }
        
        /// <summary>
        /// 显示选择器面板
        /// </summary>
        public void ShowSelector()
        {
            if (selectorPanel != null)
            {
                selectorPanel.SetActive(true);
            }
            
            CheckIfVRMDataExists();
        }
        
        /// <summary>
        /// 隐藏选择器面板
        /// </summary>
        public void HideSelector()
        {
            if (selectorPanel != null)
            {
                selectorPanel.SetActive(false);
            }
        }
        
        /// <summary>
        /// 重置选择器状态
        /// </summary>
        public void ResetSelector()
        {
            if (filePathInput != null)
            {
                filePathInput.text = "";
            }
            
            UpdateStatus("请选择VRM文件", Color.white);
            SetConfirmButtonEnabled(false);
        }
        
        /// <summary>
        /// 检查并显示VRM状态
        /// </summary>
        [ContextMenu("检查VRM状态")]
        public void CheckVRMStatus()
        {
            CheckIfVRMDataExists();
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMFileSelector] {message}");
            }
        }
    }
}
