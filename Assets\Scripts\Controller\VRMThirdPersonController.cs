using UnityEngine;
using VRoidFaceCustomization.Core;

namespace VRoidFaceCustomization.Controller
{
    /// <summary>
    /// VRM第三人称控制器集成 - 与Unity官方第三人称控制器配合使用
    /// 需要手动安装Unity官方第三人称控制器包
    /// </summary>
    public class VRMThirdPersonController : MonoBehaviour
    {
        [Header("VRM设置")]
        [SerializeField] private GameObject vrmObject; // VRM对象
        [SerializeField] private bool autoFindVRM = true; // 自动查找VRM
        
        [Header("控制器设置")]
        [SerializeField] private bool enableThirdPersonController = true; // 启用第三人称控制器
        [SerializeField] private bool useVRMAnimator = true; // 使用VRM动画器
        
        [Header("动画设置")]
        [SerializeField] private RuntimeAnimatorController defaultAnimatorController; // 默认动画控制器
        [SerializeField] private Avatar defaultAvatar; // 默认Avatar
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        
        [Header("当前状态")]
        [SerializeField] private bool isInitialized = false;
        [SerializeField] private Animator vrmAnimator;
        [SerializeField] private CharacterController characterController;
        
        // Unity官方第三人称控制器组件（需要手动安装包）
        private Component thirdPersonController;
        private Component playerInput;
        
        #region Unity生命周期
        private void Awake()
        {
            LogDebug("✅ VRMThirdPersonController 初始化");
        }
        
        private void Start()
        {
            if (autoFindVRM && vrmObject == null)
            {
                FindVRMObject();
            }
            
            if (vrmObject != null)
            {
                InitializeController();
            }
        }
        #endregion
        
        #region 控制器初始化
        /// <summary>
        /// 初始化控制器
        /// </summary>
        public void InitializeController()
        {
            if (vrmObject == null)
            {
                LogDebug("❌ VRM对象为空，无法初始化控制器");
                return;
            }
            
            LogDebug($"🚀 开始初始化VRM第三人称控制器: {vrmObject.name}");
            
            // 步骤1：设置VRM动画器
            SetupVRMAnimator();
            
            // 步骤2：设置角色控制器
            SetupCharacterController();
            
            // 步骤3：设置第三人称控制器（如果已安装）
            SetupThirdPersonController();
            
            // 步骤4：设置输入系统
            SetupPlayerInput();
            
            isInitialized = true;
            LogDebug("✅ VRM第三人称控制器初始化完成");
            
            // 触发初始化完成事件
            OnControllerInitialized?.Invoke(vrmObject);
        }
        
        /// <summary>
        /// 设置VRM动画器
        /// </summary>
        private void SetupVRMAnimator()
        {
            LogDebug("🎭 设置VRM动画器");
            
            vrmAnimator = vrmObject.GetComponent<Animator>();
            
            if (vrmAnimator == null)
            {
                LogDebug("🔧 添加Animator组件");
                vrmAnimator = vrmObject.AddComponent<Animator>();
            }
            
            // 设置动画控制器
            if (defaultAnimatorController != null)
            {
                vrmAnimator.runtimeAnimatorController = defaultAnimatorController;
                LogDebug($"✅ 设置动画控制器: {defaultAnimatorController.name}");
            }
            else
            {
                LogDebug("⚠️ 未设置默认动画控制器");
            }
            
            // 设置Avatar
            if (useVRMAnimator)
            {
                // 尝试使用VRM的Avatar
                var vrmInstance = vrmObject.GetComponent<UniVRM10.Vrm10Instance>();
                if (vrmInstance != null && vrmInstance.Humanoid != null)
                {
                    var avatar = vrmInstance.Humanoid.CreateAvatar();
                    if (avatar != null)
                    {
                        vrmAnimator.avatar = avatar;
                        LogDebug("✅ 使用VRM Avatar");
                    }
                    else
                    {
                        LogDebug("⚠️ VRM Avatar创建失败，使用默认Avatar");
                        if (defaultAvatar != null)
                        {
                            vrmAnimator.avatar = defaultAvatar;
                        }
                    }
                }
                else if (defaultAvatar != null)
                {
                    vrmAnimator.avatar = defaultAvatar;
                    LogDebug("✅ 使用默认Avatar");
                }
            }
            else if (defaultAvatar != null)
            {
                vrmAnimator.avatar = defaultAvatar;
                LogDebug("✅ 使用默认Avatar");
            }
            
            // 设置动画器参数
            vrmAnimator.applyRootMotion = false; // 通常第三人称控制器不使用根运动
            vrmAnimator.updateMode = AnimatorUpdateMode.Normal;
            vrmAnimator.cullingMode = AnimatorCullingMode.CullUpdateTransforms;
        }
        
        /// <summary>
        /// 设置角色控制器
        /// </summary>
        private void SetupCharacterController()
        {
            LogDebug("🚶 设置角色控制器");
            
            characterController = vrmObject.GetComponent<CharacterController>();
            
            if (characterController == null)
            {
                LogDebug("🔧 添加CharacterController组件");
                characterController = vrmObject.AddComponent<CharacterController>();
            }
            
            // 设置CharacterController参数
            characterController.height = 1.8f; // 默认高度
            characterController.radius = 0.3f; // 默认半径
            characterController.center = new Vector3(0, 0.9f, 0); // 中心点
            characterController.stepOffset = 0.3f; // 台阶高度
            characterController.slopeLimit = 45f; // 坡度限制
            
            LogDebug("✅ CharacterController设置完成");
        }
        
        /// <summary>
        /// 设置第三人称控制器（需要Unity官方包）
        /// </summary>
        private void SetupThirdPersonController()
        {
            if (!enableThirdPersonController)
            {
                LogDebug("⚠️ 第三人称控制器已禁用");
                return;
            }
            
            LogDebug("🎮 设置第三人称控制器");
            
            // 尝试获取Unity官方第三人称控制器组件
            // 注意：这需要手动安装Unity官方第三人称控制器包
            
            // 检查是否已有第三人称控制器组件
            var controllerType = System.Type.GetType("StarterAssets.ThirdPersonController");
            if (controllerType != null)
            {
                thirdPersonController = vrmObject.GetComponent(controllerType);
                
                if (thirdPersonController == null)
                {
                    LogDebug("🔧 添加ThirdPersonController组件");
                    thirdPersonController = vrmObject.AddComponent(controllerType);
                }
                
                LogDebug("✅ Unity官方第三人称控制器设置完成");
            }
            else
            {
                LogDebug("⚠️ 未找到Unity官方第三人称控制器，请确保已安装相关包");
                LogDebug("   请在Package Manager中搜索并安装 'Starter Assets - Third Person Character Controller'");
            }
        }
        
        /// <summary>
        /// 设置玩家输入
        /// </summary>
        private void SetupPlayerInput()
        {
            LogDebug("⌨️ 设置玩家输入");
            
            // 检查是否已有PlayerInput组件
            var inputType = System.Type.GetType("UnityEngine.InputSystem.PlayerInput");
            if (inputType != null)
            {
                playerInput = vrmObject.GetComponent(inputType);
                
                if (playerInput == null)
                {
                    LogDebug("🔧 添加PlayerInput组件");
                    playerInput = vrmObject.AddComponent(inputType);
                }
                
                LogDebug("✅ PlayerInput设置完成");
            }
            else
            {
                LogDebug("⚠️ 未找到Input System，请确保已启用新输入系统");
            }
        }
        #endregion
        
        #region 辅助方法
        /// <summary>
        /// 自动查找VRM对象
        /// </summary>
        private void FindVRMObject()
        {
            // 首先尝试从场景管理器获取
            var sceneManager = FindObjectOfType<VRoidFaceCustomization.Scene.ThirdPersonSceneManager>();
            if (sceneManager != null)
            {
                vrmObject = sceneManager.GetCurrentVRM();
                if (vrmObject != null)
                {
                    LogDebug($"🔍 从场景管理器找到VRM对象: {vrmObject.name}");
                    return;
                }
            }
            
            // 查找场景中的VRM对象
            var vrmInstances = FindObjectsOfType<UniVRM10.Vrm10Instance>();
            
            if (vrmInstances.Length > 0)
            {
                vrmObject = vrmInstances[0].gameObject;
                LogDebug($"🔍 自动找到VRM对象: {vrmObject.name}");
            }
            else
            {
                LogDebug("⚠️ 未找到VRM对象");
            }
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMThirdPersonController] {message}");
            }
        }
        #endregion
        
        #region 公共接口
        /// <summary>
        /// 设置VRM对象
        /// </summary>
        public void SetVRMObject(GameObject vrm)
        {
            vrmObject = vrm;
            LogDebug($"🎯 设置VRM对象: {(vrm != null ? vrm.name : "null")}");
            
            if (vrm != null && !isInitialized)
            {
                InitializeController();
            }
        }
        
        /// <summary>
        /// 获取VRM对象
        /// </summary>
        public GameObject GetVRMObject()
        {
            return vrmObject;
        }
        
        /// <summary>
        /// 检查是否已初始化
        /// </summary>
        public bool IsInitialized()
        {
            return isInitialized;
        }
        
        /// <summary>
        /// 重新初始化控制器
        /// </summary>
        public void Reinitialize()
        {
            isInitialized = false;
            InitializeController();
        }
        
        /// <summary>
        /// 启用/禁用控制器
        /// </summary>
        public void SetControllerEnabled(bool enabled)
        {
            if (characterController != null)
            {
                characterController.enabled = enabled;
            }
            
            if (thirdPersonController != null)
            {
                var enabledProperty = thirdPersonController.GetType().GetProperty("enabled");
                enabledProperty?.SetValue(thirdPersonController, enabled);
            }
            
            LogDebug($"🎮 控制器状态: {(enabled ? "启用" : "禁用")}");
        }
        #endregion
        
        #region 事件系统
        /// <summary>
        /// 控制器初始化完成事件
        /// </summary>
        public static event System.Action<GameObject> OnControllerInitialized;
        
        /// <summary>
        /// 控制器设置失败事件
        /// </summary>
        public static event System.Action<string> OnControllerSetupFailed;
        #endregion
        
        #region 调试功能
        /// <summary>
        /// 显示当前状态
        /// </summary>
        [ContextMenu("显示当前状态")]
        private void ShowCurrentStatus()
        {
            LogDebug($"VRM对象: {(vrmObject != null ? vrmObject.name : "null")}");
            LogDebug($"已初始化: {isInitialized}");
            LogDebug($"动画器: {(vrmAnimator != null ? "已设置" : "未设置")}");
            LogDebug($"角色控制器: {(characterController != null ? "已设置" : "未设置")}");
            LogDebug($"第三人称控制器: {(thirdPersonController != null ? "已设置" : "未设置")}");
            LogDebug($"玩家输入: {(playerInput != null ? "已设置" : "未设置")}");
        }
        
        /// <summary>
        /// 测试重新初始化
        /// </summary>
        [ContextMenu("测试重新初始化")]
        private void TestReinitialize()
        {
            if (Application.isPlaying)
            {
                Reinitialize();
            }
            else
            {
                LogDebug("⚠️ 只能在运行时测试重新初始化功能");
            }
        }
        
        /// <summary>
        /// 显示安装指南
        /// </summary>
        [ContextMenu("显示安装指南")]
        private void ShowInstallationGuide()
        {
            LogDebug("📖 Unity官方第三人称控制器安装指南:");
            LogDebug("1. 打开Package Manager (Window > Package Manager)");
            LogDebug("2. 切换到Unity Registry");
            LogDebug("3. 搜索 'Starter Assets - Third Person Character Controller'");
            LogDebug("4. 点击Install安装包");
            LogDebug("5. 安装完成后重新初始化控制器");
            LogDebug("");
            LogDebug("或者从Asset Store下载:");
            LogDebug("https://assetstore.unity.com/packages/essentials/starter-assets-third-person-character-controller-196526");
        }
        #endregion
    }
}
