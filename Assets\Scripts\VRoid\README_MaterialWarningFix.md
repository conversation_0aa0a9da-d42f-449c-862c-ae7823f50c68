# VRM10FaceController 材质警告修复

## 问题描述

在编辑模式下初始化VRM10FaceController时，会出现以下警告：

```
Instantiating material due to calling renderer.material during edit mode. 
This will leak materials into the scene. 
You most likely want to use renderer.sharedMaterial instead.
```

## 问题原因

1. **错误位置**：UniVRM10库的`MaterialValueBindingMerger.cs`第68行
2. **触发条件**：在编辑模式下，当VRM模型是Editor导入的Prefab实例时
3. **根本原因**：UniVRM10库在初始化时访问`renderer.materials`属性，这会在编辑模式下创建材质副本

## 解决方案

### 1. 智能初始化模式

修改了`VRM10FaceController.InitializeFaceController()`方法：

- **编辑模式**：使用简化初始化，避免访问Runtime，防止材质警告
- **Play模式**：使用完整初始化，获得所有功能

### 2. 编辑模式功能

在编辑模式下：
- ✅ 可以查看表情列表
- ✅ 可以设置表情参数值（仅更新数据）
- ✅ 可以使用Inspector界面
- ❌ 不会实际应用到模型（避免材质警告）

### 3. Play模式功能

在Play模式下：
- ✅ 完整的VRM表情控制
- ✅ 实时应用到模型
- ✅ 所有BlendShape功能
- ✅ 材质绑定功能

## 使用方法

### 正常使用

```csharp
// 自动根据当前模式选择初始化方式
faceController.InitializeFaceController();

// 设置表情（编辑模式下只更新数据，Play模式下实际应用）
faceController.SetExpression("happy", 0.8f);
```

### 强制完整初始化

如果在编辑模式下需要完整功能（会产生材质警告）：

```csharp
// 强制进行完整初始化（会产生材质警告）
faceController.ForceFullInitialization();
```

## Inspector界面改进

### 状态显示
- 显示当前模式（编辑模式/Play模式）
- 显示初始化状态
- 在编辑模式下显示功能限制提示

### 按钮功能
- **初始化面部控制器**：智能初始化
- **强制完整初始化**：仅在编辑模式下显示，会警告用户

## 测试

使用`VRM10FaceControllerTest`组件进行测试：

1. 添加测试组件到VRM对象
2. 在Inspector中点击"运行测试"
3. 查看控制台输出验证功能

## 注意事项

1. **推荐做法**：在Play模式下使用完整功能
2. **编辑模式限制**：表情设置不会实际应用到模型
3. **材质警告**：如果使用强制完整初始化，仍会产生警告但功能完整
4. **性能影响**：编辑模式下的简化初始化性能更好

## 兼容性

- ✅ 保持与现有代码的完全兼容
- ✅ 不影响Play模式下的任何功能
- ✅ 向后兼容所有现有API
- ✅ 支持所有VRM 1.0功能
