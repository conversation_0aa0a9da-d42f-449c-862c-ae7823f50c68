using UnityEngine;
using System;
using System.IO;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace VRoidFaceCustomization
{
    public class CharacterDataStorage : MonoBehaviour
    {
        private string savePath;
        public static event Action<string> OnCharacterSaved;
        public static event Action<string> OnCharacterLoaded;
        public static event Action<string> OnCharacterDeleted;
        public static event Action<string, string> OnStorageError;

        private void Awake()
        {
            savePath = Path.Combine(Application.persistentDataPath, "characters");
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }
        }

        public async Task<string> SaveCharacterAsync(CharacterData data, string fileName = null)
        {
            fileName = string.IsNullOrEmpty(fileName) ? Guid.NewGuid().ToString() : fileName;
            string filePath = Path.Combine(savePath, fileName + ".json");
            try
            {
                string json = JsonConvert.SerializeObject(data, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                OnCharacterSaved?.Invoke(fileName);
                return fileName;
            }
            catch (Exception e)
            {
                OnStorageError?.Invoke("SAVE_ERROR", e.Message);
                throw;
            }
        }

        public async Task<CharacterData> LoadCharacterAsync(string fileName)
        {
            Debug.Log($"💾 [CharacterDataStorage.LoadCharacterAsync] 开始加载文件: {fileName}");
            
            string filePath = Path.Combine(savePath, fileName + ".json");
            Debug.Log($"📁 [CharacterDataStorage.LoadCharacterAsync] 文件完整路径: {filePath}");
            
            if (!File.Exists(filePath))
            {
                string errorMsg = "File not found";
                Debug.LogError($"❌ [CharacterDataStorage.LoadCharacterAsync] {errorMsg}: {filePath}");
                OnStorageError?.Invoke("LOAD_ERROR", errorMsg);
                return null;
            }
            
            Debug.Log($"✅ [CharacterDataStorage.LoadCharacterAsync] 文件存在，开始读取...");
            
            try
            {
                Debug.Log($"📖 [CharacterDataStorage.LoadCharacterAsync] 异步读取文件内容...");
                string json = await File.ReadAllTextAsync(filePath);
                
                Debug.Log($"📊 [CharacterDataStorage.LoadCharacterAsync] 文件读取结果:");
                Debug.Log($"   - 文件大小: {json.Length} 字符");
                Debug.Log($"   - 内容预览: {(json.Length > 100 ? json.Substring(0, 100) + "..." : json)}");
                
                Debug.Log($"🔄 [CharacterDataStorage.LoadCharacterAsync] 开始JSON反序列化...");
                var data = JsonConvert.DeserializeObject<CharacterData>(json);
                
                Debug.Log($"📊 [CharacterDataStorage.LoadCharacterAsync] 反序列化结果: {( data != null ? "成功" : "失败")}");
                
                if (data != null)
                {
                    Debug.Log($"📋 [CharacterDataStorage.LoadCharacterAsync] 反序列化数据详情:");
                    Debug.Log($"   - 角色名称: {data.characterName}");
                    Debug.Log($"   - 角色ID: {data.characterId}");
                    Debug.Log($"   - 创建时间: {data.createdTime}");
                    Debug.Log($"   - 面部数据: {( data.facialData != null ? "存在" : "不存在")}");
                    Debug.Log($"   - 服装数据: {( data.clothingData != null ? "存在" : "不存在")}");
                    
                    if (data.facialData != null)
                    {
                        Debug.Log($"😊 [CharacterDataStorage.LoadCharacterAsync] 面部数据详情:");
                        Debug.Log($"   - 标准表情: {( data.facialData.standardExpressions?.Count ?? 0)} 个");
                        Debug.Log($"   - 自定义表情: {( data.facialData.customExpressions?.Count ?? 0)} 个");
                        Debug.Log($"   - BlendShape参数: {( data.facialData.blendShapeParameters?.Count ?? 0)} 个");
                    }
                    
                    if (data.clothingData != null)
                    {
                        Debug.Log($"👔 [CharacterDataStorage.LoadCharacterAsync] 服装数据详情:");
                        Debug.Log($"   - 当前服装: {( data.clothingData.currentOutfit?.Count ?? 0)} 件");
                        Debug.Log($"   - 最近服装: {( data.clothingData.recentClothes?.Count ?? 0)} 件");
                    }
                }
                
                Debug.Log($"🎉 [CharacterDataStorage.LoadCharacterAsync] 触发OnCharacterLoaded事件: {fileName}");
                OnCharacterLoaded?.Invoke(fileName);
                
                Debug.Log($"✅ [CharacterDataStorage.LoadCharacterAsync] 加载完成，返回数据");
                return data;
            }
            catch (Exception e)
            {
                string errorMsg = $"文件加载异常: {e.Message}";
                Debug.LogError($"❌ [CharacterDataStorage.LoadCharacterAsync] {errorMsg}");
                Debug.LogError($"🔍 [CharacterDataStorage.LoadCharacterAsync] 异常类型: {e.GetType().Name}");
                Debug.LogError($"🔍 [CharacterDataStorage.LoadCharacterAsync] 异常堆栈: {e.StackTrace}");
                
                OnStorageError?.Invoke("LOAD_ERROR", e.Message);
                return null;
            }
        }

        public async Task<bool> DeleteCharacterAsync(string fileName)
        {
            string filePath = Path.Combine(savePath, fileName + ".json");
            if (!File.Exists(filePath)) return false;
            try
            {
                await Task.Run(() => File.Delete(filePath));
                OnCharacterDeleted?.Invoke(fileName);
                return true;
            }
            catch (Exception e)
            {
                OnStorageError?.Invoke("DELETE_ERROR", e.Message);
                return false;
            }
        }

        public async Task<List<CharacterInfo>> GetSavedCharactersAsync()
        {
            var characterInfos = new List<CharacterInfo>();
            var files = await Task.Run(() => Directory.GetFiles(savePath, "*.json"));
            foreach (var file in files)
            {
                characterInfos.Add(new CharacterInfo
                {
                    fileId = Path.GetFileNameWithoutExtension(file),
                    characterName = Path.GetFileNameWithoutExtension(file), // Placeholder
                    lastModified = File.GetLastWriteTime(file)
                });
            }
            return characterInfos;
        }
        
        public async Task<bool> ExistsAsync(string fileName)
        {
            string filePath = Path.Combine(savePath, fileName + ".json");
            return await Task.Run(() => File.Exists(filePath));
        }

        public List<string> GetSavedCharacterNames()
        {
            var names = new List<string>();
            var files = Directory.GetFiles(savePath, "*.json");
            foreach (var file in files)
            {
                names.Add(Path.GetFileNameWithoutExtension(file));
            }
            return names;
        }
    }
} 