using UnityEngine;
using UniVRM10;
using System.Collections;
using UnityEngine.UI;
using VRoidFaceCustomization.Data;
using VRoidFaceCustomization.UI;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM1.0适配系统测试器
    /// 用于测试VRM模型在场景切换时的状态保存和恢复
    /// </summary>
    public class VRMAdaptationTester : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private KeyCode testSaveKey = KeyCode.F1;
        [SerializeField] private KeyCode testLoadKey = KeyCode.F2;
        [SerializeField] private KeyCode testSceneTransitionKey = KeyCode.F3;
        [SerializeField] private KeyCode modifyVRMKey = KeyCode.F4;
        
        [Header("UI测试按钮")]
        [SerializeField] private Button saveButton;
        [SerializeField] private Button loadButton;
        [SerializeField] private Button sceneTransitionButton;
        [SerializeField] private Button modifyVRMButton;
        [SerializeField] private Button clearStateButton;
        
        [Header("测试结果显示")]
        [SerializeField] private Text statusText;
        [SerializeField] private Text blendShapeCountText;
        [SerializeField] private Text materialPropsCountText;
        
        [Header("VRM文件路径")]
        [SerializeField] private string[] vrmFilePaths = {
            "Assets/Prefabs/model/xa初始模型.vrm",
            "Assets/VRMModels/xa初始模型.vrm", 
            "Assets/Models/xa初始模型.vrm",
            "Assets/Prefabs/clorh model/xa初始模型.vrm"
        };
        
        private VRMStateManager vrmStateManager;
        private VRMRuntimeLoader vrmRuntimeLoader;
        private SceneTransitionManager sceneTransitionManager;
        
        // 测试用的修改参数
        private bool isVRMModified = false;
        
        private void Start()
        {
            InitializeComponents();
            SetupUI();
            LogDebug("🧪 VRM适配系统测试器已启动");
            LogDebug("🎮 控制说明:");
            LogDebug("   F1 - 保存VRM状态");
            LogDebug("   F2 - 加载VRM状态");
            LogDebug("   F3 - 测试场景切换");
            LogDebug("   F4 - 修改VRM模型（用于测试）");
            LogDebug("   F5 - 检查VRM文件路径");
            
            // 启动时检查VRM文件
            CheckVRMFilePaths();
        }
        
        private void InitializeComponents()
        {
            vrmStateManager = VRMStateManager.Instance;
            vrmRuntimeLoader = VRMRuntimeLoader.Instance;
            
            // 创建场景切换管理器（如果不存在）
            sceneTransitionManager = FindObjectOfType<SceneTransitionManager>();
            if (sceneTransitionManager == null)
            {
                var go = new GameObject("SceneTransitionManager");
                sceneTransitionManager = go.AddComponent<SceneTransitionManager>();
            }
            
            LogDebug($"✅ 组件初始化完成:");
            LogDebug($"   VRMStateManager: {vrmStateManager != null}");
            LogDebug($"   VRMRuntimeLoader: {vrmRuntimeLoader != null}");
            LogDebug($"   SceneTransitionManager: {sceneTransitionManager != null}");
        }
        
        private void SetupUI()
        {
            if (saveButton != null)
                saveButton.onClick.AddListener(() => StartCoroutine(TestSaveVRMState()));
                
            if (loadButton != null)
                loadButton.onClick.AddListener(() => StartCoroutine(TestLoadVRMState()));
                
            if (sceneTransitionButton != null)
                sceneTransitionButton.onClick.AddListener(TestSceneTransition);
                
            if (modifyVRMButton != null)
                modifyVRMButton.onClick.AddListener(() => StartCoroutine(TestModifyVRM()));
                
            if (clearStateButton != null)
                clearStateButton.onClick.AddListener(TestClearState);
                
            UpdateUI();
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(testSaveKey))
            {
                StartCoroutine(TestSaveVRMState());
            }
            else if (Input.GetKeyDown(testLoadKey))
            {
                StartCoroutine(TestLoadVRMState());
            }
            else if (Input.GetKeyDown(testSceneTransitionKey))
            {
                TestSceneTransition();
            }
            else if (Input.GetKeyDown(modifyVRMKey))
            {
                StartCoroutine(TestModifyVRM());
            }
            
            // F5 - 检查VRM文件路径
            if (Input.GetKeyDown(KeyCode.F5))
            {
                CheckVRMFilePaths();
            }
        }
        
        /// <summary>
        /// 测试保存VRM状态
        /// </summary>
        private IEnumerator TestSaveVRMState()
        {
            LogDebug("🧪 [测试] 开始保存VRM状态...");
            UpdateStatus("测试：保存VRM状态...");
            
            var vrmInstance = FindObjectOfType<Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug("❌ [测试] 场景中没有找到VRM实例");
                UpdateStatus("错误：未找到VRM实例");
                yield break;
            }
            
            // 保存VRM渲染状态
            var renderState = vrmStateManager.CaptureVRMRenderState(vrmInstance.gameObject);
            if (renderState != null)
            {
                LogDebug($"✅ [测试] VRM渲染状态保存成功");
                LogDebug($"   BlendShapes: {renderState.blendShapes.Count}");
                LogDebug($"   材质属性: {renderState.materialProperties.Count}");
                LogDebug($"   骨骼变换: {renderState.boneTransforms.Count}");
                UpdateStatus("成功：VRM状态已保存");
            }
            else
            {
                LogDebug("❌ [测试] VRM渲染状态保存失败");
                UpdateStatus("错误：VRM状态保存失败");
            }
            
            // 同时保存VRM文件数据
            string vrmFilePath = "Assets/Prefabs/model/xa初始模型.vrm";
            if (System.IO.File.Exists(vrmFilePath))
            {
                var saveTask = vrmRuntimeLoader.SaveVRMFromFile(vrmFilePath, vrmInstance.gameObject);
                while (!saveTask.IsCompleted)
                {
                    yield return null;
                }
                
                if (saveTask.Result)
                {
                    LogDebug("✅ [测试] VRM文件数据保存成功");
                }
                else
                {
                    LogDebug("❌ [测试] VRM文件数据保存失败");
                }
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 测试加载VRM状态
        /// </summary>
        private IEnumerator TestLoadVRMState()
        {
            LogDebug("🧪 [测试] 开始加载VRM状态...");
            UpdateStatus("测试：加载VRM状态...");
            
            if (!vrmStateManager.HasSavedRenderState())
            {
                LogDebug("❌ [测试] 没有保存的VRM渲染状态");
                UpdateStatus("错误：没有保存的状态");
                yield break;
            }
            
            var vrmInstance = FindObjectOfType<Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug("❌ [测试] 场景中没有找到VRM实例");
                UpdateStatus("错误：未找到VRM实例");
                yield break;
            }
            
            var renderState = vrmStateManager.GetSavedRenderState();
            
            // 使用异步版本
            bool applySuccess = false;
            var applyTask = vrmStateManager.ApplyVRMRenderState(vrmInstance.gameObject, renderState);

            // 等待异步操作完成
            while (!applyTask.IsCompleted)
            {
                yield return null;
            }

            applySuccess = applyTask.Result;

            if (applySuccess)
            {
                LogDebug("✅ [测试] VRM渲染状态应用成功");
                UpdateStatus("成功：VRM状态已恢复");
            }
            else
            {
                LogDebug("❌ [测试] VRM渲染状态应用失败");
                UpdateStatus("错误：VRM状态恢复失败");
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 测试场景切换
        /// </summary>
        private void TestSceneTransition()
        {
            LogDebug("🧪 [测试] 开始测试场景切换...");
            UpdateStatus("测试：场景切换...");
            
            if (sceneTransitionManager != null)
            {
                _ = sceneTransitionManager.TransitionToThirdPersonScene();
            }
            else
            {
                LogDebug("❌ [测试] SceneTransitionManager不可用");
                UpdateStatus("错误：场景切换管理器不可用");
            }
        }
        
        /// <summary>
        /// 测试修改VRM模型
        /// </summary>
        private IEnumerator TestModifyVRM()
        {
            LogDebug("🧪 [测试] 开始修改VRM模型（用于测试状态保存）...");
            UpdateStatus("测试：修改VRM模型...");
            
            var vrmInstance = FindObjectOfType<Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug("❌ [测试] 场景中没有找到VRM实例");
                UpdateStatus("错误：未找到VRM实例");
                yield break;
            }
            
            // 修改BlendShapes
            var skinnedMeshRenderers = vrmInstance.GetComponentsInChildren<SkinnedMeshRenderer>();
            int modifiedBlendShapes = 0;
            
            foreach (var renderer in skinnedMeshRenderers)
            {
                if (renderer.sharedMesh != null)
                {
                    var mesh = renderer.sharedMesh;
                    for (int i = 0; i < Mathf.Min(3, mesh.blendShapeCount); i++) // 只修改前3个
                    {
                        float targetWeight = isVRMModified ? 0f : Random.Range(20f, 80f);
                        renderer.SetBlendShapeWeight(i, targetWeight);
                        modifiedBlendShapes++;
                        
                        LogDebug($"   修改BlendShape: {mesh.GetBlendShapeName(i)} = {targetWeight}");
                    }
                }
            }
            
            // 修改材质颜色
            var renderers = vrmInstance.GetComponentsInChildren<Renderer>();
            int modifiedMaterials = 0;
            
            foreach (var renderer in renderers)
            {
                foreach (var material in renderer.materials)
                {
                    if (material != null && material.HasProperty("_Color"))
                    {
                        Color targetColor = isVRMModified ? Color.white : new Color(
                            Random.Range(0.5f, 1f),
                            Random.Range(0.5f, 1f),
                            Random.Range(0.5f, 1f),
                            1f
                        );
                        material.SetColor("_Color", targetColor);
                        modifiedMaterials++;
                        
                        if (modifiedMaterials >= 3) break; // 只修改3个材质
                    }
                }
                if (modifiedMaterials >= 3) break;
            }
            
            isVRMModified = !isVRMModified;
            
            LogDebug($"✅ [测试] VRM模型修改完成");
            LogDebug($"   修改了 {modifiedBlendShapes} 个BlendShape");
            LogDebug($"   修改了 {modifiedMaterials} 个材质");
            UpdateStatus($"成功：修改了VRM模型 (状态: {(isVRMModified ? "已修改" : "恢复原状")})");
            
            yield return new WaitForSeconds(0.1f);
            UpdateUI();
        }
        
        /// <summary>
        /// 测试清除状态
        /// </summary>
        private void TestClearState()
        {
            LogDebug("🧪 [测试] 清除保存的状态...");
            
            vrmStateManager.ClearSavedState();
            vrmRuntimeLoader.ClearSavedData();
            
            LogDebug("✅ [测试] 状态已清除");
            UpdateStatus("成功：状态已清除");
            UpdateUI();
        }
        
        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus(string status)
        {
            if (statusText != null)
            {
                statusText.text = $"状态: {status}";
            }
        }
        
        /// <summary>
        /// 更新UI显示
        /// </summary>
        private void UpdateUI()
        {
            if (vrmStateManager == null) return;
            
            var renderState = vrmStateManager.GetSavedRenderState();
            
            if (blendShapeCountText != null)
            {
                blendShapeCountText.text = $"BlendShapes: {(renderState?.blendShapes?.Count ?? 0)}";
            }
            
            if (materialPropsCountText != null)
            {
                materialPropsCountText.text = $"材质属性: {(renderState?.materialProperties?.Count ?? 0)}";
            }
            
            // 更新按钮状态
            bool hasRenderState = vrmStateManager.HasSavedRenderState();
            bool hasVRMData = vrmRuntimeLoader.HasSavedVRMData();
            
            if (loadButton != null)
                loadButton.interactable = hasRenderState;
                
            if (sceneTransitionButton != null)
                sceneTransitionButton.interactable = hasRenderState && hasVRMData;
                
            if (clearStateButton != null)
                clearStateButton.interactable = hasRenderState || hasVRMData;
        }
        
        /// <summary>
        /// 检查VRM文件路径
        /// </summary>
        private void CheckVRMFilePaths()
        {
            LogDebug("🔍 检查VRM文件路径...");
            bool foundAny = false;
            
            foreach (var path in vrmFilePaths)
            {
                if (System.IO.File.Exists(path))
                {
                    LogDebug($"✅ 找到VRM文件: {path}");
                    foundAny = true;
                }
                else
                {
                    LogDebug($"❌ 文件不存在: {path}");
                }
            }
            
            if (!foundAny)
            {
                LogDebug("🚨 未找到任何VRM文件！");
                LogDebug("💡 请确保VRM文件存在于以下路径之一:");
                foreach (var path in vrmFilePaths)
                {
                    LogDebug($"   - {path}");
                }
                
                // 尝试查找项目中的所有VRM文件
                FindAllVRMFiles();
            }
        }
        
        /// <summary>
        /// 查找项目中的所有VRM文件
        /// </summary>
        private void FindAllVRMFiles()
        {
            LogDebug("🔎 扫描项目中的所有VRM文件...");
            
            string[] vrmFiles = System.IO.Directory.GetFiles("Assets", "*.vrm", System.IO.SearchOption.AllDirectories);
            
            if (vrmFiles.Length > 0)
            {
                LogDebug($"📁 找到 {vrmFiles.Length} 个VRM文件:");
                foreach (var file in vrmFiles)
                {
                    LogDebug($"   📄 {file}");
                }
                LogDebug("💡 请将正确的路径添加到vrmFilePaths数组中");
            }
            else
            {
                LogDebug("❌ 项目中没有找到任何VRM文件");
                LogDebug("💡 请确保将VRM文件导入到项目中");
            }
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMAdaptationTester] {message}");
            }
        }
        
        private void OnGUI()
        {
            if (!debugMode) return;
            
            GUI.skin.box.fontSize = 14;
            GUI.skin.label.fontSize = 12;
            
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.Box("VRM1.0适配系统测试器");
            
            GUILayout.Label($"VRM渲染状态: {(vrmStateManager?.HasSavedRenderState() == true ? "已保存" : "未保存")}");
            GUILayout.Label($"VRM文件数据: {(vrmRuntimeLoader?.HasSavedVRMData() == true ? "已保存" : "未保存")}");
            
            var renderState = vrmStateManager?.GetSavedRenderState();
            if (renderState != null)
            {
                GUILayout.Label($"BlendShapes: {renderState.blendShapes.Count}");
                GUILayout.Label($"材质属性: {renderState.materialProperties.Count}");
                GUILayout.Label($"骨骼变换: {renderState.boneTransforms.Count}");
            }
            
            GUILayout.Space(10);
            GUILayout.Label("快捷键:");
            GUILayout.Label($"{testSaveKey} - 保存状态");
            GUILayout.Label($"{testLoadKey} - 加载状态");
            GUILayout.Label($"{testSceneTransitionKey} - 场景切换");
            GUILayout.Label($"{modifyVRMKey} - 修改VRM模型");
            
            GUILayout.EndArea();
        }
    }
} 