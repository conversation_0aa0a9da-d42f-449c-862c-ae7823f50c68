# 💾 数据管理系统文档

## 📋 **系统概述**

数据管理系统负责VRM模型数据、角色状态、服装配置等信息的序列化、存储、缓存和管理。支持多种存储方式（本地文件、PlayerPrefs、云端），提供完整的数据持久化解决方案。

### 🏗️ **架构图**
```
数据管理系统
├── 角色数据管理层
│   ├── CharacterDataManager (数据管理器)
│   ├── CharacterDataStructures (数据结构)
│   └── StorageDataStructures (存储结构)
│
├── 序列化层
│   ├── CharacterDataSerializer (序列化器)
│   └── CharacterDataStorage (存储管理)
│
└── VRM资源管理层
    ├── VRMModelManager (模型管理器)
    ├── VRMStateManager (状态管理器)
    ├── VRMAssetManager (资源管理器)
    ├── VRMRuntimeLoader (运行时加载器)
    └── VRMFileCapture (文件捕获器)
```

---

## 🎯 **核心组件详解**

### 1. **CharacterDataManager.cs** - 角色数据管理器
**文件位置**: `Assets/Scripts/Data/CharacterDataManager.cs`  
**脚本行数**: 74行  
**主要职责**: 统一管理角色数据的缓存、事件和生命周期

#### **主要功能**
- 🎯 **单例管理** - 全局唯一的数据管理入口
- 📦 **数据缓存** - 内存中缓存频繁访问的数据
- 🔄 **事件系统** - 数据变更通知和回调
- ⚡ **异步操作** - 支持异步数据保存和加载
- 🛡️ **错误处理** - 完善的异常处理和恢复机制

#### **核心API**
```csharp
// 单例访问
CharacterDataManager.Instance

// 异步数据操作
public async Task<CharacterData> LoadCharacterAsync(string slotName)
public async Task SaveCharacterAsync(CharacterData data, string slotName)
public async Task<bool> DeleteCharacterAsync(string slotName)

// 同步数据操作
public CharacterData LoadCharacter(string slotName)
public bool SaveCharacter(CharacterData data, string slotName)
public bool DeleteCharacter(string slotName)

// 数据管理
public List<string> GetSavedSlots()
public bool HasSavedData(string slotName)
public void ClearAllData()

// 事件系统
public event System.Action<string> OnDataLoaded;
public event System.Action<string> OnDataSaved;
public event System.Action<string> OnDataDeleted;
```

#### **使用示例**
```csharp
var dataManager = CharacterDataManager.Instance;

// 异步保存角色数据
var characterData = new CharacterData
{
    facialData = faceController.GetFacialParametersData(),
    clothingData = clothBinder.GetClothingData()
};
await dataManager.SaveCharacterAsync(characterData, "MyCharacter");

// 异步加载角色数据
var loadedData = await dataManager.LoadCharacterAsync("MyCharacter");
if (loadedData != null)
{
    // 应用加载的数据
    VRM10UnifiedManager.Instance.LoadCharacterData(loadedData);
}

// 获取所有保存的插槽
var savedSlots = dataManager.GetSavedSlots();
```

---

### 2. **CharacterDataStructures.cs** - 角色数据结构
**文件位置**: `Assets/Scripts/Data/CharacterDataStructures.cs`  
**脚本行数**: 83行  
**主要职责**: 定义角色相关的数据结构和序列化格式

#### **核心数据结构**
```csharp
[System.Serializable]
public struct CharacterData
{
    [Header("基本信息")]
    public string characterName;
    public string description;
    public System.DateTime createdTime;
    public System.DateTime lastModified;
    public string version;

    [Header("面部数据")]
    public VRM10FacialParameters facialData;

    [Header("服装数据")]
    public ClothingData clothingData;

    [Header("场景数据")]
    public SceneData sceneData;

    [Header("自定义数据")]
    public Dictionary<string, object> customData;
}

[System.Serializable]
public struct ClothingData
{
    public Dictionary<string, string> currentOutfit; // 服装类型 -> 服装名称
    public List<string> ownedClothes; // 拥有的所有服装
    public List<OutfitPreset> savedOutfits; // 保存的套装
    public Dictionary<string, bool> clothVisibility; // 服装可见性
}

[System.Serializable]
public struct OutfitPreset
{
    public string name;
    public string description;
    public Dictionary<string, string> clothingItems;
    public System.DateTime createdTime;
    public string previewImage; // Base64编码的预览图
}
```

#### **数据验证和转换**
```csharp
// 数据有效性验证
public static bool ValidateCharacterData(CharacterData data)
{
    return !string.IsNullOrEmpty(data.characterName) &&
           data.facialData != null &&
           data.clothingData.currentOutfit != null;
}

// 数据版本升级
public static CharacterData UpgradeDataVersion(CharacterData oldData, string targetVersion)
{
    // 处理版本兼容性升级
    switch (targetVersion)
    {
        case "1.1.0":
            return UpgradeToV1_1_0(oldData);
        default:
            return oldData;
    }
}

// 数据深拷贝
public static CharacterData DeepCopy(CharacterData original)
{
    var json = JsonUtility.ToJson(original);
    return JsonUtility.FromJson<CharacterData>(json);
}
```

---

### 3. **CharacterDataSerializer.cs** - 数据序列化器
**文件位置**: `Assets/Scripts/Data/CharacterDataSerializer.cs`  
**脚本行数**: 33行  
**主要职责**: 处理数据的序列化和反序列化操作

#### **支持的序列化格式**
- **JSON格式**: 人类可读，便于调试和编辑
- **二进制格式**: 体积小，加载速度快
- **压缩格式**: 进一步减小文件大小

#### **核心API**
```csharp
public static class CharacterDataSerializer
{
    // JSON序列化
    public static string SerializeToJson(CharacterData data, bool prettyPrint = false)
    public static CharacterData DeserializeFromJson(string json)
    
    // 二进制序列化
    public static byte[] SerializeToBinary(CharacterData data)
    public static CharacterData DeserializeFromBinary(byte[] data)
    
    // 压缩序列化
    public static byte[] SerializeCompressed(CharacterData data)
    public static CharacterData DeserializeCompressed(byte[] compressedData)
    
    // 文件操作
    public static bool SaveToFile(CharacterData data, string filePath, SerializationFormat format)
    public static CharacterData LoadFromFile(string filePath, SerializationFormat format)
}

public enum SerializationFormat
{
    Json,
    Binary,
    Compressed
}
```

#### **使用示例**
```csharp
var characterData = GetCurrentCharacterData();

// JSON序列化
string jsonData = CharacterDataSerializer.SerializeToJson(characterData, true);
Debug.Log(jsonData);

// 二进制序列化（更紧凑）
byte[] binaryData = CharacterDataSerializer.SerializeToBinary(characterData);

// 压缩序列化（最小体积）
byte[] compressedData = CharacterDataSerializer.SerializeCompressed(characterData);

// 直接保存到文件
bool success = CharacterDataSerializer.SaveToFile(
    characterData, 
    "character_save.json", 
    SerializationFormat.Json
);
```

---

### 4. **CharacterDataStorage.cs** - 数据存储管理
**文件位置**: `Assets/Scripts/Data/CharacterDataStorage.cs`  
**脚本行数**: 117行  
**主要职责**: 管理多种存储方式和存储策略

#### **支持的存储方式**
```csharp
public enum StorageType
{
    LocalFile,      // 本地文件存储
    PlayerPrefs,    // Unity PlayerPrefs
    StreamingAssets,// StreamingAssets文件夹
    PersistentData, // 持久化数据路径
    Cloud          // 云端存储（预留接口）
}
```

#### **存储配置**
```csharp
[System.Serializable]
public class StorageConfig
{
    [Header("存储设置")]
    public StorageType primaryStorage = StorageType.PersistentData;
    public StorageType backupStorage = StorageType.PlayerPrefs;
    public bool enableAutoBackup = true;
    public int maxBackupCount = 5;
    
    [Header("文件设置")]
    public string fileExtension = ".vrm_save";
    public SerializationFormat serializationFormat = SerializationFormat.Compressed;
    public bool encryptData = false;
    
    [Header("性能设置")]
    public bool enableCaching = true;
    public int cacheSize = 50;
    public float cacheExpireTime = 300f; // 5分钟
}
```

#### **核心存储API**
```csharp
public class CharacterDataStorage
{
    // 初始化存储系统
    public static void Initialize(StorageConfig config)
    
    // 数据保存
    public static async Task<bool> SaveDataAsync(string key, CharacterData data, StorageType storageType = StorageType.PersistentData)
    public static bool SaveData(string key, CharacterData data, StorageType storageType = StorageType.PersistentData)
    
    // 数据加载
    public static async Task<CharacterData?> LoadDataAsync(string key, StorageType storageType = StorageType.PersistentData)
    public static CharacterData? LoadData(string key, StorageType storageType = StorageType.PersistentData)
    
    // 数据管理
    public static bool DeleteData(string key, StorageType storageType = StorageType.PersistentData)
    public static List<string> GetAllKeys(StorageType storageType = StorageType.PersistentData)
    public static bool DataExists(string key, StorageType storageType = StorageType.PersistentData)
    
    // 备份管理
    public static bool CreateBackup(string key)
    public static bool RestoreFromBackup(string key, int backupIndex = 0)
    public static List<BackupInfo> GetBackupList(string key)
    
    // 缓存管理
    public static void ClearCache()
    public static void SetCacheExpireTime(float seconds)
}
```

---

### 5. **VRM资源管理组件**

#### **VRMModelManager.cs** - VRM模型管理器
**文件位置**: `Assets/Scripts/Data/VRMModelManager.cs`  
**脚本行数**: 383行  
**主要职责**: VRM模型的生命周期管理

##### **功能特性**
- 🔄 **模型加载管理** - 异步加载VRM模型文件
- 📦 **实例池管理** - 复用VRM实例，优化性能
- 🎭 **组件自动配置** - 自动添加必要的控制器组件
- 🔍 **模型验证** - 检查模型完整性和兼容性
- 📊 **内存优化** - 自动清理未使用的模型资源

##### **核心API**
```csharp
public class VRMModelManager : MonoBehaviour
{
    // 模型加载
    public async Task<GameObject> LoadVRMModelAsync(string filePath, LoadOptions options = null)
    public async Task<GameObject> LoadVRMFromBytesAsync(byte[] vrmData, string modelName = "VRMModel")
    
    // 模型管理
    public void RegisterModel(GameObject vrmModel, string identifier)
    public GameObject GetModel(string identifier)
    public void UnloadModel(string identifier)
    public void UnloadAllModels()
    
    // 实例池
    public GameObject GetPooledInstance(string modelIdentifier)
    public void ReturnToPool(GameObject instance)
    public void ClearPool()
    
    // 模型信息
    public VRMModelInfo GetModelInfo(string identifier)
    public List<string> GetLoadedModelIdentifiers()
    public bool IsModelLoaded(string identifier)
}

[System.Serializable]
public class LoadOptions
{
    public bool autoSetupControllers = true;
    public bool enablePhysics = true;
    public bool optimizeForMobile = false;
    public int maxTextureSize = 2048;
    public bool generateMipMaps = true;
}
```

#### **VRMStateManager.cs** - VRM状态管理器
**文件位置**: `Assets/Scripts/Data/VRMStateManager.cs`  
**脚本行数**: 264行  
**主要职责**: 管理VRM模型的运行时状态

##### **状态类型**
```csharp
public enum VRMState
{
    Uninitialized,  // 未初始化
    Loading,        // 加载中
    Ready,          // 就绪状态
    Active,         // 活跃状态
    Paused,         // 暂停状态
    Error,          // 错误状态
    Disposing       // 释放中
}

[System.Serializable]
public class VRMStateData
{
    public VRMState currentState;
    public Vector3 position;
    public Quaternion rotation;
    public Vector3 scale;
    public Dictionary<string, float> blendShapeWeights;
    public Dictionary<string, object> customProperties;
    public float timestamp;
}
```

##### **使用示例**
```csharp
var stateManager = GetComponent<VRMStateManager>();

// 监听状态变化
stateManager.OnStateChanged += (oldState, newState) => {
    Debug.Log($"VRM状态从 {oldState} 变更为 {newState}");
};

// 保存当前状态
var currentState = stateManager.CaptureCurrentState();
stateManager.SaveState("checkpoint_01", currentState);

// 恢复状态
var savedState = stateManager.LoadState("checkpoint_01");
if (savedState != null)
{
    stateManager.RestoreState(savedState);
}

// 状态查询
bool isReady = stateManager.IsInState(VRMState.Ready);
var stateHistory = stateManager.GetStateHistory();
```

#### **VRMAssetManager.cs** - VRM资源管理器
**文件位置**: `Assets/Scripts/Data/VRMAssetManager.cs`  
**脚本行数**: 282行  
**主要职责**: VRM相关资源的管理和优化

##### **资源类型管理**
- 🖼️ **纹理资源** - 角色贴图、材质纹理管理
- 🎨 **材质资源** - 材质实例化和共享管理
- 🦴 **网格资源** - 3D网格的加载和缓存
- 🎭 **动画资源** - 表情动画和骨骼动画
- 🌸 **物理资源** - SpringBone和碰撞体配置

#### **VRMRuntimeLoader.cs** - 运行时加载器
**文件位置**: `Assets/Scripts/Data/VRMRuntimeLoader.cs`  
**脚本行数**: 367行  
**主要职责**: 运行时动态加载VRM文件

##### **支持的加载方式**
```csharp
// 从文件路径加载
public async Task<GameObject> LoadFromFileAsync(string filePath)

// 从字节数组加载
public async Task<GameObject> LoadFromBytesAsync(byte[] vrmData)

// 从URL下载并加载
public async Task<GameObject> LoadFromURLAsync(string url)

// 从Resources加载
public GameObject LoadFromResources(string resourcePath)

// 从StreamingAssets加载
public async Task<GameObject> LoadFromStreamingAssetsAsync(string fileName)
```

#### **VRMFileCapture.cs** - 文件捕获器
**文件位置**: `Assets/Scripts/Data/VRMFileCapture.cs`  
**脚本行数**: 287行  
**主要职责**: VRM文件的捕获、预览和元数据提取

---

## 🚀 **完整使用流程**

### 1. **系统初始化**
```csharp
// 配置存储系统
var storageConfig = new StorageConfig
{
    primaryStorage = StorageType.PersistentData,
    backupStorage = StorageType.PlayerPrefs,
    enableAutoBackup = true,
    serializationFormat = SerializationFormat.Compressed
};
CharacterDataStorage.Initialize(storageConfig);

// 获取数据管理器
var dataManager = CharacterDataManager.Instance;
```

### 2. **保存角色数据**
```csharp
// 收集当前角色数据
var characterData = new CharacterData
{
    characterName = "我的角色",
    description = "测试角色数据",
    createdTime = System.DateTime.Now,
    facialData = faceController.GetFacialParametersData(),
    clothingData = new ClothingData
    {
        currentOutfit = clothBinder.GetCurrentOutfit(),
        ownedClothes = clothBinder.GetOwnedClothes(),
        savedOutfits = GetSavedOutfits()
    }
};

// 异步保存
try 
{
    bool success = await dataManager.SaveCharacterAsync(characterData, "slot_01");
    if (success)
    {
        Debug.Log("角色数据保存成功！");
    }
}
catch (System.Exception ex)
{
    Debug.LogError($"保存失败: {ex.Message}");
}
```

### 3. **加载角色数据**
```csharp
// 异步加载
try
{
    var loadedData = await dataManager.LoadCharacterAsync("slot_01");
    if (loadedData.HasValue)
    {
        var data = loadedData.Value;
        
        // 应用面部数据
        faceController.ApplyFacialData(data.facialData);
        
        // 应用服装数据
        foreach (var outfit in data.clothingData.currentOutfit)
        {
            clothBinder.WearClothByType(
                System.Enum.Parse<VRM10ClothType>(outfit.Key), 
                outfit.Value
            );
        }
        
        Debug.Log($"角色 '{data.characterName}' 加载成功！");
    }
}
catch (System.Exception ex)
{
    Debug.LogError($"加载失败: {ex.Message}");
}
```

### 4. **数据管理操作**
```csharp
// 获取所有保存的角色
var savedSlots = dataManager.GetSavedSlots();
foreach (var slot in savedSlots)
{
    Debug.Log($"发现保存的角色插槽: {slot}");
}

// 检查特定插槽是否存在数据
bool hasData = dataManager.HasSavedData("slot_01");

// 删除角色数据
bool deleted = await dataManager.DeleteCharacterAsync("old_character");

// 清空所有数据（慎用）
dataManager.ClearAllData();
```

---

## ⚠️ **注意事项和最佳实践**

### 性能优化建议
1. **异步操作**: 大数据量时优先使用异步API避免主线程阻塞
2. **数据压缩**: 使用压缩格式减少存储空间和加载时间
3. **缓存管理**: 合理设置缓存大小和过期时间
4. **批量操作**: 多个数据操作时考虑批量处理

### 数据安全
1. **备份策略**: 启用自动备份防止数据丢失
2. **数据验证**: 加载前验证数据完整性
3. **版本兼容**: 处理不同版本数据的升级
4. **错误恢复**: 实现数据损坏时的恢复机制

### 存储选择
- **本地文件**: 适合大量数据，支持直接编辑
- **PlayerPrefs**: 适合少量配置数据，跨平台兼容好
- **持久化路径**: 适合用户数据，不会被清理
- **StreamingAssets**: 适合只读的预设数据

---

## 📚 **相关文档**
- [VRoid核心系统文档](01_VRoid核心系统文档.md)
- [编辑器工具文档](03_编辑器工具文档.md)
- [API参考文档](07_API参考文档.md)

---

**版本**: 1.0.0  
**最后更新**: 2025-01-22  
**维护者**: 数据管理系统开发团队 