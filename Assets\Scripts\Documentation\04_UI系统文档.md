# 🖥️ UI系统文档

## 📋 **系统概述**

UI系统负责用户界面交互、场景管理和可视化辅助功能。包含5个核心脚本，提供场景切换、文件选择、第三人称场景管理等功能，为用户提供直观友好的操作界面。

### 🏗️ **UI系统架构**
```
UI系统架构
├── 场景管理层
│   ├── SceneTransitionManager - 场景转换管理器
│   ├── ThirdPersonSceneLoader - 第三人称场景加载器
│   └── ThirdPersonSceneSetup - 第三人称场景设置
│
└── 交互界面层
    ├── VRMFileSelector - VRM文件选择器
    └── SpawnPointGizmo - 生成点可视化工具
```

---

## 🎯 **核心组件详解**

### 1. **SceneTransitionManager.cs** - 场景转换管理器
**文件位置**: `Assets/Scripts/UI/SceneTransitionManager.cs`  
**脚本行数**: 485行  
**主要职责**: 管理场景间切换和数据传递

#### **主要功能**
- 🔄 **场景切换** - 支持异步场景加载和卸载
- 📦 **数据传递** - 场景间角色数据的无缝传递
- 🎭 **状态保持** - 保持VRM模型状态和用户设置
- ⚡ **加载优化** - 预加载和缓存机制提升性能
- 🛡️ **错误处理** - 完善的场景切换错误恢复机制

#### **核心API**
```csharp
public class SceneTransitionManager : MonoBehaviour
{
    /// <summary>切换到指定场景</summary>
    /// <param name="sceneName">目标场景名称</param>
    /// <param name="preserveVRMData">是否保持VRM数据</param>
    /// <returns>切换是否成功</returns>
    public async Task<bool> TransitionToScene(string sceneName, bool preserveVRMData = true)
    
    /// <summary>携带数据切换场景</summary>
    /// <param name="sceneName">目标场景名称</param>
    /// <param name="characterData">要传递的角色数据</param>
    /// <returns>切换是否成功</returns>
    public async Task<bool> TransitionWithData(string sceneName, CharacterData characterData)
    
    /// <summary>获取场景切换状态</summary>
    /// <returns>当前切换状态</returns>
    public SceneTransitionState GetTransitionState()
    
    /// <summary>预加载场景</summary>
    /// <param name="sceneName">要预加载的场景名称</param>
    public void PreloadScene(string sceneName)
}
```

#### **使用示例**
```csharp
// 基础场景切换
var transitionManager = FindObjectOfType<SceneTransitionManager>();
bool success = await transitionManager.TransitionToScene("AvatarRuntime");

// 携带数据切换场景
var characterData = VRM10UnifiedManager.Instance.GetCurrentCharacterData();
await transitionManager.TransitionWithData("ThirdPersonScene", characterData);

// 预加载场景提升性能
transitionManager.PreloadScene("NextScene");
```

#### **支持的场景类型**
- **AvatarRuntime**: 角色编辑和自定义场景
- **ThirdPersonScene**: 第三人称控制场景  
- **MainMenu**: 主菜单场景
- **SampleScene**: 示例和测试场景

---

### 2. **ThirdPersonSceneLoader.cs** - 第三人称场景加载器
**文件位置**: `Assets/Scripts/UI/ThirdPersonSceneLoader.cs`  
**脚本行数**: 611行  
**主要职责**: 专门负责第三人称场景的加载和初始化

#### **主要功能**
- 🎮 **场景初始化** - 设置第三人称控制环境
- 🎭 **VRM集成** - 将VRM模型集成到第三人称系统
- 🎯 **生成点管理** - 管理角色生成位置和朝向
- 📊 **性能监控** - 监控场景加载性能
- 🔧 **自动配置** - 自动配置相机、光照和控制器

#### **加载流程**
```csharp
public class ThirdPersonSceneLoader : MonoBehaviour
{
    /// <summary>加载第三人称场景的完整流程</summary>
    public async Task LoadThirdPersonScene()
    {
        // 1. 场景预检查
        if (!ValidateSceneRequirements()) return;
        
        // 2. 加载场景资源
        await LoadSceneAssets();
        
        // 3. 设置VRM模型
        SetupVRMModel();
        
        // 4. 配置第三人称控制器
        ConfigureThirdPersonController();
        
        // 5. 初始化场景环境
        InitializeSceneEnvironment();
        
        // 6. 完成加载
        OnSceneLoadComplete();
    }
}
```

#### **配置选项**
```csharp
[System.Serializable]
public class ThirdPersonSceneConfig
{
    [Header("生成设置")]
    public Transform spawnPoint;           // 角色生成点
    public Vector3 spawnRotation;          // 生成朝向
    public bool useRandomSpawn = false;    // 是否随机生成
    
    [Header("相机设置")]
    public CameraMode cameraMode = CameraMode.ThirdPerson;
    public float cameraDistance = 5f;      // 相机距离
    public float cameraHeight = 2f;        // 相机高度
    
    [Header("环境设置")]
    public bool enableDynamicLighting = true;
    public bool enablePostProcessing = true;
    public Skybox sceneSkybox;             // 场景天空盒
}
```

---

### 3. **VRMFileSelector.cs** - VRM文件选择器
**文件位置**: `Assets/Scripts/UI/VRMFileSelector.cs`  
**脚本行数**: 254行  
**主要职责**: 提供用户友好的VRM文件选择界面

#### **主要功能**
- 📁 **文件浏览** - 浏览本地VRM文件
- 🔍 **文件预览** - 显示VRM模型缩略图和信息
- ✅ **格式验证** - 验证VRM文件格式和版本
- 📋 **历史记录** - 记录最近使用的文件
- 🚀 **快速加载** - 一键加载选中的VRM模型

#### **界面组件**
```csharp
[System.Serializable]
public class VRMFileSelectorUI
{
    [Header("UI组件")]
    public Button browseButton;            // 浏览按钮
    public Text selectedFileText;          // 选中文件显示
    public Image previewImage;             // 预览图像
    public Button loadButton;              // 加载按钮
    public Dropdown recentFilesDropdown;   // 最近文件下拉菜单
    
    [Header("预览设置")]
    public RenderTexture previewRenderTexture; // 预览渲染纹理
    public Camera previewCamera;               // 预览相机
    public float previewRotationSpeed = 30f;  // 预览旋转速度
}
```

#### **文件操作API**
```csharp
public class VRMFileSelector : MonoBehaviour
{
    /// <summary>打开文件选择对话框</summary>
    public void OpenFileDialog()
    
    /// <summary>选择指定VRM文件</summary>
    /// <param name="filePath">VRM文件路径</param>
    /// <returns>选择是否成功</returns>
    public bool SelectVRMFile(string filePath)
    
    /// <summary>加载选中的VRM文件</summary>
    /// <returns>加载是否成功</returns>
    public async Task<bool> LoadSelectedVRM()
    
    /// <summary>获取最近使用的文件列表</summary>
    /// <returns>文件路径列表</returns>
    public List<string> GetRecentFiles()
    
    /// <summary>清除选择</summary>
    public void ClearSelection()
}
```

#### **支持的文件格式**
- **VRM 1.0**: 推荐格式，完全支持
- **VRM 0.x**: 兼容支持，可能需要转换
- **GLB/GLTF**: 部分支持，限制功能

---

### 4. **ThirdPersonSceneSetup.cs** - 第三人称场景设置
**文件位置**: `Assets/Scripts/UI/ThirdPersonSceneSetup.cs`  
**脚本行数**: 281行  
**主要职责**: 第三人称场景的UI设置界面

#### **主要功能**
- ⚙️ **控制器设置** - 调整第三人称控制参数
- 🎥 **相机配置** - 自定义相机视角和行为
- 🎨 **视觉效果** - 配置后处理和视觉特效
- 💾 **设置保存** - 保存和加载用户设置
- 🎮 **输入映射** - 自定义控制按键映射

#### **设置界面组件**
```csharp
[System.Serializable]
public class ThirdPersonSetupUI
{
    [Header("控制设置")]
    public Slider moveSpeedSlider;         // 移动速度滑块
    public Slider jumpHeightSlider;        // 跳跃高度滑块
    public Toggle sprintToggle;            // 冲刺开关
    
    [Header("相机设置")]
    public Slider cameraDistanceSlider;    // 相机距离滑块
    public Slider cameraSensitivitySlider; // 相机灵敏度滑块
    public Toggle cameraInvertYToggle;     // Y轴反转开关
    
    [Header("图形设置")]
    public Dropdown qualityDropdown;       // 质量设置下拉菜单
    public Toggle postProcessingToggle;    // 后处理开关
    public Slider fovSlider;               // 视野角度滑块
}
```

#### **设置管理API**
```csharp
public class ThirdPersonSceneSetup : MonoBehaviour
{
    /// <summary>应用当前设置</summary>
    public void ApplySettings()
    
    /// <summary>重置为默认设置</summary>
    public void ResetToDefaults()
    
    /// <summary>保存设置到本地</summary>
    public void SaveSettings()
    
    /// <summary>从本地加载设置</summary>
    public void LoadSettings()
    
    /// <summary>获取当前设置数据</summary>
    /// <returns>设置数据结构</returns>
    public ThirdPersonSettings GetCurrentSettings()
}
```

---

### 5. **SpawnPointGizmo.cs** - 生成点可视化工具
**文件位置**: `Assets/Scripts/UI/SpawnPointGizmo.cs`  
**脚本行数**: 94行  
**主要职责**: 在Scene视图中可视化角色生成点

#### **主要功能**
- 📍 **位置标记** - 在Scene视图显示生成点位置
- 🧭 **方向指示** - 显示角色生成时的朝向
- 🎨 **可视化定制** - 自定义Gizmo颜色和样式
- 📏 **距离测量** - 显示生成点间的距离信息
- 🔧 **编辑辅助** - 在编辑器中快速调整生成点

#### **可视化配置**
```csharp
[System.Serializable]
public class GizmoSettings
{
    [Header("外观设置")]
    public Color gizmoColor = Color.green;     // Gizmo颜色
    public float gizmoSize = 1.0f;             // Gizmo大小
    public bool showDirection = true;          // 显示朝向
    public bool showLabel = true;              // 显示标签
    
    [Header("形状设置")]
    public GizmoShape shape = GizmoShape.Sphere; // Gizmo形状
    public bool wireframe = false;             // 线框模式
    public float directionLength = 2.0f;       // 方向箭头长度
}

public enum GizmoShape
{
    Sphere,     // 球形
    Cube,       // 立方体
    Arrow,      // 箭头
    Cross       // 十字
}
```

#### **使用方法**
```csharp
public class SpawnPointGizmo : MonoBehaviour
{
    /// <summary>在Scene视图绘制Gizmo</summary>
    void OnDrawGizmos()
    {
        if (!showInSceneView) return;
        
        // 设置Gizmo颜色和样式
        Gizmos.color = gizmoSettings.gizmoColor;
        
        // 绘制生成点
        DrawSpawnPoint();
        
        // 绘制方向指示
        if (gizmoSettings.showDirection)
        {
            DrawDirection();
        }
        
        // 绘制标签
        if (gizmoSettings.showLabel)
        {
            DrawLabel();
        }
    }
}
```

---

## 🔄 **UI系统工作流程**

### 典型用户流程

#### **1. VRM模型加载流程**
```
用户操作 → VRMFileSelector → 文件验证 → 模型预览 → 确认加载 → SceneTransitionManager → 场景切换
```

#### **2. 第三人称场景进入流程**  
```
场景切换请求 → ThirdPersonSceneLoader → 资源加载 → VRM集成 → 控制器配置 → 场景初始化完成
```

#### **3. 设置调整流程**
```
用户设置 → ThirdPersonSceneSetup → 参数验证 → 实时预览 → 应用设置 → 本地保存
```

### 数据流转图
```mermaid
graph TD
    A[用户界面] --> B[VRMFileSelector]
    B --> C[SceneTransitionManager]
    C --> D[ThirdPersonSceneLoader]
    D --> E[ThirdPersonSceneSetup]
    E --> F[VRM模型应用]
    F --> G[完成初始化]
    
    H[SpawnPointGizmo] --> D
    I[设置数据] --> E
    J[场景状态] --> C
```

---

## 🎨 **UI界面设计规范**

### 视觉设计原则
1. **简洁明了**: 界面布局清晰，功能一目了然
2. **一致性**: 使用统一的颜色、字体和控件样式
3. **响应式**: 适配不同分辨率和屏幕比例
4. **可访问性**: 支持键盘导航和辅助功能

### 颜色规范
- **主色调**: #4A90E2 (蓝色系)
- **辅助色**: #7ED321 (绿色 - 成功)
- **警告色**: #F5A623 (橙色 - 警告)
- **错误色**: #D0021B (红色 - 错误)
- **背景色**: #F8F9FA (浅灰色)
- **文字色**: #333333 (深灰色)

### 控件规范
```csharp
// 标准按钮样式
public static class UIStyles
{
    public static readonly Color PRIMARY_BUTTON = new Color(0.29f, 0.56f, 0.89f);
    public static readonly Color SUCCESS_BUTTON = new Color(0.49f, 0.82f, 0.13f);
    public static readonly Color WARNING_BUTTON = new Color(0.96f, 0.65f, 0.14f);
    public static readonly Color DANGER_BUTTON = new Color(0.82f, 0.01f, 0.11f);
    
    public static readonly Vector2 BUTTON_SIZE = new Vector2(120, 40);
    public static readonly Vector2 LARGE_BUTTON_SIZE = new Vector2(180, 50);
    public static readonly Vector2 SMALL_BUTTON_SIZE = new Vector2(80, 30);
}
```

---

## 📱 **平台适配**

### 桌面平台 (Windows/Mac/Linux)
- **全功能支持**: 所有UI功能完全可用
- **文件对话框**: 原生系统文件选择器
- **键盘快捷键**: 支持常用快捷键操作
- **拖拽支持**: 支持拖拽VRM文件到界面

### WebGL平台
- **限制功能**: 文件选择功能受限
- **替代方案**: 使用HTML5 File API
- **性能优化**: 减少UI更新频率
- **兼容性**: 确保跨浏览器兼容

### 移动平台 (Android/iOS)
- **触控优化**: 触控友好的UI设计
- **屏幕适配**: 响应式布局适配
- **手势支持**: 支持常见手势操作
- **性能考虑**: 简化UI效果提升性能

---

## ⚠️ **注意事项和最佳实践**

### 性能优化
1. **UI更新频率**: 避免每帧更新UI，使用事件驱动
2. **资源管理**: 及时释放不用的UI资源
3. **批量操作**: 批量更新UI状态而非逐个更新
4. **预加载**: 预加载常用的UI Prefab

### 用户体验
1. **加载提示**: 长时间操作显示加载进度
2. **错误处理**: 友好的错误提示和恢复建议
3. **状态保持**: 保持用户的操作状态和设置
4. **快捷操作**: 提供快捷键和右键菜单

### 开发建议
1. **模块化设计**: UI组件独立可复用
2. **事件系统**: 使用UnityEvent或C#事件解耦
3. **数据绑定**: 使用MVVM模式管理UI数据
4. **测试友好**: 支持单元测试和自动化测试

### 常见问题解决
```csharp
// UI组件找不到
var button = transform.Find("Button")?.GetComponent<Button>();
if (button == null)
{
    Debug.LogError("❌ 找不到Button组件，请检查UI结构");
    return;
}

// 场景切换失败
public async Task<bool> SafeSceneTransition(string sceneName)
{
    try
    {
        // 验证场景存在
        if (!Application.CanStreamedLevelBeLoaded(sceneName))
        {
            Debug.LogError($"❌ 场景不存在: {sceneName}");
            return false;
        }
        
        // 执行切换
        await SceneManager.LoadSceneAsync(sceneName);
        return true;
    }
    catch (System.Exception ex)
    {
        Debug.LogError($"❌ 场景切换失败: {ex.Message}");
        return false;
    }
}

// UI状态同步
public void SyncUIState()
{
    // 确保在主线程执行
    if (!UnityEngine.MainThreadWatcher.Check())
    {
        UnityMainThreadDispatcher.Instance().Enqueue(SyncUIState);
        return;
    }
    
    // 更新UI状态
    UpdateUIComponents();
}
```

---

## 📚 **相关文档**
- [VRoid核心系统文档](01_VRoid核心系统文档.md) - 核心系统集成
- [第三人称系统文档](05_第三人称系统文档.md) - 第三人称功能详解
- [API参考文档](07_API参考文档.md) - UI相关API参考
- [FAQ问题解答](08_FAQ问题解答.md) - UI常见问题

---

**版本**: 1.0.0  
**最后更新**: 2025-01-22  
**UI组件数量**: 5个核心UI脚本  
**维护者**: UI系统开发团队 