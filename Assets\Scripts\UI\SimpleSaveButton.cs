using UnityEngine;
using UnityEngine.UI;
using UniVRM10;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization.UI
{
    /// <summary>
    /// 简单的VRM保存按钮
    /// 直接调用VRMStateManager保存当前VRM状态
    /// </summary>
    public class SimpleSaveButton : MonoBehaviour
    {
        [Header("设置")]
        [SerializeField] private bool debugMode = true;
        
        /// <summary>
        /// 保存VRM状态（供Button的OnClick事件调用）
        /// </summary>
        public void SaveVRMState()
        {
            LogDebug("🎯 开始保存VRM状态...");
            
            // 1. 查找VRM对象
            var vrmInstance = FindObjectOfType<Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug("❌ 场景中未找到VRM对象！");
                return;
            }
            
            LogDebug($"🎭 找到VRM对象: {vrmInstance.name}");
            
            // 2. 获取VRMStateManager并保存状态
            var stateManager = VRMStateManager.Instance;
            if (stateManager == null)
            {
                LogDebug("❌ VRMStateManager未找到！请确保场景中有VRMStateManager组件。");
                return;
            }
            
            // 3. 捕获VRM渲染状态
            var renderState = stateManager.CaptureVRMRenderState(vrmInstance.gameObject);
            if (renderState != null)
            {
                LogDebug("✅ VRM状态保存成功！");
                LogDebug($"   BlendShapes: {renderState.blendShapes.Count}");
                LogDebug($"   Materials: {renderState.materialProperties.Count}");
                LogDebug($"   Bones: {renderState.boneTransforms.Count}");
            }
            else
            {
                LogDebug("❌ VRM状态保存失败！");
            }
        }
        
        /// <summary>
        /// 保存VRM状态和文件数据（完整保存）
        /// </summary>
        public void SaveVRMComplete()
        {
            StartCoroutine(SaveVRMCompleteCoroutine());
        }
        
        private System.Collections.IEnumerator SaveVRMCompleteCoroutine()
        {
            LogDebug("🎯 开始完整保存VRM数据...");
            
            // 1. 保存VRM状态
            SaveVRMState();
            
            yield return null;
            
            // 2. 保存VRM文件数据
            var vrmInstance = FindObjectOfType<Vrm10Instance>();
            if (vrmInstance != null)
            {
                string vrmFilePath = "Assets/Prefabs/model/xa初始模型.vrm";
                var runtimeLoader = VRMRuntimeLoader.Instance;
                
                if (runtimeLoader != null && System.IO.File.Exists(vrmFilePath))
                {
                    var saveTask = runtimeLoader.SaveVRMFromFile(vrmFilePath, vrmInstance.gameObject);
                    while (!saveTask.IsCompleted)
                    {
                        yield return null;
                    }
                    
                    if (saveTask.Result)
                    {
                        LogDebug("✅ VRM文件数据保存成功");
                    }
                    else
                    {
                        LogDebug("❌ VRM文件数据保存失败");
                    }
                }
            }
            
            LogDebug("🎉 完整VRM数据保存完成！");
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[SimpleSaveButton] {message}");
            }
        }
    }
}
