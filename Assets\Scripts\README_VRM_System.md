# VRM捏脸换装系统重构 - 使用指南

## 📋 系统概述

这是一个重构后的VRM捏脸换装系统，专注于简化场景间数据传递，提供清晰的架构和可靠的数据管理。

### 🎯 主要功能
- **AvatarRuntime场景**: VRM模型捏脸和换装编辑
- **ThirdPersonTestScene场景**: 第三人称控制器测试
- **数据传递**: 场景间无缝数据传递
- **数据持久化**: 可选的文件保存功能
- **第三人称控制器**: 与Unity官方控制器集成

## 🏗️ 系统架构

### 核心组件
```
Assets/Scripts/Core/
├── CharacterParameters.cs      # 角色参数数据结构
├── CharacterDataBridge.cs      # 场景间数据桥接
├── VRMDataCollector.cs         # VRM数据收集器
├── VRMDataApplier.cs          # VRM数据应用器
└── SceneController.cs         # 场景控制器
```

### UI组件
```
Assets/Scripts/UI/
├── SaveButton.cs              # 保存按钮
└── SceneTransitionButton.cs   # 场景切换按钮
```

### 场景管理
```
Assets/Scripts/Scene/
└── ThirdPersonSceneManager.cs # 第三人称场景管理器
```

### 控制器集成
```
Assets/Scripts/Controller/
└── VRMThirdPersonController.cs # VRM第三人称控制器
```

### 数据持久化
```
Assets/Scripts/Data/
└── DataPersistence.cs         # 数据持久化管理
```

## 🚀 快速开始

### 1. AvatarRuntime场景设置

1. **添加保存按钮**:
   ```csharp
   // 在UI Canvas中添加Button，然后添加SaveButton组件
   var saveButton = button.AddComponent<SaveButton>();
   saveButton.SetTargetVRM(vrmGameObject);
   ```

2. **添加场景切换按钮**:
   ```csharp
   // 在UI Canvas中添加Button，然后添加SceneTransitionButton组件
   var transitionButton = button.AddComponent<SceneTransitionButton>();
   transitionButton.SetTargetVRM(vrmGameObject);
   transitionButton.SetTargetScene("ThirdPersonTestScene");
   ```

### 2. ThirdPersonTestScene场景设置

1. **添加场景管理器**:
   ```csharp
   // 创建空GameObject，添加ThirdPersonSceneManager组件
   var sceneManager = gameObject.AddComponent<ThirdPersonSceneManager>();
   sceneManager.defaultVRMPrefab = yourVRMPrefab;
   ```

2. **添加第三人称控制器**:
   ```csharp
   // 在VRM对象上添加VRMThirdPersonController组件
   var controller = vrmObject.AddComponent<VRMThirdPersonController>();
   ```

## 📊 数据流程

### 保存流程
```
VRM对象 → VRMDataCollector → CharacterParameters → CharacterDataBridge
                                                  ↓
                                            DataPersistence (可选)
```

### 加载流程
```
CharacterDataBridge → CharacterParameters → VRMDataApplier → VRM对象
```

### 场景切换流程
```
AvatarRuntime → 保存数据 → 切换场景 → ThirdPersonTestScene → 自动加载数据
```

## 🔧 API参考

### CharacterDataBridge
```csharp
// 保存角色参数
CharacterDataBridge.Instance.SetCharacterParameters(parameters);

// 获取角色参数
var parameters = CharacterDataBridge.Instance.GetCharacterParameters();

// 检查是否有有效数据
bool hasData = CharacterDataBridge.Instance.HasValidData();
```

### VRMDataCollector
```csharp
// 收集VRM数据
var collector = GetComponent<VRMDataCollector>();
var parameters = collector.CollectFromVRM(vrmObject);
```

### VRMDataApplier
```csharp
// 应用VRM数据
var applier = GetComponent<VRMDataApplier>();
applier.ApplyToVRM(vrmObject, parameters, (success) => {
    Debug.Log($"应用结果: {success}");
});
```

### SceneController
```csharp
// 保存并切换场景
SceneController.Instance.SaveAndGoToTestScene(vrmObject);

// 直接切换场景
SceneController.Instance.GoToScene("SceneName");
```

## 🎮 第三人称控制器集成

### 安装Unity官方第三人称控制器

1. **通过Package Manager安装**:
   - 打开 Window > Package Manager
   - 切换到 Unity Registry
   - 搜索 "Starter Assets - Third Person Character Controller"
   - 点击 Install

2. **通过Asset Store安装**:
   - 访问: https://assetstore.unity.com/packages/essentials/starter-assets-third-person-character-controller-196526
   - 下载并导入到项目

### 使用VRMThirdPersonController

```csharp
// 自动设置
var controller = vrmObject.AddComponent<VRMThirdPersonController>();
controller.InitializeController();

// 手动设置
controller.SetVRMObject(vrmObject);
controller.defaultAnimatorController = yourAnimatorController;
controller.defaultAvatar = yourAvatar;
```

## 🧪 测试指南

### 1. 基础功能测试

1. **数据收集测试**:
   - 在AvatarRuntime场景中调整VRM参数
   - 点击保存按钮
   - 检查控制台日志确认数据收集成功

2. **场景切换测试**:
   - 点击场景切换按钮
   - 确认场景成功切换到ThirdPersonTestScene
   - 检查VRM模型是否正确加载

3. **数据应用测试**:
   - 在ThirdPersonTestScene中检查VRM模型
   - 确认面部参数和服装配置正确应用
   - 检查控制台日志确认应用成功

### 2. 错误处理测试

1. **无VRM对象测试**:
   - 在没有VRM对象的情况下点击保存
   - 确认显示适当的错误信息

2. **无数据测试**:
   - 直接进入ThirdPersonTestScene（不通过保存）
   - 确认加载默认VRM模型

3. **数据损坏测试**:
   - 手动修改保存的数据文件
   - 确认系统能够处理损坏的数据

### 3. 性能测试

1. **大量参数测试**:
   - 设置大量BlendShape权重
   - 测试保存和加载性能

2. **复杂服装测试**:
   - 穿戴多件服装
   - 测试数据收集和应用性能

## 🐛 常见问题

### Q: 场景切换后VRM模型没有正确加载？
A: 检查以下几点：
- 确认CharacterDataBridge中有有效数据
- 检查ThirdPersonSceneManager是否正确设置
- 查看控制台错误日志

### Q: BlendShape权重没有正确应用？
A: 检查以下几点：
- 确认VRM模型完全初始化
- 检查BlendShape名称是否匹配
- 确认权重值在有效范围内

### Q: 第三人称控制器无法正常工作？
A: 检查以下几点：
- 确认已安装Unity官方第三人称控制器包
- 检查VRMThirdPersonController组件设置
- 确认Input System已启用

### Q: 数据保存失败？
A: 检查以下几点：
- 确认有写入权限
- 检查磁盘空间
- 查看DataPersistence组件日志

## 📈 性能优化建议

1. **数据收集优化**:
   - 只收集有效的BlendShape权重（>0.001）
   - 限制材质属性收集范围
   - 使用异步操作避免卡顿

2. **数据应用优化**:
   - 批量应用BlendShape权重
   - 缓存材质引用
   - 使用协程分帧处理

3. **内存优化**:
   - 及时清理临时对象
   - 使用对象池管理频繁创建的对象
   - 定期调用GC.Collect()

## 🔄 版本更新

### v1.0.0 (当前版本)
- ✅ 基础数据收集和应用功能
- ✅ 场景间数据传递
- ✅ 简化的UI组件
- ✅ 第三人称控制器集成
- ✅ 数据持久化支持

### 计划功能
- 🔄 更多服装系统支持
- 🔄 动画参数保存
- 🔄 批量角色管理
- 🔄 云端数据同步

## 📞 技术支持

如果遇到问题，请：
1. 检查控制台日志
2. 确认组件设置正确
3. 参考本文档的常见问题部分
4. 使用调试功能（右键菜单中的Context Menu选项）

---

**注意**: 这是一个重构后的简化系统，专注于核心功能的稳定性和可维护性。如需扩展功能，请基于现有架构进行开发。
