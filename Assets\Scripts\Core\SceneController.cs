using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using VRoidFaceCustomization.Core;

namespace VRoidFaceCustomization.Core
{
    /// <summary>
    /// 场景控制器 - 简化的场景切换管理
    /// </summary>
    public class SceneController : MonoBehaviour
    {
        [Header("场景设置")]
        [SerializeField] private string avatarRuntimeSceneName = "AvatarRuntime";
        [SerializeField] private string thirdPersonTestSceneName = "ThirdPersonTestScene";
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        
        [Header("当前状态")]
        [SerializeField] private bool isTransitioning = false;
        [SerializeField] private string currentSceneName = "";
        
        #region 单例模式
        private static SceneController _instance;
        public static SceneController Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<SceneController>();
                    if (_instance == null)
                    {
                        GameObject controllerObject = new GameObject("SceneController");
                        _instance = controllerObject.AddComponent<SceneController>();
                        DontDestroyOnLoad(controllerObject);
                        Debug.Log("✅ [SceneController] 创建新实例");
                    }
                }
                return _instance;
            }
        }
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                currentSceneName = SceneManager.GetActiveScene().name;
                LogDebug($"✅ SceneController 初始化完成，当前场景: {currentSceneName}");
            }
            else if (_instance != this)
            {
                LogDebug("🗑️ 销毁重复的SceneController实例");
                Destroy(gameObject);
            }
        }
        #endregion
        
        #region 公共接口
        /// <summary>
        /// 保存当前角色数据并切换到测试场景
        /// </summary>
        public void SaveAndGoToTestScene(GameObject vrmObject)
        {
            if (isTransitioning)
            {
                LogDebug("⚠️ 场景切换正在进行中，请稍候");
                return;
            }
            
            LogDebug("🚀 开始保存并切换到测试场景");
            StartCoroutine(SaveAndTransitionCoroutine(vrmObject, thirdPersonTestSceneName));
        }
        
        /// <summary>
        /// 返回到角色编辑场景
        /// </summary>
        public void ReturnToAvatarScene()
        {
            if (isTransitioning)
            {
                LogDebug("⚠️ 场景切换正在进行中，请稍候");
                return;
            }
            
            LogDebug("🔙 返回到角色编辑场景");
            StartCoroutine(TransitionToSceneCoroutine(avatarRuntimeSceneName));
        }
        
        /// <summary>
        /// 直接切换场景（不保存数据）
        /// </summary>
        public void GoToScene(string sceneName)
        {
            if (isTransitioning)
            {
                LogDebug("⚠️ 场景切换正在进行中，请稍候");
                return;
            }
            
            LogDebug($"🔄 切换到场景: {sceneName}");
            StartCoroutine(TransitionToSceneCoroutine(sceneName));
        }
        
        /// <summary>
        /// 获取当前场景名称
        /// </summary>
        public string GetCurrentSceneName()
        {
            return currentSceneName;
        }
        
        /// <summary>
        /// 检查是否正在切换场景
        /// </summary>
        public bool IsTransitioning()
        {
            return isTransitioning;
        }
        #endregion
        
        #region 场景切换逻辑
        /// <summary>
        /// 保存数据并切换场景的协程
        /// </summary>
        private IEnumerator SaveAndTransitionCoroutine(GameObject vrmObject, string targetScene)
        {
            isTransitioning = true;
            
            LogDebug("💾 开始收集角色数据...");
            
            // 获取或创建数据收集器
            var collector = GetOrCreateDataCollector();
            
            // 收集角色参数
            var parameters = collector.CollectFromVRM(vrmObject);
            
            if (parameters == null || !parameters.IsValid())
            {
                LogDebug("❌ 角色数据收集失败");
                isTransitioning = false;
                yield break;
            }
            
            LogDebug($"✅ 角色数据收集成功: {parameters.characterName}");
            LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
            LogDebug($"   👔 服装数量: {parameters.clothingData.GetClothingCount()}件");
            
            // 将数据存储到桥接器
            var bridge = CharacterDataBridge.Instance;
            bridge.SetCharacterParameters(parameters);
            
            LogDebug("📤 数据已存储到桥接器");
            
            // 等待一帧确保数据保存完成
            yield return null;
            
            // 切换场景
            LogDebug($"🔄 切换到场景: {targetScene}");
            yield return StartCoroutine(LoadSceneCoroutine(targetScene));
            
            isTransitioning = false;
            LogDebug("🎉 场景切换完成");
        }
        
        /// <summary>
        /// 简单场景切换的协程
        /// </summary>
        private IEnumerator TransitionToSceneCoroutine(string targetScene)
        {
            isTransitioning = true;
            
            LogDebug($"🔄 切换到场景: {targetScene}");
            yield return StartCoroutine(LoadSceneCoroutine(targetScene));
            
            isTransitioning = false;
            LogDebug("✅ 场景切换完成");
        }
        
        /// <summary>
        /// 加载场景的协程
        /// </summary>
        private IEnumerator LoadSceneCoroutine(string sceneName)
        {
            // 开始异步加载场景
            var asyncLoad = SceneManager.LoadSceneAsync(sceneName);
            
            if (asyncLoad == null)
            {
                LogDebug($"❌ 无法加载场景: {sceneName}");
                yield break;
            }
            
            // 等待场景加载完成
            while (!asyncLoad.isDone)
            {
                LogDebug($"⏳ 场景加载进度: {asyncLoad.progress * 100:F1}%");
                yield return null;
            }
            
            // 更新当前场景名称
            currentSceneName = sceneName;
            LogDebug($"✅ 场景加载完成: {sceneName}");
        }
        #endregion
        
        #region 辅助方法
        /// <summary>
        /// 获取或创建数据收集器
        /// </summary>
        private VRMDataCollector GetOrCreateDataCollector()
        {
            // VRMDataCollector现在是普通类，直接创建实例
            LogDebug("🔧 创建数据收集器");
            return new VRMDataCollector();
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[SceneController] {message}");
            }
        }
        #endregion
        
        #region Unity生命周期
        private void Start()
        {
            LogDebug("🚀 SceneController 启动完成");
        }
        
        private void OnDestroy()
        {
            if (_instance == this)
            {
                _instance = null;
                LogDebug("🗑️ SceneController 实例已销毁");
            }
        }
        #endregion
        
        #region 事件系统
        /// <summary>
        /// 场景切换开始事件
        /// </summary>
        public static event System.Action<string> OnSceneTransitionStart;
        
        /// <summary>
        /// 场景切换完成事件
        /// </summary>
        public static event System.Action<string> OnSceneTransitionComplete;
        
        /// <summary>
        /// 数据保存完成事件
        /// </summary>
        public static event System.Action<CharacterParameters> OnDataSaved;
        
        /// <summary>
        /// 触发场景切换开始事件
        /// </summary>
        private void TriggerSceneTransitionStart(string sceneName)
        {
            OnSceneTransitionStart?.Invoke(sceneName);
        }
        
        /// <summary>
        /// 触发场景切换完成事件
        /// </summary>
        private void TriggerSceneTransitionComplete(string sceneName)
        {
            OnSceneTransitionComplete?.Invoke(sceneName);
        }
        
        /// <summary>
        /// 触发数据保存完成事件
        /// </summary>
        private void TriggerDataSaved(CharacterParameters parameters)
        {
            OnDataSaved?.Invoke(parameters);
        }
        #endregion
        
        #region 调试功能
        /// <summary>
        /// 显示当前状态
        /// </summary>
        [ContextMenu("显示当前状态")]
        private void ShowCurrentStatus()
        {
            LogDebug($"当前场景: {currentSceneName}");
            LogDebug($"正在切换: {isTransitioning}");
            
            var bridge = CharacterDataBridge.Instance;
            LogDebug($"桥接器数据: {bridge.GetDataSummary()}");
        }
        
        /// <summary>
        /// 测试场景切换
        /// </summary>
        [ContextMenu("测试场景切换")]
        private void TestSceneTransition()
        {
            if (currentSceneName == avatarRuntimeSceneName)
            {
                GoToScene(thirdPersonTestSceneName);
            }
            else
            {
                GoToScene(avatarRuntimeSceneName);
            }
        }
        #endregion
    }
}
