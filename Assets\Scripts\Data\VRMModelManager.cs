using UnityEngine;
using UniVRM10;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM模型管理器
    /// 负责在场景间传递VRM模型实例
    /// </summary>
    public class VRMModelManager : MonoBehaviour
    {
        public static VRMModelManager Instance { get; private set; }

        /// <summary>
        /// 确保VRMModelManager存在
        /// </summary>
        public static VRMModelManager EnsureInstance()
        {
            if (Instance == null)
            {
                // 查找现有的VRMModelManager
                Instance = FindObjectOfType<VRMModelManager>();

                if (Instance == null)
                {
                    Debug.Log("🚨 [VRMModelManager] 没有找到预部署的实例，创建新的GameObject");
                    // 创建新的VRMModelManager
                    var managerObj = new GameObject("VRMModelManager");
                    Instance = managerObj.AddComponent<VRMModelManager>();
                    DontDestroyOnLoad(managerObj);
                }
                else
                {
                    Debug.Log("✅ [VRMModelManager] 使用预部署的实例");
                }
            }
            return Instance;
        }
        
        [Header("当前VRM模型")]
        [SerializeField] private GameObject currentVRMModel;
        [SerializeField] private Vrm10Instance currentVrmInstance;
        [SerializeField] private string vrmAssetPath; // VRM资源路径
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        
        private void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                // 只有动态创建的实例才设置DontDestroyOnLoad
                if (gameObject.name == "VRMModelManager" && transform.parent == null)
                {
                    DontDestroyOnLoad(gameObject);
                    LogDebug("🎯 VRMModelManager初始化 (DontDestroyOnLoad)");
                }
                else
                {
                    LogDebug("🎯 VRMModelManager初始化 (预部署实例)");
                }
            }
            else
            {
                LogDebug($"🔄 发现已存在的VRMModelManager，销毁新实例");
                // 如果新实例有VRM模型而旧实例没有，则转移模型
                if (currentVRMModel != null && Instance.currentVRMModel == null)
                {
                    LogDebug("📦 转移VRM模型到现有实例");
                    Instance.currentVRMModel = currentVRMModel;
                    Instance.currentVrmInstance = currentVrmInstance;
                }
                Destroy(gameObject);
            }
        }
        
        /// <summary>
        /// 设置当前VRM模型
        /// </summary>
        public void SetCurrentVRMModel(GameObject vrmModel)
        {
            if (vrmModel == null)
            {
                LogDebug("❌ VRM模型为空");
                return;
            }

            // 获取VRM实例组件
            var vrmInstance = vrmModel.GetComponent<Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug("❌ 未找到Vrm10Instance组件");
                return;
            }

            // 保存VRM模型引用
            currentVRMModel = vrmModel;
            currentVrmInstance = vrmInstance;

            // 尝试获取VRM资源路径
            #if UNITY_EDITOR
            try
            {
                var assetPath = UnityEditor.AssetDatabase.GetAssetPath(vrmModel);
                if (!string.IsNullOrEmpty(assetPath))
                {
                    vrmAssetPath = assetPath;
                    LogDebug($"📁 保存VRM资源路径: {vrmAssetPath}");
                }
                else
                {
                    // 如果是场景中的实例，尝试从Vrm10Instance获取信息
                    if (vrmInstance.Vrm != null && vrmInstance.Vrm.Meta != null)
                    {
                        vrmAssetPath = vrmInstance.Vrm.Meta.Name;
                        LogDebug($"📝 从VRM Meta获取名称: {vrmAssetPath}");
                    }
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"⚠️ 获取VRM资源路径失败: {e.Message}");
            }
            #endif

            // 同时注册到VRMAssetManager
            var vrmAssetManager = VRMAssetManager.Instance;
            if (vrmAssetManager != null)
            {
                vrmAssetManager.RegisterVRMModel(vrmModel);
                LogDebug($"📝 VRM模型已注册到VRMAssetManager: {vrmModel.name}");
            }

            LogDebug($"✅ VRM模型设置完成: {vrmModel.name}");
        }
        
        /// <summary>
        /// 获取当前VRM模型的副本
        /// </summary>
        public GameObject GetVRMModelCopy()
        {
            if (currentVRMModel == null)
            {
                LogDebug("❌ 当前没有VRM模型");
                return null;
            }
            
            try
            {
                // 创建VRM模型的副本
                var copy = Instantiate(currentVRMModel);
                copy.name = currentVRMModel.name + "_Copy";
                
                LogDebug($"✅ 创建VRM模型副本: {copy.name}");
                return copy;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 创建VRM模型副本失败: {e.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 获取当前VRM模型（直接引用，不是副本）
        /// </summary>
        public GameObject GetCurrentVRMModel()
        {
            LogDebug($"🔍 GetCurrentVRMModel调用 - 当前模型: {(currentVRMModel != null ? currentVRMModel.name : "null")}");

            // 如果当前模型引用丢失，尝试从VRMAssetManager恢复
            if (currentVRMModel == null)
            {
                LogDebug("⚠️ 当前VRM模型引用丢失，尝试从VRMAssetManager恢复...");

                var vrmAssetManager = VRMAssetManager.Instance;
                if (vrmAssetManager != null)
                {
                    LogDebug("✅ VRMAssetManager找到，尝试获取VRM实例...");
                    var recoveredModel = vrmAssetManager.GetCurrentVRMInstance();
                    if (recoveredModel != null)
                    {
                        // 重新设置当前模型
                        currentVRMModel = recoveredModel;
                        currentVrmInstance = recoveredModel.GetComponent<Vrm10Instance>();
                        LogDebug($"✅ 从VRMAssetManager恢复VRM模型: {recoveredModel.name}");
                    }
                    else
                    {
                        LogDebug("❌ VRMAssetManager也没有有效的VRM模型");
                    }
                }
                else
                {
                    LogDebug("❌ VRMAssetManager未找到");
                }
            }

            return currentVRMModel;
        }
        
        /// <summary>
        /// 获取当前VRM实例
        /// </summary>
        public Vrm10Instance GetCurrentVrmInstance()
        {
            return currentVrmInstance;
        }

        /// <summary>
        /// 重新加载VRM模型（用于场景切换后恢复）
        /// </summary>
        public GameObject ReloadVRMModel()
        {
            if (string.IsNullOrEmpty(vrmAssetPath))
            {
                LogDebug("❌ 没有保存的VRM资源路径");
                return null;
            }

            #if UNITY_EDITOR
            try
            {
                // 从资源路径加载VRM预制体
                var vrmAsset = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(vrmAssetPath);
                if (vrmAsset != null)
                {
                    // 实例化VRM模型
                    var newVrmInstance = Instantiate(vrmAsset);
                    newVrmInstance.name = vrmAsset.name + "_Reloaded";

                    // 更新当前引用
                    currentVRMModel = newVrmInstance;
                    currentVrmInstance = newVrmInstance.GetComponent<Vrm10Instance>();

                    LogDebug($"✅ 重新加载VRM模型成功: {newVrmInstance.name}");
                    return newVrmInstance;
                }
                else
                {
                    LogDebug($"❌ 无法从路径加载VRM资源: {vrmAssetPath}");
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 重新加载VRM模型失败: {e.Message}");
            }
            #endif

            return null;
        }
        
        /// <summary>
        /// 检查是否有VRM模型
        /// </summary>
        public bool HasVRMModel()
        {
            bool hasModel = currentVRMModel != null && currentVrmInstance != null;

            // 详细的状态检查
            if (currentVRMModel != null)
            {
                if (currentVRMModel.Equals(null))
                {
                    LogDebug("🗑️ 检测到VRM模型已被Unity销毁，清理引用");
                    currentVRMModel = null;
                    currentVrmInstance = null;
                    hasModel = false;
                }
                else
                {
                    LogDebug($"✅ VRM模型存在: {currentVRMModel.name} (场景: {currentVRMModel.scene.name})");
                }
            }
            else
            {
                LogDebug("❌ VRM模型引用为null");
            }

            if (currentVrmInstance != null)
            {
                if (currentVrmInstance.Equals(null))
                {
                    LogDebug("🗑️ 检测到VRM实例已被Unity销毁");
                    currentVrmInstance = null;
                    hasModel = false;
                }
                else
                {
                    LogDebug($"✅ VRM实例存在: {currentVrmInstance.name}");
                }
            }
            else
            {
                LogDebug("❌ VRM实例引用为null");
            }

            LogDebug($"🔍 最终检查结果: {hasModel}");
            return hasModel;
        }
        
        /// <summary>
        /// 清理当前VRM模型
        /// </summary>
        public void ClearCurrentVRMModel()
        {
            if (currentVRMModel != null)
            {
                LogDebug($"🗑️ 清理VRM模型: {currentVRMModel.name}");
                
                // 如果不是DontDestroyOnLoad的对象，直接销毁
                if (currentVRMModel.scene.name != "DontDestroyOnLoad")
                {
                    Destroy(currentVRMModel);
                }
            }
            
            currentVRMModel = null;
            currentVrmInstance = null;
        }
        
        /// <summary>
        /// 将VRM模型移动到指定位置
        /// </summary>
        public void MoveVRMModelTo(Vector3 position, Quaternion rotation)
        {
            if (currentVRMModel != null)
            {
                currentVRMModel.transform.position = position;
                currentVRMModel.transform.rotation = rotation;
                LogDebug($"📍 移动VRM模型到: {position}");
            }
        }
        
        /// <summary>
        /// 获取VRM模型信息用于保存
        /// </summary>
        public VRMModelInfo GetVRMModelInfo()
        {
            if (currentVRMModel == null || currentVrmInstance == null)
            {
                return null;
            }
            
            return new VRMModelInfo
            {
                modelName = currentVRMModel.name,
                vrmName = currentVrmInstance.Vrm?.name ?? "Unknown",
                instanceId = currentVRMModel.GetInstanceID().ToString()
            };
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMModelManager] {message}");
            }
        }
        
        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
    
    /// <summary>
    /// VRM模型信息
    /// </summary>
    [System.Serializable]
    public class VRMModelInfo
    {
        public string modelName;
        public string vrmName;
        public string instanceId;
    }
}
