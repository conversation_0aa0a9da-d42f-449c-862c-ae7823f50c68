using UnityEngine;
using UniVRM10;
using UniGLTF;
using System.IO;
using System.Threading.Tasks;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM文件捕获器
    /// 自动检测场景中的VRM模型并尝试获取其原始文件数据
    /// </summary>
    public class VRMFileCapture : MonoBehaviour
    {
        [Header("VRM文件捕获设置")]
        [SerializeField] private bool autoDetectOnStart = true;
        [SerializeField] private bool debugMode = true;
        [SerializeField] private string vrmFilesDirectory = "Assets/Prefabs/model/"; // VRM文件存放目录
        
        [Header("状态显示")]
        [SerializeField] private bool hasDetectedVRM = false;
        [SerializeField] private string detectedVRMName = "";
        [SerializeField] private string detectedFilePath = "";
        
        void Start()
        {
            if (autoDetectOnStart)
            {
                DetectAndCaptureVRMFiles();
            }
        }
        
        /// <summary>
        /// 检测并捕获场景中的VRM文件
        /// </summary>
        [ContextMenu("检测VRM文件")]
        public async void DetectAndCaptureVRMFiles()
        {
            LogDebug("🔍 开始检测场景中的VRM模型...");
            
            var vrmInstances = FindObjectsOfType<Vrm10Instance>();
            LogDebug($"📊 找到 {vrmInstances.Length} 个VRM实例");
            
            foreach (var vrmInstance in vrmInstances)
            {
                await TryCaptureVRMFile(vrmInstance);
            }
            
            if (vrmInstances.Length == 0)
            {
                LogDebug("⚠️ 场景中没有找到VRM模型");
                LogDebug("💡 请将VRM文件拖入场景，或使用VRM导入功能");
            }
        }
        
        /// <summary>
        /// 尝试捕获VRM文件数据
        /// </summary>
        private async Task<bool> TryCaptureVRMFile(Vrm10Instance vrmInstance)
        {
            if (vrmInstance == null) return false;
            
            var vrmObject = vrmInstance.gameObject;
            LogDebug($"🎭 处理VRM模型: {vrmObject.name}");
            
            // 方法1: 尝试从Editor Asset路径获取
            string filePath = TryGetAssetPath(vrmObject);
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                LogDebug($"📁 找到资源路径: {filePath}");
                return await SaveVRMFromPath(filePath, vrmObject);
            }
            
            // 方法2: 尝试从常见目录搜索匹配的VRM文件
            filePath = await SearchForMatchingVRMFile(vrmInstance);
            if (!string.IsNullOrEmpty(filePath))
            {
                LogDebug($"🔍 搜索找到匹配文件: {filePath}");
                return await SaveVRMFromPath(filePath, vrmObject);
            }
            
            // 方法3: 提示用户手动指定文件
            LogDebug($"❌ 无法自动找到VRM文件: {vrmObject.name}");
            LogDebug("💡 请使用手动指定VRM文件的方法");
            
            return false;
        }
        
        /// <summary>
        /// 尝试获取Unity Asset路径
        /// </summary>
        private string TryGetAssetPath(GameObject vrmObject)
        {
            #if UNITY_EDITOR
            try
            {
                // 尝试从PrefabUtility获取原始资源路径
                var prefabAssetPath = UnityEditor.PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(vrmObject);
                if (!string.IsNullOrEmpty(prefabAssetPath))
                {
                    LogDebug($"🔗 Prefab路径: {prefabAssetPath}");
                    return prefabAssetPath;
                }
                
                // 尝试从AssetDatabase获取路径
                var assetPath = UnityEditor.AssetDatabase.GetAssetPath(vrmObject);
                if (!string.IsNullOrEmpty(assetPath))
                {
                    LogDebug($"🔗 Asset路径: {assetPath}");
                    return assetPath;
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"⚠️ 获取Asset路径失败: {e.Message}");
            }
            #endif
            
            return null;
        }
        
        /// <summary>
        /// 搜索匹配的VRM文件
        /// </summary>
        private async Task<string> SearchForMatchingVRMFile(Vrm10Instance vrmInstance)
        {
            var vrmObject = vrmInstance.gameObject;
            var modelName = vrmObject.name.Replace("(Clone)", "").Trim();
            
            // 搜索目录列表
            string[] searchDirectories = {
                vrmFilesDirectory,
                "Assets/Prefabs/model/",
                "Assets/VRMModels/",
                "Assets/Models/",
                "Assets/Characters/",
                "Assets/Prefabs/",
                "Assets/",
                Application.dataPath,
                Path.Combine(Application.dataPath, "VRMModels"),
                Path.Combine(Application.dataPath, "Models"),
                Path.Combine(Application.dataPath, "Characters"),
                Path.Combine(Application.dataPath, "Prefabs", "model")
            };
            
            foreach (var directory in searchDirectories)
            {
                if (!Directory.Exists(directory)) continue;
                
                LogDebug($"🔍 搜索目录: {directory}");
                
                // 搜索.vrm文件
                var vrmFiles = Directory.GetFiles(directory, "*.vrm", SearchOption.AllDirectories);
                
                foreach (var filePath in vrmFiles)
                {
                    var fileName = Path.GetFileNameWithoutExtension(filePath);
                    
                    // 名称匹配检查
                    if (IsNameMatch(fileName, modelName))
                    {
                        LogDebug($"✅ 名称匹配: {fileName} ≈ {modelName}");
                        
                        // 进一步验证：加载VRM文件检查Meta信息
                        if (await VerifyVRMFileMatch(filePath, vrmInstance))
                        {
                            return filePath;
                        }
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 检查名称是否匹配
        /// </summary>
        private bool IsNameMatch(string fileName, string modelName)
        {
            // 简单的名称匹配逻辑
            fileName = fileName.ToLower().Replace(" ", "").Replace("_", "").Replace("-", "");
            modelName = modelName.ToLower().Replace(" ", "").Replace("_", "").Replace("-", "");
            
            return fileName.Contains(modelName) || modelName.Contains(fileName) || fileName == modelName;
        }
        
        /// <summary>
        /// 验证VRM文件是否匹配
        /// </summary>
        private async Task<bool> VerifyVRMFileMatch(string filePath, Vrm10Instance currentInstance)
        {
            try
            {
                // 读取文件的Meta信息进行比较
                var bytes = await Task.Run(() => File.ReadAllBytes(filePath));
                using var gltfData = new GlbLowLevelParser(filePath, bytes).Parse();
                var vrm10Data = Vrm10Data.Parse(gltfData);
                
                if (vrm10Data?.VrmExtension?.Meta != null && currentInstance.Vrm?.Meta != null)
                {
                    var fileMeta = vrm10Data.VrmExtension.Meta;
                    var currentMeta = currentInstance.Vrm.Meta;
                    
                    // 比较Meta信息
                    bool nameMatch = fileMeta.Name == currentMeta.Name;
                    bool authorMatch = (fileMeta.Authors?.Count > 0 && currentMeta.Authors?.Count > 0) 
                                     ? fileMeta.Authors[0] == currentMeta.Authors[0] : true;
                    
                    LogDebug($"📋 Meta比较 - 名称: {nameMatch}, 作者: {authorMatch}");
                    
                    return nameMatch || authorMatch;
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"⚠️ 验证VRM文件失败: {e.Message}");
            }
            
            return false;
        }
        
        /// <summary>
        /// 从路径保存VRM文件
        /// </summary>
        private async Task<bool> SaveVRMFromPath(string filePath, GameObject vrmObject)
        {
            var success = await VRMRuntimeLoader.Instance.SaveVRMFromFile(filePath, vrmObject);
            
            if (success)
            {
                hasDetectedVRM = true;
                detectedVRMName = vrmObject.name;
                detectedFilePath = filePath;
                
                LogDebug($"✅ VRM文件捕获成功!");
                LogDebug($"📁 文件: {Path.GetFileName(filePath)}");
                LogDebug($"🎭 模型: {vrmObject.name}");
            }
            
            return success;
        }
        
        /// <summary>
        /// 手动指定VRM文件路径
        /// </summary>
        public async Task<bool> ManualSpecifyVRMFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                LogDebug($"❌ 文件不存在: {filePath}");
                return false;
            }
            
            // 查找场景中的VRM实例
            var vrmInstances = FindObjectsOfType<Vrm10Instance>();
            if (vrmInstances.Length == 0)
            {
                LogDebug("❌ 场景中没有VRM实例");
                return false;
            }
            
            // 使用第一个找到的VRM实例
            var vrmInstance = vrmInstances[0];
            return await SaveVRMFromPath(filePath, vrmInstance.gameObject);
        }
        
        /// <summary>
        /// 获取检测状态
        /// </summary>
        public bool HasDetectedVRM()
        {
            return hasDetectedVRM;
        }
        
        /// <summary>
        /// 获取检测到的VRM信息
        /// </summary>
        public (string name, string path) GetDetectedVRMInfo()
        {
            return (detectedVRMName, detectedFilePath);
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMFileCapture] {message}");
            }
        }
    }
}
