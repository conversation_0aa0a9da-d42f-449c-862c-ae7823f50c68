using UnityEngine;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VRoidFaceCustomization
{
    public class CharacterDataManager : MonoBehaviour
    {
        public static CharacterDataManager Instance { get; private set; }

        private CharacterDataStorage storage;
        private VRM10UnifiedManager characterManager;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                // 只有动态创建的实例才设置DontDestroyOnLoad
                if (gameObject.name == "CharacterDataManager" && transform.parent == null)
                {
                    DontDestroyOnLoad(gameObject);
                    Debug.Log("✅ CharacterDataManager初始化 (DontDestroyOnLoad)");
                }
                else
                {
                    Debug.Log("✅ CharacterDataManager初始化 (预部署实例)");
                }
                InitializeManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeManager()
        {
            storage = GetComponent<CharacterDataStorage>();
            if (storage == null)
            {
                storage = gameObject.AddComponent<CharacterDataStorage>();
            }
            characterManager = FindObjectOfType<VRM10UnifiedManager>();
        }

        public async Task<string> SaveCharacterAsync(CharacterData characterData, string customFileName = null)
        {
            if (characterData == null) throw new ArgumentNullException(nameof(characterData));
            if (storage == null) throw new InvalidOperationException("Storage not initialized");

            characterData.lastModifiedTime = DateTime.Now;
            return await storage.SaveCharacterAsync(characterData, customFileName);
        }

        public async Task<CharacterData> LoadCharacterAsync(string fileId, bool applyToCurrentCharacter = true)
        {
            Debug.Log($"📁 [CharacterDataManager.LoadCharacterAsync] 开始加载角色数据: {fileId}");
            Debug.Log($"🔍 [CharacterDataManager.LoadCharacterAsync] 参数: applyToCurrentCharacter={applyToCurrentCharacter}");
            
            if (storage == null) 
            {
                string errorMsg = "Storage not initialized";
                Debug.LogError($"❌ [CharacterDataManager.LoadCharacterAsync] {errorMsg}");
                throw new InvalidOperationException(errorMsg);
            }
            
            Debug.Log($"✅ [CharacterDataManager.LoadCharacterAsync] Storage已初始化");
            Debug.Log($"📁 [CharacterDataManager.LoadCharacterAsync] 调用storage.LoadCharacterAsync({fileId})...");
            
            CharacterData characterData = null;
            try
            {
                characterData = await storage.LoadCharacterAsync(fileId);
                Debug.Log($"📊 [CharacterDataManager.LoadCharacterAsync] Storage加载结果: {( characterData != null ? "成功" : "失败")}");
                
                if (characterData != null)
                {
                    Debug.Log($"📋 [CharacterDataManager.LoadCharacterAsync] 加载的数据详情:");
                    Debug.Log($"   - 角色名称: {characterData.characterName}");
                    Debug.Log($"   - 角色ID: {characterData.characterId}");
                    Debug.Log($"   - 创建时间: {characterData.createdTime}");
                    Debug.Log($"   - 面部数据: {( characterData.facialData != null ? "存在" : "不存在")}");
                    Debug.Log($"   - 服装数据: {( characterData.clothingData != null ? "存在" : "不存在")}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ [CharacterDataManager.LoadCharacterAsync] Storage加载异常: {ex.Message}");
                Debug.LogError($"🔍 [CharacterDataManager.LoadCharacterAsync] 异常堆栈: {ex.StackTrace}");
                throw;
            }
            
            if (applyToCurrentCharacter && characterData != null && characterManager != null)
            {
                Debug.Log($"🎭 [CharacterDataManager.LoadCharacterAsync] 应用数据到当前角色管理器...");
                Debug.Log($"🔍 [CharacterDataManager.LoadCharacterAsync] characterManager状态: {characterManager != null}");
                
                try
            {
                characterManager.LoadCharacterData(characterData);
                    Debug.Log($"✅ [CharacterDataManager.LoadCharacterAsync] 数据应用完成");
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"❌ [CharacterDataManager.LoadCharacterAsync] 数据应用异常: {ex.Message}");
                    Debug.LogError($"🔍 [CharacterDataManager.LoadCharacterAsync] 异常堆栈: {ex.StackTrace}");
                    // 不抛出异常，因为数据已成功加载
                }
            }
            else
            {
                if (!applyToCurrentCharacter)
                {
                    Debug.Log($"⚠️ [CharacterDataManager.LoadCharacterAsync] 跳过数据应用 - applyToCurrentCharacter=false");
                }
                if (characterData == null)
                {
                    Debug.Log($"⚠️ [CharacterDataManager.LoadCharacterAsync] 跳过数据应用 - characterData为null");
                }
                if (characterManager == null)
                {
                    Debug.Log($"⚠️ [CharacterDataManager.LoadCharacterAsync] 跳过数据应用 - characterManager为null");
                }
            }
            
            Debug.Log($"🎉 [CharacterDataManager.LoadCharacterAsync] LoadCharacterAsync完成，返回数据: {( characterData != null ? characterData.characterName : "null")}");
            return characterData;
        }

        public async Task<List<CharacterInfo>> GetSavedCharactersAsync()
        {
            return await storage.GetSavedCharactersAsync();
        }
    }
} 