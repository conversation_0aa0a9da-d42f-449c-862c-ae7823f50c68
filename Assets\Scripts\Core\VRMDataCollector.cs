using UnityEngine;
using UniVRM10;
using System.Collections.Generic;
using VRoidFaceCustomization.Core;
using VRoidFaceCustomization;

namespace VRoidFaceCustomization.Core
{
    /// <summary>
    /// VRM数据收集器 - 从VRM模型收集捏脸和换装参数
    /// </summary>
    public class VRMDataCollector
    {
        // 收集设置
        private bool debugMode = true;
        private float minBlendShapeWeight = 0.001f; // 最小BlendShape权重阈值

        // 当前状态
        private GameObject currentVRMObject;
        private Vrm10Instance currentVrmInstance;
        
        /// <summary>
        /// 从指定的VRM对象收集所有参数数据
        /// </summary>
        public CharacterParameters CollectFromVRM(GameObject vrmObject)
        {
            if (vrmObject == null)
            {
                LogDebug("❌ VRM对象为空，无法收集数据");
                return null;
            }
            
            LogDebug($"🔍 开始从VRM对象收集数据: {vrmObject.name}");
            
            // 获取VRM实例
            var vrmInstance = vrmObject.GetComponent<Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug("❌ 未找到Vrm10Instance组件");
                return null;
            }
            
            currentVRMObject = vrmObject;
            currentVrmInstance = vrmInstance;
            
            // 创建参数对象
            var parameters = new CharacterParameters
            {
                characterName = vrmObject.name,
                characterId = System.Guid.NewGuid().ToString(),
                saveTime = System.DateTime.Now,
                vrmModelName = vrmObject.name,
                modelPosition = vrmObject.transform.position,
                modelRotation = vrmObject.transform.eulerAngles,
                modelScale = vrmObject.transform.localScale
            };
            
            // 收集面部参数
            CollectFaceParameters(vrmObject, parameters.faceData);
            
            // 收集服装参数
            CollectClothingParameters(vrmObject, parameters.clothingData);
            
            LogDebug($"✅ 数据收集完成:");
            LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
            LogDebug($"   👔 服装数量: {parameters.clothingData.GetClothingCount()}件");
            
            return parameters;
        }
        
        /// <summary>
        /// 收集面部参数（BlendShape和材质）
        /// </summary>
        private void CollectFaceParameters(GameObject vrmObject, FaceParameters faceData)
        {
            LogDebug("🎭 开始收集面部参数...");
            
            // 收集BlendShape权重
            CollectBlendShapeWeights(vrmObject, faceData);
            
            // 收集面部材质属性
            CollectFaceMaterialProperties(vrmObject, faceData);
            
            LogDebug($"✅ 面部参数收集完成: {faceData.GetTotalParameterCount()}个参数");
        }
        
        /// <summary>
        /// 收集BlendShape权重
        /// </summary>
        private void CollectBlendShapeWeights(GameObject vrmObject, FaceParameters faceData)
        {
            var skinnedMeshRenderers = vrmObject.GetComponentsInChildren<SkinnedMeshRenderer>();
            int totalBlendShapes = 0;
            int validBlendShapes = 0;
            
            foreach (var renderer in skinnedMeshRenderers)
            {
                if (renderer.sharedMesh == null) continue;
                
                var mesh = renderer.sharedMesh;
                for (int i = 0; i < mesh.blendShapeCount; i++)
                {
                    totalBlendShapes++;
                    string shapeName = mesh.GetBlendShapeName(i);
                    float weight = renderer.GetBlendShapeWeight(i);
                    
                    // 只保存有效权重
                    if (weight > minBlendShapeWeight)
                    {
                        string fullShapeName = $"{renderer.name}/{shapeName}";
                        faceData.SetBlendShapeWeight(fullShapeName, weight);
                        validBlendShapes++;
                    }
                }
            }
            
            LogDebug($"   🎭 BlendShape: {validBlendShapes}/{totalBlendShapes} 个有效权重");
        }
        
        /// <summary>
        /// 收集面部材质属性
        /// </summary>
        private void CollectFaceMaterialProperties(GameObject vrmObject, FaceParameters faceData)
        {
            var renderers = vrmObject.GetComponentsInChildren<Renderer>();
            int materialCount = 0;
            
            foreach (var renderer in renderers)
            {
                // 只收集面部相关的材质
                if (!IsFaceRenderer(renderer)) continue;
                
                for (int i = 0; i < renderer.materials.Length; i++)
                {
                    var material = renderer.materials[i];
                    if (material == null) continue;
                    
                    string materialPath = $"{renderer.name}/Material_{i}";
                    
                    // 收集常见的材质属性
                    CollectMaterialProperty(material, materialPath, "_Color", faceData);
                    CollectMaterialProperty(material, materialPath, "_EmissionColor", faceData);
                    CollectMaterialProperty(material, materialPath, "_Metallic", faceData);
                    CollectMaterialProperty(material, materialPath, "_Smoothness", faceData);
                    
                    materialCount++;
                }
            }
            
            LogDebug($"   🎨 材质属性: {materialCount} 个材质");
        }
        
        /// <summary>
        /// 收集单个材质属性
        /// </summary>
        private void CollectMaterialProperty(Material material, string materialPath, string propertyName, FaceParameters faceData)
        {
            if (!material.HasProperty(propertyName)) return;
            
            string fullPropertyPath = $"{materialPath}/{propertyName}";
            var property = new MaterialProperty { propertyName = propertyName };
            
            // 根据属性类型收集数据
            if (propertyName.Contains("Color"))
            {
                property.propertyType = MaterialPropertyType.Color;
                property.colorValue = material.GetColor(propertyName);
            }
            else
            {
                property.propertyType = MaterialPropertyType.Float;
                property.floatValue = material.GetFloat(propertyName);
            }
            
            faceData.SetMaterialProperty(fullPropertyPath, property);
        }
        
        /// <summary>
        /// 收集服装参数
        /// </summary>
        private void CollectClothingParameters(GameObject vrmObject, ClothingParameters clothingData)
        {
            LogDebug("👔 开始收集服装参数...");

            // 查找VRM10ClothBinder组件 - 先在根对象查找，然后在子对象中查找
            var clothBinder = vrmObject.GetComponent<VRM10ClothBinder>();
            if (clothBinder == null)
            {
                // 在子对象中查找
                clothBinder = vrmObject.GetComponentInChildren<VRM10ClothBinder>();
            }

            if (clothBinder == null)
            {
                LogDebug("⚠️ 未找到VRM10ClothBinder组件（已搜索根对象和所有子对象），跳过服装收集");
                return;
            }

            LogDebug($"✅ 找到VRM10ClothBinder组件在: {clothBinder.gameObject.name}");
            
            // 获取当前穿戴的服装
            var wornClothes = GetWornClothes(clothBinder);
            
            foreach (var cloth in wornClothes)
            {
                if (cloth == null || cloth.gameObject == null) continue;
                
                string slotType = cloth.type.ToString();
                string clothingName = cloth.gameObject.name;
                
                // 创建服装配置
                var config = new ClothingConfig
                {
                    clothingName = clothingName,
                    prefabPath = GetClothingPrefabPath(cloth),
                    position = cloth.gameObject.transform.localPosition,
                    rotation = cloth.gameObject.transform.localEulerAngles,
                    scale = cloth.gameObject.transform.localScale
                };
                
                // 收集服装材质属性
                CollectClothingMaterialProperties(cloth.gameObject, config);
                
                clothingData.SetClothing(slotType, clothingName, config);
            }
            
            LogDebug($"✅ 服装参数收集完成: {clothingData.GetClothingCount()}件服装");
        }
        
        /// <summary>
        /// 获取当前穿戴的服装（需要根据实际的VRM10ClothBinder实现调整）
        /// </summary>
        private List<VRM10ClothItem> GetWornClothes(VRM10ClothBinder clothBinder)
        {
            // 这里需要根据你的VRM10ClothBinder实际实现来获取穿戴的服装
            // 暂时返回空列表，需要你根据实际情况实现
            var wornClothes = new List<VRM10ClothItem>();
            
            // 示例代码（需要根据实际API调整）:
            // wornClothes = clothBinder.GetWornClothes();
            
            LogDebug($"   👔 发现 {wornClothes.Count} 件穿戴的服装");
            return wornClothes;
        }
        
        /// <summary>
        /// 获取服装预制体路径
        /// </summary>
        private string GetClothingPrefabPath(VRM10ClothItem cloth)
        {
            // 这里需要根据你的服装系统实现来获取预制体路径
            // 暂时返回空字符串，需要你根据实际情况实现
            return "";
        }
        
        /// <summary>
        /// 收集服装材质属性
        /// </summary>
        private void CollectClothingMaterialProperties(GameObject clothObject, ClothingConfig config)
        {
            var renderers = clothObject.GetComponentsInChildren<Renderer>();
            
            foreach (var renderer in renderers)
            {
                for (int i = 0; i < renderer.materials.Length; i++)
                {
                    var material = renderer.materials[i];
                    if (material == null) continue;
                    
                    string materialPath = $"{renderer.name}/Material_{i}";
                    
                    // 收集常见的材质属性
                    if (material.HasProperty("_Color"))
                    {
                        var property = new MaterialProperty
                        {
                            propertyName = "_Color",
                            propertyType = MaterialPropertyType.Color,
                            colorValue = material.GetColor("_Color")
                        };
                        config.materialProperties[$"{materialPath}/_Color"] = property;
                    }
                }
            }
        }
        
        /// <summary>
        /// 判断是否为面部渲染器
        /// </summary>
        private bool IsFaceRenderer(Renderer renderer)
        {
            string name = renderer.name.ToLower();
            return name.Contains("face") || name.Contains("head") || name.Contains("eye") || name.Contains("mouth");
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMDataCollector] {message}");
            }
        }
    }
    
    /// <summary>
    /// 临时的VRM10ClothItem类定义（需要根据实际实现调整）
    /// </summary>
    public class VRM10ClothItem
    {
        public GameObject gameObject;
        public VRM10ClothType type;
    }
    
    /// <summary>
    /// 临时的VRM10ClothType枚举（需要根据实际实现调整）
    /// </summary>
    public enum VRM10ClothType
    {
        Top,
        Bottom,
        Dress,
        Shoes,
        Hat,
        Gloves,
        Accessory
    }
    
    /// <summary>
    /// 临时的VRM10ClothBinder类定义（需要根据实际实现调整）
    /// </summary>
    public class VRM10ClothBinder : MonoBehaviour
    {
        // 这个类需要根据你的实际VRM10ClothBinder实现来定义
    }
}
