using System;
using System.Collections.Generic;
using UnityEngine;

namespace VRoidFaceCustomization.Core
{
    /// <summary>
    /// 角色参数数据结构 - 简化版本
    /// 只包含必要的捏脸和换装数据
    /// </summary>
    [System.Serializable]
    public class CharacterParameters
    {
        [Header("基础信息")]
        public string characterName = "DefaultCharacter";
        public string characterId = "";
        public DateTime saveTime = DateTime.Now;
        
        [Header("面部参数")]
        public FaceParameters faceData = new FaceParameters();
        
        [Header("服装参数")]
        public ClothingParameters clothingData = new ClothingParameters();
        
        [Header("模型信息")]
        public string vrmModelName = "";
        public Vector3 modelPosition = Vector3.zero;
        public Vector3 modelRotation = Vector3.zero;
        public Vector3 modelScale = Vector3.one;
        
        /// <summary>
        /// 创建参数数据的副本
        /// </summary>
        public CharacterParameters Clone()
        {
            var clone = new CharacterParameters
            {
                characterName = this.characterName,
                characterId = this.characterId,
                saveTime = this.saveTime,
                vrmModelName = this.vrmModelName,
                modelPosition = this.modelPosition,
                modelRotation = this.modelRotation,
                modelScale = this.modelScale,
                faceData = this.faceData.Clone(),
                clothingData = this.clothingData.Clone()
            };
            return clone;
        }
        
        /// <summary>
        /// 验证数据完整性
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(characterName) && 
                   faceData != null && 
                   clothingData != null;
        }
    }
    
    /// <summary>
    /// 面部参数数据
    /// </summary>
    [System.Serializable]
    public class FaceParameters
    {
        [Header("BlendShape权重")]
        public Dictionary<string, float> blendShapeWeights = new Dictionary<string, float>();
        
        [Header("面部材质参数")]
        public Dictionary<string, MaterialProperty> faceMaterialProperties = new Dictionary<string, MaterialProperty>();
        
        /// <summary>
        /// 添加BlendShape权重
        /// </summary>
        public void SetBlendShapeWeight(string shapeName, float weight)
        {
            if (weight > 0.001f) // 只保存有效权重
            {
                blendShapeWeights[shapeName] = weight;
            }
            else if (blendShapeWeights.ContainsKey(shapeName))
            {
                blendShapeWeights.Remove(shapeName);
            }
        }
        
        /// <summary>
        /// 获取BlendShape权重
        /// </summary>
        public float GetBlendShapeWeight(string shapeName)
        {
            return blendShapeWeights.TryGetValue(shapeName, out float weight) ? weight : 0f;
        }
        
        /// <summary>
        /// 添加材质属性
        /// </summary>
        public void SetMaterialProperty(string propertyPath, MaterialProperty property)
        {
            faceMaterialProperties[propertyPath] = property;
        }
        
        /// <summary>
        /// 创建副本
        /// </summary>
        public FaceParameters Clone()
        {
            var clone = new FaceParameters();
            clone.blendShapeWeights = new Dictionary<string, float>(this.blendShapeWeights);
            clone.faceMaterialProperties = new Dictionary<string, MaterialProperty>();
            
            foreach (var kvp in this.faceMaterialProperties)
            {
                clone.faceMaterialProperties[kvp.Key] = kvp.Value.Clone();
            }
            
            return clone;
        }
        
        /// <summary>
        /// 获取参数总数
        /// </summary>
        public int GetTotalParameterCount()
        {
            return blendShapeWeights.Count + faceMaterialProperties.Count;
        }
    }
    
    /// <summary>
    /// 服装参数数据
    /// </summary>
    [System.Serializable]
    public class ClothingParameters
    {
        [Header("当前穿戴")]
        public Dictionary<string, string> currentOutfit = new Dictionary<string, string>();
        
        [Header("服装配置")]
        public Dictionary<string, ClothingConfig> clothingConfigs = new Dictionary<string, ClothingConfig>();
        
        /// <summary>
        /// 设置穿戴的服装
        /// </summary>
        public void SetClothing(string slotType, string clothingName, ClothingConfig config = null)
        {
            currentOutfit[slotType] = clothingName;
            
            if (config != null)
            {
                clothingConfigs[clothingName] = config;
            }
        }
        
        /// <summary>
        /// 移除穿戴的服装
        /// </summary>
        public void RemoveClothing(string slotType)
        {
            if (currentOutfit.ContainsKey(slotType))
            {
                currentOutfit.Remove(slotType);
            }
        }
        
        /// <summary>
        /// 获取穿戴的服装
        /// </summary>
        public string GetClothing(string slotType)
        {
            return currentOutfit.TryGetValue(slotType, out string clothingName) ? clothingName : null;
        }
        
        /// <summary>
        /// 创建副本
        /// </summary>
        public ClothingParameters Clone()
        {
            var clone = new ClothingParameters();
            clone.currentOutfit = new Dictionary<string, string>(this.currentOutfit);
            clone.clothingConfigs = new Dictionary<string, ClothingConfig>();
            
            foreach (var kvp in this.clothingConfigs)
            {
                clone.clothingConfigs[kvp.Key] = kvp.Value.Clone();
            }
            
            return clone;
        }
        
        /// <summary>
        /// 获取穿戴的服装数量
        /// </summary>
        public int GetClothingCount()
        {
            return currentOutfit.Count;
        }
    }
    
    /// <summary>
    /// 材质属性
    /// </summary>
    [System.Serializable]
    public class MaterialProperty
    {
        public string propertyName;
        public MaterialPropertyType propertyType;
        public Color colorValue;
        public float floatValue;
        public Vector4 vectorValue;
        
        public MaterialProperty Clone()
        {
            return new MaterialProperty
            {
                propertyName = this.propertyName,
                propertyType = this.propertyType,
                colorValue = this.colorValue,
                floatValue = this.floatValue,
                vectorValue = this.vectorValue
            };
        }
    }
    
    /// <summary>
    /// 服装配置
    /// </summary>
    [System.Serializable]
    public class ClothingConfig
    {
        public string clothingName;
        public string prefabPath;
        public Vector3 position;
        public Vector3 rotation;
        public Vector3 scale = Vector3.one;
        public Dictionary<string, MaterialProperty> materialProperties = new Dictionary<string, MaterialProperty>();
        
        public ClothingConfig Clone()
        {
            var clone = new ClothingConfig
            {
                clothingName = this.clothingName,
                prefabPath = this.prefabPath,
                position = this.position,
                rotation = this.rotation,
                scale = this.scale,
                materialProperties = new Dictionary<string, MaterialProperty>()
            };
            
            foreach (var kvp in this.materialProperties)
            {
                clone.materialProperties[kvp.Key] = kvp.Value.Clone();
            }
            
            return clone;
        }
    }
    
    /// <summary>
    /// 材质属性类型
    /// </summary>
    public enum MaterialPropertyType
    {
        Color,
        Float,
        Vector
    }
}
