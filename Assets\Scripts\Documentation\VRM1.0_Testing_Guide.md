# VRM1.0适配系统 - 详细测试指南

## 🎯 测试目标
验证VRM1.0模型在场景切换时能够正确保存和恢复所有定制化状态。

## 📋 测试前准备

### 1. 检查Unity编译状态
- 打开Unity项目
- 查看Console面板，确保**没有编译错误**
- 如有错误，请先解决编译问题

### 2. 运行系统诊断（**必须**）
在开始测试前，请先运行诊断工具：

1. **添加诊断工具**：
   - 在AvatarRuntime场景中创建空GameObject
   - 添加 `VRMDiagnosticTool` 组件
   - 或在Inspector中右键点击组件 → "运行VRM诊断"

2. **查看诊断结果**：
   - Console会显示详细的系统状态
   - 检查VRM文件是否找到
   - 确认所有管理器组件存在

### 3. 确认文件存在
确认以下文件已存在：
- ✅ `Assets/Scripts/Data/VRMStateManager.cs`
- ✅ `Assets/Scripts/Testing/VRMAdaptationTester.cs`
- ✅ `Assets/Scripts/Testing/VRMDiagnosticTool.cs` (新增)
- ✅ `Assets/Scripts/UI/SceneTransitionManager.cs` (已更新)
- ✅ `Assets/Scripts/UI/ThirdPersonSceneLoader.cs` (已更新)

## 🚀 测试方法一：使用VRMAdaptationTester（推荐）

### 步骤1: 设置测试环境
1. 打开 **AvatarRuntime** 场景
2. 在场景中找到任意一个GameObject（或创建新的空对象）
3. 添加 `VRMAdaptationTester` 组件：
   - 选中GameObject
   - 在Inspector中点击 "Add Component"
   - 搜索 "VRMAdaptationTester"
   - 添加组件

### 步骤2: 确保VRM模型存在
1. 确认场景中有VRM模型加载
2. **新增**: 按 **F5** 键检查VRM文件路径：
   - 系统会自动扫描所有VRM文件
   - 显示找到的文件路径
   - 如果没找到，会给出解决建议

### 步骤3: 执行快捷键测试
运行游戏后按以下快捷键：

#### 🔍 基础功能测试
```
F5 → 检查VRM文件路径
    - 扫描项目中所有VRM文件
    - 验证文件路径是否正确
    - 查看文件大小和详情

F4 → 修改VRM模型
    - 观察BlendShape变化
    - 观察材质颜色变化
    - 查看Console日志
    
F1 → 保存VRM状态
    - 检查Console是否显示保存成功
    - 查看保存的状态计数
    
F2 → 加载VRM状态  
    - 验证模型恢复到保存时的状态
    - 检查BlendShape和材质是否正确恢复
```

#### 🔄 场景切换测试
```
F5 → 检查VRM文件路径（确保文件存在）
F4 → 修改VRM模型（制造差异）
F1 → 保存状态
F3 → 测试场景切换
    - 自动切换到ThirdPersonTestScene
    - 检查VRM模型是否正确重建
    - 验证所有定制化修改是否保持
```

### 步骤4: 观察测试结果

#### ✅ 成功的标志
- Console显示 "✅ VRM渲染状态保存成功"
- Console显示 "✅ VRM渲染状态应用成功"  
- Console显示 "✅ 找到VRM文件: [路径]"
- 场景切换后模型外观与修改后一致
- 左上角调试UI显示正确的状态计数

#### ❌ 失败的标志及解决方案
- **Console显示错误信息**
  - 查看具体错误内容
  - 参考下面的故障排除指南
- **场景切换后模型恢复到初始状态**
  - 检查VRM文件是否找到 (按F5)
  - 确认VRM数据大小不为0KB
- **BlendShape或材质颜色丢失**
  - 检查VRM模型是否有BlendShape
  - 确认材质属性是否正确保存

## 🔧 故障排除指南

### 问题1: "The referenced script (Unknown) on this Behaviour is missing!"
**原因**: 脚本引用丢失
**解决方案**:
1. 打开有问题的GameObject
2. 查看Inspector中显示"Missing Script"的组件
3. 删除丢失的组件引用
4. 重新添加正确的组件

### 问题2: "glb header not found" 或 VRM数据大小为0KB
**原因**: VRM文件路径错误或文件不存在
**解决方案**:
1. 按 **F5** 键检查VRM文件路径
2. 确保VRM文件存在于以下位置之一：
   ```
   Assets/Prefabs/model/xa初始模型.vrm
   Assets/VRMModels/xa初始模型.vrm
   Assets/Models/xa初始模型.vrm
   Assets/Prefabs/clorh model/xa初始模型.vrm
   ```
3. 如果文件在其他位置，将路径添加到VRMAdaptationTester的vrmFilePaths数组中

### 问题3: "VRM实例: ✗" 或 "未找到VRM实例"
**原因**: 场景中没有Vrm10Instance组件
**解决方案**:
1. 确保场景中有正确加载的VRM模型
2. 检查VRM模型GameObject是否有Vrm10Instance组件
3. 如果使用预设模型，确保预设包含VRM组件

### 问题4: "角色对象缺少Vrm10Instance组件"
**原因**: ThirdPersonTestScene中的LoadedCharacter不是VRM模型
**解决方案**:
1. 检查ThirdPersonTestScene中的LoadedCharacter对象
2. 确保该对象是正确的VRM模型
3. 或者修改代码使用正确的VRM预设

### 问题5: 材质属性保存失败或应用失败
**原因**: 材质类型不兼容或属性名称不匹配
**解决方案**:
1. 检查Console中的详细错误信息
2. 确认VRM模型使用的是VRM兼容的材质
3. 验证BlendShape名称是否正确

## 🔧 使用诊断工具

### 自动诊断
1. 添加 `VRMDiagnosticTool` 到场景中
2. 组件会自动运行诊断 (autoRunOnStart = true)
3. 查看Console输出的详细报告

### 手动诊断
- 在Inspector中右键点击VRMDiagnosticTool组件
- 选择 "运行VRM诊断"
- 查看诊断结果和建议

### 自动修复
- 在Inspector中右键点击VRMDiagnosticTool组件
- 选择 "自动修复常见问题"
- 系统会自动添加缺失的管理器组件

## 🎮 测试方法二：原有UI流程测试

### 步骤1: 传统测试流程
1. 打开 **AvatarRuntime** 场景
2. 运行VRM诊断确保系统正常
3. 使用现有的UI界面进行角色定制：
   - 调整面部参数
   - 更换服装
   - 修改材质颜色
4. 点击 **保存按钮**
5. 系统应自动切换到 **ThirdPersonTestScene**
6. 验证角色状态是否完整恢复

### 步骤2: 验证检查点
- [ ] 面部BlendShape保持
- [ ] 服装状态保持  
- [ ] 材质颜色保持
- [ ] 模型位置和旋转保持
- [ ] 整体外观一致

## 🔍 调试和诊断

### 启用详细日志
在VRMStateManager和VRMAdaptationTester中：
```csharp
[SerializeField] private bool debugMode = true;
```

### 关键日志标志
查看Console中的关键信息：

#### 保存阶段
```
🔍 开始捕获VRM渲染状态: [模型名]
✅ 找到VRM文件: [文件路径]
📦 数据大小: [大小] bytes (应该 > 0)
🎭 捕获了 X 个BlendShape状态  
🎨 捕获了 X 个材质属性
🦴 捕获了 X 个骨骼变换
✅ VRM渲染状态已捕获
```

#### 加载阶段
```
🎯 开始应用VRM渲染状态到: [模型名]
🎭 成功应用了 X/Y 个BlendShape状态
🎨 成功应用了 X/Y 个材质属性  
🦴 成功应用了 X/Y 个骨骼变换
✅ VRM渲染状态应用完成
```

### 诊断工具输出
```
📁 VRM文件: X 个
🎭 VRM实例: X 个
📜 缺失脚本: X 个
🔧 VRMStateManager: ✅/❌
🔧 VRMRuntimeLoader: ✅/❌
```

## 📊 性能测试

### 内存使用测试
1. 打开Unity Profiler
2. 运行测试流程
3. 观察内存使用情况：
   - 保存时内存变化
   - 场景切换时内存清理
   - 加载后内存稳定性

### 执行时间测试
观察Console中的时间戳：
- 保存操作耗时
- 场景切换耗时  
- 状态应用耗时

## 🛠️ 自定义测试

### 测试不同VRM模型
1. 将不同的VRM1.0文件放入项目
2. 按F5检查文件是否被发现
3. 修改VRMAdaptationTester中的文件路径（如需要）
4. 重复测试流程验证兼容性

### 创建测试场景
1. 复制AvatarRuntime场景
2. 添加VRMDiagnosticTool和VRMAdaptationTester
3. 导入不同的VRM模型进行测试

## 📋 测试检查清单

### 基础功能 ✓
- [ ] VRM诊断工具运行正常
- [ ] VRM文件被正确发现 (F5)
- [ ] VRM模型正确加载
- [ ] 状态管理器初始化
- [ ] 保存功能正常
- [ ] 加载功能正常

### 状态保持 ✓  
- [ ] BlendShape权重保持
- [ ] 材质颜色保持
- [ ] 骨骼变换保持
- [ ] 根变换保持

### 场景切换 ✓
- [ ] VRM文件数据保存成功 (大小 > 0KB)
- [ ] 自动场景切换
- [ ] VRM模型重建
- [ ] 状态自动恢复
- [ ] 视觉效果一致

### 错误处理 ✓
- [ ] 异常情况处理
- [ ] 详细错误日志
- [ ] 优雅的失败恢复
- [ ] 诊断工具提供有用建议

## 🎯 预期测试结果

**成功标准**: 
- VRM诊断工具显示"系统状态良好"
- 所有快捷键功能正常
- 场景切换后VRM状态100%恢复
- Console无错误信息
- 视觉效果完全一致

**如果测试失败**:
1. 运行VRM诊断工具 (F5 或 VRMDiagnosticTool)
2. 检查Console错误信息
3. 参考上面的故障排除指南
4. 确认所有组件正确配置
5. 验证VRM文件路径和权限

---

**新增功能**:
- 🔧 **F5键**: 检查VRM文件路径
- 🔧 **VRMDiagnosticTool**: 自动诊断系统问题
- 🔧 **智能文件扫描**: 自动查找项目中的VRM文件
- 🔧 **自动修复**: 自动添加缺失的管理器组件

**注意**: 首次测试建议先按F5检查文件路径，再使用VRMAdaptationTester的快捷键方式测试。 