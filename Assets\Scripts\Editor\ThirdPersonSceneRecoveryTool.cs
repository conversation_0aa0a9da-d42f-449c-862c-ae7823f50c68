using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
using VRoidFaceCustomization.UI;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// 第三人称场景恢复工具
    /// 重新创建被删除的ThirdPersonTestScene
    /// </summary>
    public class ThirdPersonSceneRecoveryTool : EditorWindow
    {
        [MenuItem("Tools/VRoid Face Customization/Recover Third Person Scene")]
        public static void ShowWindow()
        {
            GetWindow<ThirdPersonSceneRecoveryTool>("第三人称场景恢复工具");
        }
        
        private string sceneName = "ThirdPersonTestScene";
        private string scenePath = "Assets/Scenes/ThirdPersonTestScene.unity";
        private bool includeStarterAssets = true;
        private bool includeManagers = true;
        private bool setupCinemachine = true;
        
        void OnGUI()
        {
            GUILayout.Label("第三人称场景恢复工具", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            EditorGUILayout.HelpBox("此工具将重新创建完整的第三人称控制场景，包含所有必要的组件和设置", MessageType.Info);
            GUILayout.Space(10);
            
            // 场景设置
            EditorGUILayout.LabelField("场景设置", EditorStyles.boldLabel);
            sceneName = EditorGUILayout.TextField("场景名称", sceneName);
            scenePath = EditorGUILayout.TextField("保存路径", scenePath);
            
            GUILayout.Space(10);
            
            // 创建选项
            EditorGUILayout.LabelField("创建选项", EditorStyles.boldLabel);
            includeStarterAssets = EditorGUILayout.Toggle("包含StarterAssets组件", includeStarterAssets);
            includeManagers = EditorGUILayout.Toggle("预部署管理器", includeManagers);
            setupCinemachine = EditorGUILayout.Toggle("设置Cinemachine相机", setupCinemachine);
            
            GUILayout.Space(20);
            
            // 创建按钮
            if (GUILayout.Button("🎯 创建第三人称场景", GUILayout.Height(40)))
            {
                CreateThirdPersonScene();
            }
            
            GUILayout.Space(10);
            
            // 快速恢复按钮
            if (GUILayout.Button("⚡ 快速恢复（使用默认设置）", GUILayout.Height(30)))
            {
                QuickRecover();
            }
            
            GUILayout.Space(10);
            
            EditorGUILayout.HelpBox("创建后记得保存场景！场景将包含：\n• ThirdPersonSceneLoader\n• 基础环境（地面、光照）\n• 角色生成点\n• StarterAssets组件\n• VRM管理器", MessageType.Info);
        }
        
        /// <summary>
        /// 快速恢复场景
        /// </summary>
        void QuickRecover()
        {
            sceneName = "ThirdPersonTestScene";
            scenePath = "Assets/Scenes/ThirdPersonTestScene.unity";
            includeStarterAssets = true;
            includeManagers = true;
            setupCinemachine = true;
            
            CreateThirdPersonScene();
        }
        
        /// <summary>
        /// 创建第三人称场景
        /// </summary>
        void CreateThirdPersonScene()
        {
            Debug.Log("🎯 开始创建第三人称场景...");
            
            // 创建新场景
            var newScene = EditorSceneManager.NewScene(NewSceneSetup.DefaultGameObjects, NewSceneMode.Single);
            newScene.name = sceneName;
            
            Debug.Log($"✅ 新场景创建成功: {sceneName}");
            
            // 创建基础环境
            CreateBasicEnvironment();
            
            // 创建场景加载器
            CreateSceneLoader();
            
            // 创建生成点
            CreateSpawnPoint();
            
            // 创建预设角色对象
            CreateLoadedCharacter();
            
            // 预部署管理器
            if (includeManagers)
            {
                CreateManagers();
            }
            
            // 设置Cinemachine
            if (setupCinemachine)
            {
                SetupCinemachineCamera();
            }
            
            // 保存场景
            bool saved = EditorSceneManager.SaveScene(newScene, scenePath);
            if (saved)
            {
                Debug.Log($"✅ 场景保存成功: {scenePath}");
                EditorUtility.DisplayDialog("成功", $"第三人称场景已成功创建并保存到:\n{scenePath}", "确定");
            }
            else
            {
                Debug.LogError("❌ 场景保存失败");
                EditorUtility.DisplayDialog("错误", "场景保存失败，请检查路径是否正确", "确定");
            }
        }
        
        /// <summary>
        /// 创建基础环境
        /// </summary>
        void CreateBasicEnvironment()
        {
            Debug.Log("🏗️ 创建基础环境...");
            
            // 创建地面
            var ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = new Vector3(10, 1, 10);
            
            // 设置地面材质
            var groundRenderer = ground.GetComponent<Renderer>();
            if (groundRenderer != null)
            {
                // 尝试使用现有材质
                var stageMaterial = AssetDatabase.LoadAssetAtPath<Material>("Assets/material/M_StageWhite.mat");
                if (stageMaterial != null)
                {
                    groundRenderer.material = stageMaterial;
                    Debug.Log("✅ 应用舞台材质");
                }
            }
            
            // 调整光照
            var mainLight = GameObject.Find("Directional Light");
            if (mainLight != null)
            {
                mainLight.transform.rotation = Quaternion.Euler(50, -30, 0);
                var light = mainLight.GetComponent<Light>();
                if (light != null)
                {
                    light.intensity = 1.0f;
                    light.shadows = LightShadows.Soft;
                }
                Debug.Log("✅ 调整主光源");
            }
            
            Debug.Log("✅ 基础环境创建完成");
        }
        
        /// <summary>
        /// 创建场景加载器
        /// </summary>
        void CreateSceneLoader()
        {
            Debug.Log("🔄 创建ThirdPersonSceneLoader...");
            
            var loaderObj = new GameObject("ThirdPersonSceneLoader");
            var loader = loaderObj.AddComponent<ThirdPersonSceneLoader>();
            
            // 设置默认参数
            // 由于ThirdPersonSceneLoader的字段可能是private，我们通过反射设置
            var loaderType = typeof(ThirdPersonSceneLoader);
            
            // 设置autoLoadOnStart
            var autoLoadField = loaderType.GetField("autoLoadOnStart", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (autoLoadField != null)
            {
                autoLoadField.SetValue(loader, true);
                Debug.Log("✅ 设置autoLoadOnStart = true");
            }
            
            // 设置usePresetCharacter
            var presetField = loaderType.GetField("usePresetCharacter", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (presetField != null)
            {
                presetField.SetValue(loader, true);
                Debug.Log("✅ 设置usePresetCharacter = true");
            }
            
            // 尝试设置生成点引用
            var spawnPoint = GameObject.Find("SpawnPoint");
            if (spawnPoint != null)
            {
                var spawnField = loaderType.GetField("spawnPoint", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (spawnField != null)
                {
                    spawnField.SetValue(loader, spawnPoint.transform);
                    Debug.Log("✅ 设置SpawnPoint引用");
                }
            }
            
            Debug.Log("✅ ThirdPersonSceneLoader创建完成");
        }
        
        /// <summary>
        /// 创建生成点
        /// </summary>
        void CreateSpawnPoint()
        {
            Debug.Log("📍 创建SpawnPoint...");
            
            var spawnPoint = new GameObject("SpawnPoint");
            spawnPoint.transform.position = new Vector3(0, 1, 0);
            spawnPoint.transform.rotation = Quaternion.identity;
            
            // 添加一个小的可视化标记
            var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = "SpawnMarker";
            cube.transform.SetParent(spawnPoint.transform);
            cube.transform.localPosition = Vector3.zero;
            cube.transform.localScale = new Vector3(0.5f, 0.1f, 0.5f);
            
            // 设置材质颜色
            var renderer = cube.GetComponent<Renderer>();
            if (renderer != null)
            {
                var material = new Material(Shader.Find("Standard"));
                material.color = Color.green;
                renderer.material = material;
            }
            
            // 移除碰撞器（只是标记用）
            var collider = cube.GetComponent<Collider>();
            if (collider != null)
            {
                DestroyImmediate(collider);
            }
            
            Debug.Log("✅ SpawnPoint创建完成");
        }
        
        /// <summary>
        /// 创建LoadedCharacter预设对象
        /// </summary>
        void CreateLoadedCharacter()
        {
            Debug.Log("🎭 创建LoadedCharacter预设对象...");
            
            var loadedCharacter = new GameObject("LoadedCharacter");
            loadedCharacter.transform.position = new Vector3(0, 1, 0);
            
            // 添加基础组件
            var characterController = loadedCharacter.AddComponent<CharacterController>();
            characterController.center = new Vector3(0, 1, 0);
            characterController.height = 2.0f;
            characterController.radius = 0.3f;
            
            var animator = loadedCharacter.AddComponent<Animator>();
            
            // 尝试添加第三人称控制器组件
            var thirdPersonControllerType = System.Type.GetType("StarterAssets.ThirdPersonController");
            if (thirdPersonControllerType != null)
            {
                loadedCharacter.AddComponent(thirdPersonControllerType);
                Debug.Log("✅ 添加ThirdPersonController");
            }
            
            // 尝试添加输入组件
            var inputType = System.Type.GetType("StarterAssets.StarterAssetsInputs");
            if (inputType != null)
            {
                loadedCharacter.AddComponent(inputType);
                Debug.Log("✅ 添加StarterAssetsInputs");
            }
            
            // 尝试添加相机目标组件
            var cameraTargetType = System.Type.GetType("Cinemachine.CinemachineCameraTarget");
            if (cameraTargetType != null)
            {
                loadedCharacter.AddComponent(cameraTargetType);
                Debug.Log("✅ 添加CinemachineCameraTarget");
            }
            
            // 创建一个临时的可视化对象
            var capsule = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            capsule.name = "TemporaryBody";
            capsule.transform.SetParent(loadedCharacter.transform);
            capsule.transform.localPosition = new Vector3(0, 1, 0);
            capsule.transform.localScale = new Vector3(0.6f, 1, 0.6f);
            
            // 设置材质
            var renderer = capsule.GetComponent<Renderer>();
            if (renderer != null)
            {
                var material = new Material(Shader.Find("Standard"));
                material.color = new Color(0.5f, 0.8f, 1.0f, 0.7f); // 浅蓝色半透明
                renderer.material = material;
            }
            
            // 移除胶囊体的碰撞器（使用CharacterController代替）
            var capsuleCollider = capsule.GetComponent<Collider>();
            if (capsuleCollider != null)
            {
                DestroyImmediate(capsuleCollider);
            }
            
            Debug.Log("✅ LoadedCharacter预设对象创建完成");
        }
        
        /// <summary>
        /// 创建管理器
        /// </summary>
        void CreateManagers()
        {
            Debug.Log("🚀 预部署管理器...");
            
            // 创建各种管理器
            CreateManager<VRMAssetManager>("VRMAssetManager");
            CreateManager<VRMRuntimeLoader>("VRMRuntimeLoader");
            CreateManager<CharacterDataManager>("CharacterDataManager");
            
            // 尝试创建其他管理器（如果存在的话）
            CreateManagerByTypeName("VRMModelManager", "VRoidFaceCustomization.VRMModelManager");
            CreateManagerByTypeName("VRMStateManager", "VRoidFaceCustomization.Data.VRMStateManager");
            
            Debug.Log("✅ 管理器预部署完成");
        }
        
        /// <summary>
        /// 创建管理器
        /// </summary>
        void CreateManager<T>(string name) where T : MonoBehaviour
        {
            var go = new GameObject(name);
            go.AddComponent<T>();
            Debug.Log($"✅ 创建 {name}");
        }
        
        /// <summary>
        /// 通过类型名创建管理器
        /// </summary>
        void CreateManagerByTypeName(string objectName, string typeName)
        {
            var type = System.Type.GetType(typeName);
            if (type != null)
            {
                var go = new GameObject(objectName);
                go.AddComponent(type);
                Debug.Log($"✅ 创建 {objectName}");
            }
        }
        
        /// <summary>
        /// 设置Cinemachine相机
        /// </summary>
        void SetupCinemachineCamera()
        {
            Debug.Log("📹 设置Cinemachine相机...");
            
            // 检查是否有Cinemachine
            var cinemachineType = System.Type.GetType("Cinemachine.CinemachineVirtualCamera");
            if (cinemachineType == null)
            {
                Debug.LogWarning("⚠️ 未找到Cinemachine，跳过相机设置");
                return;
            }
            
            // 创建虚拟相机
            var vcamObj = new GameObject("CM vcam1");
            var vcam = vcamObj.AddComponent(cinemachineType);
            
            // 设置相机位置
            vcamObj.transform.position = new Vector3(0, 2.5f, -5);
            vcamObj.transform.rotation = Quaternion.Euler(10, 0, 0);
            
            // 创建CinemachineBrain（如果主相机没有的话）
            var mainCamera = Camera.main;
            if (mainCamera != null)
            {
                var brainType = System.Type.GetType("Cinemachine.CinemachineBrain");
                if (brainType != null && mainCamera.GetComponent(brainType) == null)
                {
                    mainCamera.gameObject.AddComponent(brainType);
                    Debug.Log("✅ 添加CinemachineBrain到主相机");
                }
            }
            
            Debug.Log("✅ Cinemachine相机设置完成");
        }
    }
} 