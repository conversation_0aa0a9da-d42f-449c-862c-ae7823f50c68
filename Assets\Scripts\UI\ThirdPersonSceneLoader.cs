using UnityEngine;
using System.Threading.Tasks;
using UniVRM10;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization.UI
{
    /// <summary>
    /// 第三人称场景加载器 - 专门负责第三人称场景的加载和初始化
    /// </summary>
    public class ThirdPersonSceneLoader : MonoBehaviour
    {
        [Header("场景设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool autoLoadOnStart = true;
        [SerializeField] private bool autoSetupEnvironment = true;
        
        [Header("VRM设置")]
        [SerializeField] private Vector3 vrmSpawnPosition = Vector3.zero;
        [SerializeField] private Quaternion vrmSpawnRotation = Quaternion.identity;
        [SerializeField] private Vector3 vrmSpawnScale = Vector3.one;
        
        [Header("环境设置")]
        [SerializeField] private GameObject groundPrefab;
        [SerializeField] private Light mainLight;
        [SerializeField] private Camera mainCamera;
        
        // 事件系统
        public System.Action<GameObject> OnVRMLoaded;
        public System.Action OnSceneSetupComplete;
        public System.Action<string> OnLoadError;
        
        // 内部状态
        private GameObject currentVRMObject;
        private bool isLoading = false;
        
        private void Start()
        {
            if (autoLoadOnStart)
            {
                _ = LoadThirdPersonScene();
            }
        }
        
        /// <summary>
        /// 加载第三人称场景的完整流程
        /// </summary>
        public async Task<bool> LoadThirdPersonScene()
        {
            if (isLoading)
            {
                LogDebug("⏳ 场景加载正在进行中，请稍候...");
                return false;
            }
            
            isLoading = true;
            
            try
            {
                LogDebug("🏗️ 开始加载第三人称场景...");
                
                // 1. 设置场景环境
                if (autoSetupEnvironment)
                {
                    SetupSceneEnvironment();
                }
                
                // 2. 加载VRM模型
                var vrmObject = await LoadVRMModel();
                if (vrmObject == null)
                {
                    LogError("❌ VRM模型加载失败");
                    return false;
                }
                
                // 3. 应用角色数据
                await ApplyCharacterData(vrmObject);
                
                // 4. 配置第三人称控制器（如果需要）
                // ConfigureThirdPersonController(vrmObject);
                
                currentVRMObject = vrmObject;
                
                LogDebug("✅ 第三人称场景加载完成");
                OnSceneSetupComplete?.Invoke();
                return true;
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 加载第三人称场景时发生错误: {ex.Message}");
                OnLoadError?.Invoke(ex.Message);
                return false;
            }
            finally
            {
                isLoading = false;
            }
        }
        
        /// <summary>
        /// 加载VRM模型
        /// </summary>
        public async Task<GameObject> LoadVRMModel()
        {
            try
            {
                LogDebug("🎭 开始加载VRM模型...");
                
                // 检查是否有保存的VRM数据
                var runtimeLoader = VRMRuntimeLoader.Instance;
                if (runtimeLoader != null && runtimeLoader.HasSavedVRMData())
                {
                    LogDebug("📦 发现保存的VRM数据，正在加载...");
                    var vrmObject = await runtimeLoader.LoadVRMInNewScene();
                    if (vrmObject != null)
                    {
                        // 设置VRM位置
                        vrmObject.transform.position = vrmSpawnPosition;
                        vrmObject.transform.rotation = vrmSpawnRotation;
                        vrmObject.transform.localScale = vrmSpawnScale;
                        
                        LogDebug($"✅ VRM模型加载成功: {vrmObject.name}");
                        OnVRMLoaded?.Invoke(vrmObject);
                        return vrmObject;
                    }
                }
                
                // 如果没有保存的数据，尝试加载默认VRM
                LogDebug("⚠️ 没有保存的VRM数据，需要手动加载VRM模型");
                return null;
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 加载VRM模型时发生错误: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 应用角色数据
        /// </summary>
        public async Task ApplyCharacterData(GameObject vrmObject)
        {
            if (vrmObject == null)
            {
                LogError("❌ VRM对象为空，无法应用角色数据");
                return;
            }
            
            try
            {
                LogDebug("🎨 开始应用角色数据...");
                
                // 检查是否有保存的渲染状态
                var stateManager = VRMStateManager.Instance;
                if (stateManager != null && stateManager.HasSavedRenderState())
                {
                    var renderState = stateManager.GetSavedRenderState();
                    if (renderState != null)
                    {
                        LogDebug("🎭 应用保存的渲染状态...");
                        await stateManager.ApplyVRMRenderState(vrmObject, renderState);
                        LogDebug("✅ 角色数据应用完成");
                    }
                }
                else
                {
                    LogDebug("⚠️ 没有保存的渲染状态数据");
                }
                
                await Task.Yield();
            }
            catch (System.Exception ex)
            {
                LogError($"❌ 应用角色数据时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 设置场景环境
        /// </summary>
        private void SetupSceneEnvironment()
        {
            LogDebug("🌍 设置场景环境...");
            
            // 设置主光源
            if (mainLight == null)
            {
                mainLight = FindObjectOfType<Light>();
                if (mainLight == null)
                {
                    var lightObject = new GameObject("Main Light");
                    mainLight = lightObject.AddComponent<Light>();
                    mainLight.type = LightType.Directional;
                    mainLight.intensity = 1.0f;
                    lightObject.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
                }
            }
            
            // 设置主相机
            if (mainCamera == null)
            {
                mainCamera = Camera.main;
                if (mainCamera == null)
                {
                    var cameraObject = new GameObject("Main Camera");
                    mainCamera = cameraObject.AddComponent<Camera>();
                    cameraObject.transform.position = new Vector3(0, 1.5f, -3);
                    cameraObject.transform.LookAt(Vector3.up);
                }
            }
            
            // 创建地面（如果有预制体）
            if (groundPrefab != null && GameObject.FindObjectOfType<Terrain>() == null)
            {
                var ground = Instantiate(groundPrefab);
                ground.name = "Ground";
            }
            
            LogDebug("✅ 场景环境设置完成");
        }
        
        /// <summary>
        /// 获取当前VRM对象
        /// </summary>
        public GameObject GetCurrentVRMObject()
        {
            return currentVRMObject;
        }
        
        /// <summary>
        /// 检查是否正在加载
        /// </summary>
        public bool IsLoading()
        {
            return isLoading;
        }
        
        /// <summary>
        /// 手动设置VRM对象
        /// </summary>
        public void SetVRMObject(GameObject vrmObject)
        {
            currentVRMObject = vrmObject;
            LogDebug($"🎯 手动设置VRM对象: {vrmObject?.name ?? "null"}");
        }
        
        /// <summary>
        /// 重新加载VRM模型
        /// </summary>
        public async Task<bool> ReloadVRMModel()
        {
            if (currentVRMObject != null)
            {
                DestroyImmediate(currentVRMObject);
                currentVRMObject = null;
            }
            
            var vrmObject = await LoadVRMModel();
            if (vrmObject != null)
            {
                await ApplyCharacterData(vrmObject);
                currentVRMObject = vrmObject;
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取加载状态信息
        /// </summary>
        public string GetStatusInfo()
        {
            if (isLoading)
                return "正在加载场景...";
            
            if (currentVRMObject != null)
                return $"VRM已加载: {currentVRMObject.name}";
            
            return "等待VRM加载";
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[ThirdPersonSceneLoader] {message}");
            }
        }
        
        private void LogError(string message)
        {
            Debug.LogError($"[ThirdPersonSceneLoader] {message}");
        }
        
        #region Unity编辑器支持
        
#if UNITY_EDITOR
        [ContextMenu("重新加载场景")]
        private void TestReloadScene()
        {
            if (Application.isPlaying)
            {
                _ = LoadThirdPersonScene();
            }
            else
            {
                Debug.Log("请在运行时测试场景加载");
            }
        }
        
        [ContextMenu("检查组件状态")]
        private void CheckComponentStatus()
        {
            Debug.Log($"ThirdPersonSceneLoader状态报告:");
            Debug.Log($"  当前VRM对象: {currentVRMObject?.name ?? "无"}");
            Debug.Log($"  主光源: {mainLight?.name ?? "无"}");
            Debug.Log($"  主相机: {mainCamera?.name ?? "无"}");
            Debug.Log($"  地面预制体: {groundPrefab?.name ?? "无"}");
            Debug.Log($"  当前状态: {GetStatusInfo()}");
        }
#endif
        
        #endregion
    }
}
