Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.22f1 (887be4894c44) revision 8944612'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32657 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\unity\Unity Hub\2022.3.22f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/unity/projects/捏脸换装系统
-logFile
Logs/AssetImportWorker1.log
-srvPort
49755
Successfully changed project path to: D:/unity/projects/捏脸换装系统
D:/unity/projects/捏脸换装系统
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22464] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1026606203 [EditorId] 1026606203 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-Q7AQ761) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [22464] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1026606203 [EditorId] 1026606203 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-Q7AQ761) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with 11 workers.
Refreshing native plugins compatible for Editor in 44.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.22f1 (887be4894c44)
[Subsystems] Discovering subsystems at path D:/unity/Unity Hub/2022.3.22f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/unity/projects/捏脸换装系统/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon RX 7650 GRE (ID=0x7480)
    Vendor:   ATI
    VRAM:     8176 MB
    Driver:   32.0.21010.10
Initialize mono
Mono path[0] = 'D:/unity/Unity Hub/2022.3.22f1/Editor/Data/Managed'
Mono path[1] = 'D:/unity/Unity Hub/2022.3.22f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/unity/Unity Hub/2022.3.22f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56892
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/unity/Unity Hub/2022.3.22f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/unity/Unity Hub/2022.3.22f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/unity/Unity Hub/2022.3.22f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/unity/Unity Hub/2022.3.22f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.032923 seconds.
- Loaded All Assemblies, in  0.395 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 969 ms
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.255 seconds
Domain Reload Profiling: 1645ms
	BeginReloadAssembly (126ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (137ms)
		LoadAssemblies (121ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (133ms)
			TypeCache.Refresh (132ms)
				TypeCache.ScanAssembly (120ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (1256ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1208ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1071ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.169 seconds
Refreshing native plugins compatible for Editor in 14.11 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.269 seconds
Domain Reload Profiling: 2421ms
	BeginReloadAssembly (182ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (860ms)
		LoadAssemblies (594ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (379ms)
			TypeCache.Refresh (324ms)
				TypeCache.ScanAssembly (299ms)
			ScanForSourceGeneratedMonoScriptInfo (39ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1270ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1045ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (57ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (210ms)
			ProcessInitializeOnLoadAttributes (684ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 13.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6404 Unused Serialized files (Serialized files now loaded: 0)
Unloading 101 unused Assets / (1.2 MB). Loaded Objects now: 6857.
Memory consumption went from 235.8 MB to 234.6 MB.
Total: 4.765800 ms (FindLiveObjects: 0.596000 ms CreateObjectMapping: 0.181900 ms MarkObjects: 3.488100 ms  DeleteObjects: 0.499000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.715 seconds
Refreshing native plugins compatible for Editor in 13.23 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.957 seconds
Domain Reload Profiling: 2656ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (426ms)
		LoadAssemblies (488ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1958ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (701ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (463ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 12.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6296 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6872.
Memory consumption went from 212.2 MB to 211.0 MB.
Total: 4.330600 ms (FindLiveObjects: 0.398400 ms CreateObjectMapping: 0.381800 ms MarkObjects: 3.123900 ms  DeleteObjects: 0.425600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.669 seconds
Refreshing native plugins compatible for Editor in 11.10 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.365 seconds
Domain Reload Profiling: 3021ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (390ms)
		LoadAssemblies (480ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2365ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (875ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (53ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (596ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 12.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6296 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6887.
Memory consumption went from 214.1 MB to 212.9 MB.
Total: 4.593600 ms (FindLiveObjects: 0.553800 ms CreateObjectMapping: 0.311100 ms MarkObjects: 3.347500 ms  DeleteObjects: 0.380700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.670 seconds
Refreshing native plugins compatible for Editor in 11.87 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.235 seconds
Domain Reload Profiling: 2889ms
	BeginReloadAssembly (178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (393ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2236ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (807ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (140ms)
			ProcessInitializeOnLoadAttributes (542ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 13.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6296 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6902.
Memory consumption went from 216.0 MB to 214.9 MB.
Total: 5.140300 ms (FindLiveObjects: 0.498200 ms CreateObjectMapping: 0.597200 ms MarkObjects: 3.379100 ms  DeleteObjects: 0.664700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.668 seconds
Refreshing native plugins compatible for Editor in 10.73 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.258 seconds
Domain Reload Profiling: 2912ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (383ms)
		LoadAssemblies (465ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2258ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (776ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (137ms)
			ProcessInitializeOnLoadAttributes (517ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 13.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6296 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6917.
Memory consumption went from 218.0 MB to 216.8 MB.
Total: 5.392800 ms (FindLiveObjects: 0.653500 ms CreateObjectMapping: 0.401600 ms MarkObjects: 3.945700 ms  DeleteObjects: 0.391200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.990 seconds
Refreshing native plugins compatible for Editor in 9.73 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.059 seconds
Domain Reload Profiling: 3033ms
	BeginReloadAssembly (271ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (585ms)
		LoadAssemblies (677ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (50ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (26ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2059ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (688ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 10.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6296 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6932.
Memory consumption went from 219.8 MB to 218.7 MB.
Total: 4.415300 ms (FindLiveObjects: 0.379700 ms CreateObjectMapping: 0.314800 ms MarkObjects: 3.320400 ms  DeleteObjects: 0.399600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.795 seconds
Refreshing native plugins compatible for Editor in 10.63 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.880 seconds
Domain Reload Profiling: 2656ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (495ms)
		LoadAssemblies (560ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (42ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1881ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (636ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (427ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 8.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6296 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6947.
Memory consumption went from 221.9 MB to 220.7 MB.
Total: 3.568100 ms (FindLiveObjects: 0.366200 ms CreateObjectMapping: 0.164900 ms MarkObjects: 2.698900 ms  DeleteObjects: 0.337500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.832 seconds
Refreshing native plugins compatible for Editor in 11.37 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.913 seconds
Domain Reload Profiling: 2725ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (510ms)
		LoadAssemblies (582ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (41ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1913ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (633ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (33ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 13.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6297 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6963.
Memory consumption went from 223.8 MB to 222.6 MB.
Total: 4.698800 ms (FindLiveObjects: 0.532800 ms CreateObjectMapping: 0.337400 ms MarkObjects: 3.417900 ms  DeleteObjects: 0.409800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.832 seconds
Refreshing native plugins compatible for Editor in 13.99 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.373 seconds
Domain Reload Profiling: 3189ms
	BeginReloadAssembly (212ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (506ms)
		LoadAssemblies (610ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (22ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (2374ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (785ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (45ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (519ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 12.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6297 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6978.
Memory consumption went from 225.8 MB to 224.6 MB.
Total: 4.929200 ms (FindLiveObjects: 0.374800 ms CreateObjectMapping: 0.311600 ms MarkObjects: 3.850100 ms  DeleteObjects: 0.392100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.764 seconds
Refreshing native plugins compatible for Editor in 10.88 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.841 seconds
Domain Reload Profiling: 2589ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (454ms)
		LoadAssemblies (517ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1841ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (638ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (425ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 11.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6297 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 6993.
Memory consumption went from 227.7 MB to 226.5 MB.
Total: 4.716400 ms (FindLiveObjects: 0.422100 ms CreateObjectMapping: 0.363400 ms MarkObjects: 3.516600 ms  DeleteObjects: 0.413300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.690 seconds
Refreshing native plugins compatible for Editor in 15.03 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.141 seconds
Domain Reload Profiling: 2815ms
	BeginReloadAssembly (180ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (413ms)
		LoadAssemblies (490ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (21ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2141ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (725ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (491ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 14.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6297 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7008.
Memory consumption went from 229.6 MB to 228.4 MB.
Total: 5.207100 ms (FindLiveObjects: 0.372700 ms CreateObjectMapping: 0.295700 ms MarkObjects: 4.162800 ms  DeleteObjects: 0.375300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.743 seconds
Refreshing native plugins compatible for Editor in 11.11 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.872 seconds
Domain Reload Profiling: 2600ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (463ms)
		LoadAssemblies (522ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (42ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (21ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1872ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (641ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (433ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 11.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7024.
Memory consumption went from 231.6 MB to 230.4 MB.
Total: 4.097900 ms (FindLiveObjects: 0.406500 ms CreateObjectMapping: 0.341300 ms MarkObjects: 2.907200 ms  DeleteObjects: 0.442000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.700 seconds
Refreshing native plugins compatible for Editor in 13.63 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.187 seconds
Domain Reload Profiling: 2871ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (411ms)
		LoadAssemblies (495ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (20ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (2188ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (766ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (505ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 13.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7039.
Memory consumption went from 233.5 MB to 232.4 MB.
Total: 4.380900 ms (FindLiveObjects: 0.415500 ms CreateObjectMapping: 0.292400 ms MarkObjects: 3.217100 ms  DeleteObjects: 0.455200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.684 seconds
Refreshing native plugins compatible for Editor in 12.53 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.355 seconds
Domain Reload Profiling: 3025ms
	BeginReloadAssembly (182ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (412ms)
		LoadAssemblies (494ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2355ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (793ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (537ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 38.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7054.
Memory consumption went from 235.4 MB to 234.3 MB.
Total: 6.176300 ms (FindLiveObjects: 0.703600 ms CreateObjectMapping: 0.208100 ms MarkObjects: 4.784000 ms  DeleteObjects: 0.479700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.704 seconds
Refreshing native plugins compatible for Editor in 11.31 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.148 seconds
Domain Reload Profiling: 2836ms
	BeginReloadAssembly (184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (413ms)
		LoadAssemblies (495ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2149ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (753ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (504ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 10.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7069.
Memory consumption went from 237.4 MB to 236.2 MB.
Total: 5.637700 ms (FindLiveObjects: 0.443200 ms CreateObjectMapping: 0.677600 ms MarkObjects: 4.096900 ms  DeleteObjects: 0.419100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.711 seconds
Refreshing native plugins compatible for Editor in 11.01 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.854 seconds
Domain Reload Profiling: 2551ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (435ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (43ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1855ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (656ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (37ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (442ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 11.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6300 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7086.
Memory consumption went from 239.3 MB to 238.1 MB.
Total: 3.954900 ms (FindLiveObjects: 0.405900 ms CreateObjectMapping: 0.168300 ms MarkObjects: 3.001100 ms  DeleteObjects: 0.378900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.682 seconds
Refreshing native plugins compatible for Editor in 12.51 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.104 seconds
Domain Reload Profiling: 2773ms
	BeginReloadAssembly (182ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (486ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2105ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (770ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 13.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6300 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7101.
Memory consumption went from 241.3 MB to 240.1 MB.
Total: 4.834600 ms (FindLiveObjects: 0.673500 ms CreateObjectMapping: 0.337000 ms MarkObjects: 3.382200 ms  DeleteObjects: 0.441100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.766 seconds
Refreshing native plugins compatible for Editor in 13.90 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.212 seconds
Domain Reload Profiling: 2962ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (470ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (45ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (21ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2212ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (794ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (40ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (567ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (19ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 11.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6300 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7116.
Memory consumption went from 243.2 MB to 242.1 MB.
Total: 4.142700 ms (FindLiveObjects: 0.446800 ms CreateObjectMapping: 0.313900 ms MarkObjects: 2.965000 ms  DeleteObjects: 0.416100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.745 seconds
Refreshing native plugins compatible for Editor in 14.43 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.885 seconds
Domain Reload Profiling: 2615ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (454ms)
		LoadAssemblies (510ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (52ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (26ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (646ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (33ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (434ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 11.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 55 unused Assets / (1.2 MB). Loaded Objects now: 7129.
Memory consumption went from 245.1 MB to 243.9 MB.
Total: 4.209100 ms (FindLiveObjects: 0.465700 ms CreateObjectMapping: 0.377000 ms MarkObjects: 2.963700 ms  DeleteObjects: 0.401800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.675 seconds
Refreshing native plugins compatible for Editor in 11.02 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.186 seconds
Domain Reload Profiling: 2847ms
	BeginReloadAssembly (182ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (396ms)
		LoadAssemblies (479ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2187ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (794ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (529ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 13.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7144.
Memory consumption went from 247.0 MB to 245.8 MB.
Total: 4.500900 ms (FindLiveObjects: 0.489400 ms CreateObjectMapping: 0.271200 ms MarkObjects: 3.327400 ms  DeleteObjects: 0.412000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.673 seconds
Refreshing native plugins compatible for Editor in 11.19 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.187 seconds
Domain Reload Profiling: 2846ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (401ms)
		LoadAssemblies (481ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2188ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (800ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (137ms)
			ProcessInitializeOnLoadAttributes (539ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 23.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7159.
Memory consumption went from 248.8 MB to 247.7 MB.
Total: 4.844700 ms (FindLiveObjects: 0.710100 ms CreateObjectMapping: 0.400800 ms MarkObjects: 3.278500 ms  DeleteObjects: 0.454700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.666 seconds
Refreshing native plugins compatible for Editor in 10.67 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.134 seconds
Domain Reload Profiling: 2788ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (467ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2135ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (776ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (127ms)
			ProcessInitializeOnLoadAttributes (532ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 12.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7174.
Memory consumption went from 250.9 MB to 249.7 MB.
Total: 4.924200 ms (FindLiveObjects: 0.561300 ms CreateObjectMapping: 0.430800 ms MarkObjects: 3.321900 ms  DeleteObjects: 0.609900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.684 seconds
Refreshing native plugins compatible for Editor in 11.99 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.205 seconds
Domain Reload Profiling: 2875ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (406ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2206ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (803ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (548ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 13.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7189.
Memory consumption went from 252.8 MB to 251.6 MB.
Total: 4.565400 ms (FindLiveObjects: 0.778500 ms CreateObjectMapping: 0.333000 ms MarkObjects: 3.089500 ms  DeleteObjects: 0.363600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.684 seconds
Refreshing native plugins compatible for Editor in 11.48 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.192 seconds
Domain Reload Profiling: 2861ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (411ms)
		LoadAssemblies (489ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2192ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (802ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (48ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (143ms)
			ProcessInitializeOnLoadAttributes (529ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 13.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7204.
Memory consumption went from 254.7 MB to 253.6 MB.
Total: 4.887000 ms (FindLiveObjects: 0.456400 ms CreateObjectMapping: 0.330200 ms MarkObjects: 3.714000 ms  DeleteObjects: 0.385600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.686 seconds
Refreshing native plugins compatible for Editor in 11.29 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.284 seconds
Domain Reload Profiling: 2956ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (483ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2285ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (774ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (520ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 11.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7219.
Memory consumption went from 256.7 MB to 255.5 MB.
Total: 4.367300 ms (FindLiveObjects: 0.487800 ms CreateObjectMapping: 0.335300 ms MarkObjects: 3.155300 ms  DeleteObjects: 0.388200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.665 seconds
Refreshing native plugins compatible for Editor in 10.88 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.074 seconds
Domain Reload Profiling: 2727ms
	BeginReloadAssembly (179ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (397ms)
		LoadAssemblies (478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2074ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (688ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (38ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (461ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 12.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7234.
Memory consumption went from 258.5 MB to 257.4 MB.
Total: 3.998400 ms (FindLiveObjects: 0.406700 ms CreateObjectMapping: 0.276300 ms MarkObjects: 2.950700 ms  DeleteObjects: 0.364000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.677 seconds
Refreshing native plugins compatible for Editor in 12.46 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.071 seconds
Domain Reload Profiling: 2736ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (395ms)
		LoadAssemblies (483ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2071ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (754ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (126ms)
			ProcessInitializeOnLoadAttributes (507ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 13.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7249.
Memory consumption went from 260.5 MB to 259.4 MB.
Total: 5.584600 ms (FindLiveObjects: 0.408300 ms CreateObjectMapping: 0.310600 ms MarkObjects: 4.295300 ms  DeleteObjects: 0.569700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.763 seconds
Refreshing native plugins compatible for Editor in 11.07 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.892 seconds
Domain Reload Profiling: 2640ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (481ms)
		LoadAssemblies (541ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (44ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (646ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (431ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 9.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7264.
Memory consumption went from 262.5 MB to 261.3 MB.
Total: 4.167400 ms (FindLiveObjects: 0.573700 ms CreateObjectMapping: 0.324200 ms MarkObjects: 2.909900 ms  DeleteObjects: 0.359000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.741 seconds
Refreshing native plugins compatible for Editor in 11.23 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.223 seconds
Domain Reload Profiling: 2949ms
	BeginReloadAssembly (219ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (416ms)
		LoadAssemblies (527ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (22ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2224ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (748ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (45ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (125ms)
			ProcessInitializeOnLoadAttributes (497ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 13.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7279.
Memory consumption went from 264.4 MB to 263.2 MB.
Total: 4.841600 ms (FindLiveObjects: 0.419700 ms CreateObjectMapping: 0.332900 ms MarkObjects: 3.719300 ms  DeleteObjects: 0.369100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.748 seconds
Refreshing native plugins compatible for Editor in 13.27 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.991 seconds
Domain Reload Profiling: 2723ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (459ms)
		LoadAssemblies (517ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (44ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1991ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (693ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (462ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 10.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7294.
Memory consumption went from 266.3 MB to 265.2 MB.
Total: 4.369200 ms (FindLiveObjects: 0.444700 ms CreateObjectMapping: 0.426000 ms MarkObjects: 3.102600 ms  DeleteObjects: 0.395300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.729 seconds
Refreshing native plugins compatible for Editor in 12.27 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.275 seconds
Domain Reload Profiling: 2988ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (427ms)
		LoadAssemblies (514ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (20ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (2275ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (804ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (45ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (537ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (19ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 12.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6298 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7309.
Memory consumption went from 268.2 MB to 267.1 MB.
Total: 4.321100 ms (FindLiveObjects: 0.394800 ms CreateObjectMapping: 0.289700 ms MarkObjects: 3.216600 ms  DeleteObjects: 0.419200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.785 seconds
Refreshing native plugins compatible for Editor in 16.58 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.412 seconds
Domain Reload Profiling: 3183ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (479ms)
		LoadAssemblies (529ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (62ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (31ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (2412ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (869ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (604ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 10.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6301 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7327.
Memory consumption went from 270.3 MB to 269.1 MB.
Total: 4.956300 ms (FindLiveObjects: 0.446600 ms CreateObjectMapping: 0.344700 ms MarkObjects: 3.678300 ms  DeleteObjects: 0.485900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.851 seconds
Refreshing native plugins compatible for Editor in 12.30 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.068 seconds
Domain Reload Profiling: 2902ms
	BeginReloadAssembly (222ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (523ms)
		LoadAssemblies (595ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (44ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (21ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2068ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (690ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (125ms)
			ProcessInitializeOnLoadAttributes (460ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (19ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 10.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7347.
Memory consumption went from 272.3 MB to 271.1 MB.
Total: 4.342200 ms (FindLiveObjects: 0.425600 ms CreateObjectMapping: 0.364500 ms MarkObjects: 3.135500 ms  DeleteObjects: 0.415500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.801 seconds
Refreshing native plugins compatible for Editor in 11.52 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.934 seconds
Domain Reload Profiling: 2720ms
	BeginReloadAssembly (228ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (474ms)
		LoadAssemblies (536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (55ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (24ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1934ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (637ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (428ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 10.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7362.
Memory consumption went from 274.2 MB to 273.1 MB.
Total: 3.775100 ms (FindLiveObjects: 0.385800 ms CreateObjectMapping: 0.166900 ms MarkObjects: 2.861100 ms  DeleteObjects: 0.360700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.740 seconds
Refreshing native plugins compatible for Editor in 11.38 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.658 seconds
Domain Reload Profiling: 3384ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (533ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2659ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (946ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (70ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (185ms)
			ProcessInitializeOnLoadAttributes (598ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 12.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7377.
Memory consumption went from 276.2 MB to 275.0 MB.
Total: 6.565500 ms (FindLiveObjects: 1.245800 ms CreateObjectMapping: 0.448100 ms MarkObjects: 4.461500 ms  DeleteObjects: 0.409400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.685 seconds
Refreshing native plugins compatible for Editor in 13.58 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.241 seconds
Domain Reload Profiling: 2911ms
	BeginReloadAssembly (180ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (405ms)
		LoadAssemblies (486ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (21ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2242ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (744ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (45ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (490ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 14.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7392.
Memory consumption went from 278.1 MB to 276.9 MB.
Total: 4.471500 ms (FindLiveObjects: 0.412400 ms CreateObjectMapping: 0.295100 ms MarkObjects: 3.380000 ms  DeleteObjects: 0.383400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.733 seconds
Refreshing native plugins compatible for Editor in 11.95 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.213 seconds
Domain Reload Profiling: 2931ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (448ms)
		LoadAssemblies (533ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2214ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (786ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (535ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 13.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7407.
Memory consumption went from 280.0 MB to 278.9 MB.
Total: 4.638500 ms (FindLiveObjects: 0.423200 ms CreateObjectMapping: 0.318600 ms MarkObjects: 3.386000 ms  DeleteObjects: 0.510100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.768 seconds
Refreshing native plugins compatible for Editor in 12.42 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.073 seconds
Domain Reload Profiling: 2824ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (454ms)
		LoadAssemblies (517ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (43ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2073ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (721ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (140ms)
			ProcessInitializeOnLoadAttributes (470ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 11.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7422.
Memory consumption went from 282.0 MB to 280.8 MB.
Total: 4.427000 ms (FindLiveObjects: 0.488700 ms CreateObjectMapping: 0.332300 ms MarkObjects: 3.221000 ms  DeleteObjects: 0.384200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.701 seconds
Refreshing native plugins compatible for Editor in 13.52 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.489 seconds
Domain Reload Profiling: 3176ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (415ms)
		LoadAssemblies (495ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (2490ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (828ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (570ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 13.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7437.
Memory consumption went from 283.9 MB to 282.8 MB.
Total: 4.355700 ms (FindLiveObjects: 0.439000 ms CreateObjectMapping: 0.308200 ms MarkObjects: 3.211800 ms  DeleteObjects: 0.396200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.698 seconds
Refreshing native plugins compatible for Editor in 12.82 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.225 seconds
Domain Reload Profiling: 2907ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (413ms)
		LoadAssemblies (498ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (2225ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (807ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 16.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6306 Unused Serialized files (Serialized files now loaded: 0)
Unloading 53 unused Assets / (1.2 MB). Loaded Objects now: 7452.
Memory consumption went from 285.8 MB to 284.7 MB.
Total: 4.540700 ms (FindLiveObjects: 0.486000 ms CreateObjectMapping: 0.522100 ms MarkObjects: 3.068300 ms  DeleteObjects: 0.463700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
