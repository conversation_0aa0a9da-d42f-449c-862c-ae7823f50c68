using UnityEngine;
using UnityEngine.UI;
using VRoidFaceCustomization.Core;

namespace VRoidFaceCustomization.UI
{
    /// <summary>
    /// 场景切换按钮 - 处理保存数据并切换到测试场景
    /// </summary>
    [RequireComponent(typeof(Button))]
    public class SceneTransitionButton : MonoBehaviour
    {
        [Header("VRM设置")]
        [SerializeField] private GameObject targetVRMObject; // 目标VRM对象
        [SerializeField] private bool autoFindVRM = true; // 自动查找VRM对象
        
        [Header("场景设置")]
        [SerializeField] private string targetSceneName = "ThirdPersonTestScene"; // 目标场景名称
        [SerializeField] private bool saveBeforeTransition = true; // 切换前是否保存
        
        [Header("UI反馈")]
        [SerializeField] private Text buttonText; // 按钮文本
        [SerializeField] private Text statusText; // 状态文本
        [SerializeField] private string defaultButtonText = "进入测试场景";
        [SerializeField] private string processingButtonText = "处理中...";
        [SerializeField] private string savingText = "保存数据中...";
        [SerializeField] private string transitioningText = "切换场景中...";
        [SerializeField] private float statusDisplayTime = 2f;
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        
        private Button transitionButton;
        private bool isProcessing = false;
        private bool saveDataSuccess = false;
        
        #region Unity生命周期
        private void Awake()
        {
            transitionButton = GetComponent<Button>();
            
            if (transitionButton == null)
            {
                LogDebug("❌ 未找到Button组件");
                return;
            }
            
            // 绑定点击事件
            transitionButton.onClick.AddListener(OnTransitionButtonClicked);
            LogDebug("✅ SceneTransitionButton 初始化完成");
        }
        
        private void Start()
        {
            // 自动查找VRM对象
            if (autoFindVRM && targetVRMObject == null)
            {
                FindVRMObject();
            }
            
            // 初始化UI状态
            UpdateButtonState();
            InitializeButtonText();
        }
        
        private void OnDestroy()
        {
            if (transitionButton != null)
            {
                transitionButton.onClick.RemoveListener(OnTransitionButtonClicked);
            }
        }
        #endregion
        
        #region 场景切换功能
        /// <summary>
        /// 场景切换按钮点击事件
        /// </summary>
        private void OnTransitionButtonClicked()
        {
            if (isProcessing)
            {
                LogDebug("⚠️ 正在处理中，请稍候");
                return;
            }
            
            if (saveBeforeTransition && targetVRMObject == null)
            {
                LogDebug("❌ 未设置目标VRM对象，无法保存数据");
                ShowStatus("错误：未找到VRM对象");
                return;
            }
            
            LogDebug($"🚀 开始场景切换流程，目标场景: {targetSceneName}");
            StartCoroutine(TransitionToSceneCoroutine());
        }
        
        /// <summary>
        /// 场景切换的协程
        /// </summary>
        private System.Collections.IEnumerator TransitionToSceneCoroutine()
        {
            isProcessing = true;
            UpdateButtonState();

            // 执行场景切换流程
            yield return StartCoroutine(ExecuteSceneTransition());

            isProcessing = false;
            UpdateButtonState();
        }

        /// <summary>
        /// 执行场景切换的主要逻辑
        /// </summary>
        private System.Collections.IEnumerator ExecuteSceneTransition()
        {
            string errorMessage = "";
            bool success = false;

            try
            {
                // 触发切换开始事件
                OnTransitionStart?.Invoke(targetSceneName);
                success = true;
            }
            catch (System.Exception e)
            {
                errorMessage = e.Message;
                LogDebug($"❌ 场景切换过程中发生错误: {errorMessage}");
                ShowStatus("切换失败");
                OnTransitionFailed?.Invoke(errorMessage);
                yield break;
            }

            if (success)
            {
                // 执行场景切换流程
                yield return StartCoroutine(ExecuteTransitionCoroutine());
            }
        }

        private System.Collections.IEnumerator ExecuteTransitionCoroutine()
        {
            if (saveBeforeTransition)
            {
                // 步骤1：保存角色数据
                LogDebug("💾 保存角色数据...");
                ShowStatus(savingText);

                // 使用共享变量存储结果
                saveDataSuccess = false;
                yield return StartCoroutine(SaveCharacterDataCoroutine());

                if (!saveDataSuccess)
                {
                    LogDebug("❌ 数据保存失败，取消场景切换");
                    ShowStatus("保存失败，切换取消");
                    yield break;
                }

                LogDebug("✅ 角色数据保存成功");
            }

            // 步骤2：切换场景
            LogDebug($"🔄 切换到场景: {targetSceneName}");
            ShowStatus(transitioningText);

            // 使用SceneController进行场景切换
            var sceneController = SceneController.Instance;

            if (saveBeforeTransition && targetVRMObject != null)
            {
                // 保存并切换
                sceneController.SaveAndGoToTestScene(targetVRMObject);
            }
            else
            {
                // 直接切换
                sceneController.GoToScene(targetSceneName);
            }

            // 等待场景切换开始
            yield return new WaitForSeconds(0.5f);

            LogDebug("🎉 场景切换流程完成");

            // 触发切换完成事件
            OnTransitionComplete?.Invoke(targetSceneName);
        }
        
        /// <summary>
        /// 保存角色数据的协程
        /// </summary>
        private System.Collections.IEnumerator SaveCharacterDataCoroutine()
        {
            try
            {
                // 获取数据收集器
                var collector = GetOrCreateDataCollector();
                
                // 收集角色参数
                var parameters = collector.CollectFromVRM(targetVRMObject);
                
                if (parameters == null || !parameters.IsValid())
                {
                    LogDebug("❌ 角色参数收集失败");
                    saveDataSuccess = false;
                    yield break;
                }
                
                // 设置默认角色名称
                if (string.IsNullOrEmpty(parameters.characterName))
                {
                    parameters.characterName = "Character_" + System.DateTime.Now.ToString("MMdd_HHmm");
                }
                
                LogDebug($"✅ 角色参数收集完成: {parameters.characterName}");
                LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
                LogDebug($"   👔 服装数量: {parameters.clothingData.GetClothingCount()}件");
                
                // 保存到桥接器
                var bridge = CharacterDataBridge.Instance;
                bridge.SetCharacterParameters(parameters);
                
                LogDebug("📤 数据已保存到桥接器");

                saveDataSuccess = true;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 保存数据时发生错误: {e.Message}");
                saveDataSuccess = false;
            }
        }
        #endregion
        
        #region UI更新
        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonState()
        {
            if (transitionButton != null)
            {
                bool canTransition = !isProcessing && (!saveBeforeTransition || targetVRMObject != null);
                transitionButton.interactable = canTransition;
            }
            
            UpdateButtonText();
        }
        
        /// <summary>
        /// 更新按钮文本
        /// </summary>
        private void UpdateButtonText()
        {
            if (buttonText != null)
            {
                if (isProcessing)
                {
                    buttonText.text = processingButtonText;
                }
                else
                {
                    buttonText.text = defaultButtonText;
                }
            }
        }
        
        /// <summary>
        /// 初始化按钮文本
        /// </summary>
        private void InitializeButtonText()
        {
            if (buttonText != null && string.IsNullOrEmpty(buttonText.text))
            {
                buttonText.text = defaultButtonText;
            }
        }
        
        /// <summary>
        /// 显示状态信息
        /// </summary>
        private void ShowStatus(string message)
        {
            if (statusText != null)
            {
                statusText.text = message;
                
                // 自动清除状态文本
                if (statusDisplayTime > 0)
                {
                    StartCoroutine(ClearStatusAfterDelay());
                }
            }
            
            LogDebug($"📢 状态: {message}");
        }
        
        /// <summary>
        /// 延迟清除状态文本
        /// </summary>
        private System.Collections.IEnumerator ClearStatusAfterDelay()
        {
            yield return new WaitForSeconds(statusDisplayTime);
            
            if (statusText != null)
            {
                statusText.text = "";
            }
        }
        #endregion
        
        #region 辅助方法
        /// <summary>
        /// 自动查找VRM对象
        /// </summary>
        private void FindVRMObject()
        {
            // 查找场景中的VRM对象
            var vrmInstances = FindObjectsOfType<UniVRM10.Vrm10Instance>();
            
            if (vrmInstances.Length > 0)
            {
                targetVRMObject = vrmInstances[0].gameObject;
                LogDebug($"🔍 自动找到VRM对象: {targetVRMObject.name}");
            }
            else
            {
                LogDebug("⚠️ 未找到VRM对象");
            }
        }
        
        /// <summary>
        /// 获取或创建数据收集器
        /// </summary>
        private VRMDataCollector GetOrCreateDataCollector()
        {
            // VRMDataCollector现在是普通类，直接创建实例
            LogDebug("🔧 创建数据收集器");
            return new VRMDataCollector();
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[SceneTransitionButton] {message}");
            }
        }
        #endregion
        
        #region 公共接口
        /// <summary>
        /// 设置目标VRM对象
        /// </summary>
        public void SetTargetVRM(GameObject vrmObject)
        {
            targetVRMObject = vrmObject;
            UpdateButtonState();
            LogDebug($"🎯 设置目标VRM: {(vrmObject != null ? vrmObject.name : "null")}");
        }
        
        /// <summary>
        /// 设置目标场景名称
        /// </summary>
        public void SetTargetScene(string sceneName)
        {
            targetSceneName = sceneName;
            LogDebug($"🎯 设置目标场景: {sceneName}");
        }
        
        /// <summary>
        /// 手动触发场景切换
        /// </summary>
        public void TriggerTransition()
        {
            OnTransitionButtonClicked();
        }
        
        /// <summary>
        /// 检查是否正在处理
        /// </summary>
        public bool IsProcessing()
        {
            return isProcessing;
        }
        #endregion
        
        #region 事件系统
        /// <summary>
        /// 场景切换开始事件
        /// </summary>
        public static event System.Action<string> OnTransitionStart;
        
        /// <summary>
        /// 场景切换完成事件
        /// </summary>
        public static event System.Action<string> OnTransitionComplete;
        
        /// <summary>
        /// 场景切换失败事件
        /// </summary>
        public static event System.Action<string> OnTransitionFailed;
        #endregion
        
        #region 调试功能
        /// <summary>
        /// 显示当前状态
        /// </summary>
        [ContextMenu("显示当前状态")]
        private void ShowCurrentStatus()
        {
            LogDebug($"目标VRM: {(targetVRMObject != null ? targetVRMObject.name : "null")}");
            LogDebug($"目标场景: {targetSceneName}");
            LogDebug($"保存后切换: {saveBeforeTransition}");
            LogDebug($"正在处理: {isProcessing}");
            LogDebug($"按钮可用: {(transitionButton != null ? transitionButton.interactable.ToString() : "null")}");
        }
        
        /// <summary>
        /// 测试场景切换
        /// </summary>
        [ContextMenu("测试场景切换")]
        private void TestTransition()
        {
            if (Application.isPlaying)
            {
                TriggerTransition();
            }
            else
            {
                LogDebug("⚠️ 只能在运行时测试场景切换功能");
            }
        }
        #endregion
    }
}
