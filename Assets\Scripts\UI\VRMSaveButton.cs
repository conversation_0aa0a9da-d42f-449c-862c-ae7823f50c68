using UnityEngine;
using UnityEngine.UI;
using UniVRM10;
using VRoidFaceCustomization.Core;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization.UI
{
    /// <summary>
    /// VRM保存按钮组件
    /// 用于保存当前VRM模型的状态和数据
    /// </summary>
    public class VRMSaveButton : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private Button saveButton;
        
        [Header("设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool autoFindVRM = true;
        [SerializeField] private bool saveVRMFile = true;
        [SerializeField] private bool saveRenderState = true;
        
        [Header("VRM文件路径")]
        [SerializeField] private string vrmFilePath = "Assets/Prefabs/model/xa初始模型.vrm";
        
        private void Start()
        {
            // 如果没有指定按钮，尝试获取当前GameObject上的Button组件
            if (saveButton == null)
            {
                saveButton = GetComponent<Button>();
            }
            
            // 绑定按钮事件
            if (saveButton != null)
            {
                saveButton.onClick.AddListener(OnSaveButtonClicked);
                LogDebug("✅ VRM保存按钮已初始化");
            }
            else
            {
                LogDebug("❌ 未找到Button组件！");
            }
        }
        
        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        public void OnSaveButtonClicked()
        {
            LogDebug("🎯 开始保存VRM数据...");
            StartCoroutine(SaveVRMDataCoroutine());
        }
        
        /// <summary>
        /// 保存VRM数据的协程
        /// </summary>
        private System.Collections.IEnumerator SaveVRMDataCoroutine()
        {
            // 1. 查找VRM对象
            GameObject vrmObject = FindVRMObject();
            if (vrmObject == null)
            {
                LogDebug("❌ 未找到VRM对象！");
                yield break;
            }
            
            LogDebug($"🎭 找到VRM对象: {vrmObject.name}");
            
            // 2. 保存VRM渲染状态
            if (saveRenderState)
            {
                var stateManager = VRMStateManager.Instance;
                if (stateManager != null)
                {
                    var renderState = stateManager.CaptureVRMRenderState(vrmObject);
                    if (renderState != null)
                    {
                        LogDebug("✅ VRM渲染状态保存成功");
                        LogDebug($"   BlendShapes: {renderState.blendShapes.Count}");
                        LogDebug($"   Materials: {renderState.materialProperties.Count}");
                        LogDebug($"   Bones: {renderState.boneTransforms.Count}");
                    }
                    else
                    {
                        LogDebug("❌ VRM渲染状态保存失败");
                    }
                }
                else
                {
                    LogDebug("❌ VRMStateManager未找到！");
                }
            }
            
            // 3. 保存VRM文件数据
            if (saveVRMFile && !string.IsNullOrEmpty(vrmFilePath))
            {
                var runtimeLoader = VRMRuntimeLoader.Instance;
                if (runtimeLoader != null)
                {
                    var saveTask = runtimeLoader.SaveVRMFromFile(vrmFilePath, vrmObject);
                    while (!saveTask.IsCompleted)
                    {
                        yield return null;
                    }
                    
                    if (saveTask.Result)
                    {
                        LogDebug("✅ VRM文件数据保存成功");
                    }
                    else
                    {
                        LogDebug("❌ VRM文件数据保存失败");
                    }
                }
                else
                {
                    LogDebug("❌ VRMRuntimeLoader未找到！");
                }
            }
            
            // 4. 保存角色参数数据（可选）
            SaveCharacterParameters(vrmObject);
            
            LogDebug("🎉 VRM数据保存完成！");
        }
        
        /// <summary>
        /// 查找VRM对象
        /// </summary>
        private GameObject FindVRMObject()
        {
            if (autoFindVRM)
            {
                // 方法1: 通过VRM10UnifiedManager查找
                var unifiedManager = VRM10UnifiedManager.Instance;
                if (unifiedManager != null)
                {
                    var vrmInstance = unifiedManager.GetVRMInstance();
                    if (vrmInstance != null)
                    {
                        return vrmInstance.gameObject;
                    }
                }
                
                // 方法2: 直接查找场景中的Vrm10Instance
                var vrmInstance2 = FindObjectOfType<Vrm10Instance>();
                if (vrmInstance2 != null)
                {
                    return vrmInstance2.gameObject;
                }
                
                LogDebug("⚠️ 自动查找VRM对象失败");
                return null;
            }
            
            return null;
        }
        
        /// <summary>
        /// 保存角色参数数据
        /// </summary>
        private void SaveCharacterParameters(GameObject vrmObject)
        {
            try
            {
                var collector = new VRMDataCollector();
                var parameters = collector.CollectFromVRM(vrmObject);
                
                if (parameters != null && parameters.IsValid())
                {
                    var bridge = CharacterDataBridge.Instance;
                    bridge.SetCharacterParameters(parameters);
                    LogDebug("✅ 角色参数数据保存成功");
                }
                else
                {
                    LogDebug("⚠️ 角色参数收集失败");
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 保存角色参数时出错: {e.Message}");
            }
        }
        
        /// <summary>
        /// 设置VRM文件路径
        /// </summary>
        public void SetVRMFilePath(string filePath)
        {
            vrmFilePath = filePath;
            LogDebug($"📁 VRM文件路径已设置: {filePath}");
        }
        
        /// <summary>
        /// 手动触发保存（供外部调用）
        /// </summary>
        public void TriggerSave()
        {
            OnSaveButtonClicked();
        }
        
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMSaveButton] {message}");
            }
        }
    }
}
