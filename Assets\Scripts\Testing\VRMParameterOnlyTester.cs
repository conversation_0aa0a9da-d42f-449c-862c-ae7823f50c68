using UnityEngine;
using UnityEngine.UI;
using VRoidFaceCustomization;
using VRoidFaceCustomization.UI;
using VRoidFaceCustomization.Data;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM参数传输方案测试器
    /// 专门测试新的简化方案：只传输参数数据，不传输VRM文件
    /// </summary>
    public class VRMParameterOnlyTester : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool autoStartTest = true;
        [SerializeField] private bool detailedLogging = true;
        
        [Header("快捷键")]
        [SerializeField] private KeyCode modifyParametersKey = KeyCode.F4;
        [SerializeField] private KeyCode saveParametersKey = KeyCode.F1;
        [SerializeField] private KeyCode loadParametersKey = KeyCode.F2;
        [SerializeField] private KeyCode sceneTransitionKey = KeyCode.F3;
        [SerializeField] private KeyCode systemDiagnosticKey = KeyCode.F5;
        
        [Header("UI显示")]
        [SerializeField] private Text statusText;
        [SerializeField] private Text parameterCountText;
        [SerializeField] private Text clothingCountText;
        
        private VRM10UnifiedManager unifiedManager;
        private CharacterDataManager dataManager;
        private SceneTransitionManager sceneTransitionManager;
        
        private void Start()
        {
            LogDebug("🎯 VRM参数传输方案测试器已启动");
            LogDebug("🎮 新方案控制说明:");
            LogDebug("   F4 - 修改面部和服装参数");
            LogDebug("   F1 - 保存参数数据");
            LogDebug("   F2 - 加载参数数据");
            LogDebug("   F3 - 测试场景切换（参数传输）");
            LogDebug("   F5 - 系统诊断");
            
            InitializeComponents();
            
            if (autoStartTest)
            {
                StartCoroutine(AutoTestSequence());
            }
        }
        
        private void InitializeComponents()
        {
            unifiedManager = VRM10UnifiedManager.Instance;
            if (unifiedManager == null)
            {
                unifiedManager = FindObjectOfType<VRM10UnifiedManager>();
            }
            
            dataManager = CharacterDataManager.Instance;
            sceneTransitionManager = FindObjectOfType<SceneTransitionManager>();
            
            LogDebug($"✅ 组件初始化完成:");
            LogDebug($"   UnifiedManager: {unifiedManager != null}");
            LogDebug($"   DataManager: {dataManager != null}");
            LogDebug($"   SceneTransitionManager: {sceneTransitionManager != null}");
        }
        
        private System.Collections.IEnumerator AutoTestSequence()
        {
            yield return new WaitForSeconds(1f);
            LogDebug("🤖 开始自动测试序列...");
            
            // 1. 系统诊断
            LogDebug("🔍 第1步: 系统诊断");
            RunSystemDiagnostic();
            yield return new WaitForSeconds(2f);
            
            // 2. 修改参数
            LogDebug("🎭 第2步: 修改参数");
            TestModifyParameters();
            yield return new WaitForSeconds(2f);
            
            // 3. 保存参数
            LogDebug("💾 第3步: 保存参数");
            TestSaveParameters();
            yield return new WaitForSeconds(2f);
            
            // 4. 加载参数验证
            LogDebug("📥 第4步: 加载参数验证");
            TestLoadParameters();
            yield return new WaitForSeconds(2f);
            
            LogDebug("🎉 自动测试序列完成！");
            LogDebug("💡 按F3进行场景切换测试");
        }
        
        void Update()
        {
            if (Input.GetKeyDown(modifyParametersKey))
            {
                TestModifyParameters();
            }
            
            if (Input.GetKeyDown(saveParametersKey))
            {
                TestSaveParameters();
            }
            
            if (Input.GetKeyDown(loadParametersKey))
            {
                TestLoadParameters();
            }
            
            if (Input.GetKeyDown(sceneTransitionKey))
            {
                TestSceneTransition();
            }
            
            if (Input.GetKeyDown(systemDiagnosticKey))
            {
                RunSystemDiagnostic();
            }
            
            UpdateUI();
        }
        
        /// <summary>
        /// 测试修改参数
        /// </summary>
        private void TestModifyParameters()
        {
            LogDebug("🎭 [测试] 开始修改面部和服装参数...");
            
            if (unifiedManager != null)
            {
                // 修改面部表情
                unifiedManager.SetFaceExpression("happy", Random.Range(0.3f, 1.0f));
                unifiedManager.SetFaceExpression("surprised", Random.Range(0.2f, 0.8f));
                unifiedManager.SetFaceExpression("blink", Random.Range(0.1f, 0.6f));
                
                LogDebug("✅ 面部参数修改完成");
                
                // TODO: 修改服装（如果有可用的服装）
                LogDebug("⚠️ 服装修改功能待实现（需要服装库）");
            }
            else
            {
                LogDebug("❌ VRM10UnifiedManager不存在");
            }
        }
        
        /// <summary>
        /// 测试保存参数
        /// </summary>
        private void TestSaveParameters()
        {
            LogDebug("💾 [测试] 开始保存参数数据...");
            
            if (unifiedManager != null)
            {
                var characterData = unifiedManager.GetCurrentCharacterData();
                if (characterData != null)
                {
                    characterData.characterName = "ParameterTest_" + System.DateTime.Now.ToString("HHmm");
                    
                    LogDebug($"📊 收集到的参数数据:");
                    LogDebug($"   🎭 面部参数: {(characterData.facialData != null ? characterData.facialData.GetTotalParameterCount() : 0)}个");
                    LogDebug($"   👔 服装数据: {(characterData.clothingData != null ? characterData.clothingData.currentOutfit.Count : 0)}件");
                    
                    // 异步保存
                    StartCoroutine(SaveParametersCoroutine(characterData));
                }
                else
                {
                    LogDebug("❌ 无法获取角色数据");
                }
            }
            else
            {
                LogDebug("❌ VRM10UnifiedManager不存在");
            }
        }
        
        private System.Collections.IEnumerator SaveParametersCoroutine(CharacterData data)
        {
            if (dataManager != null)
            {
                var saveTask = dataManager.SaveCharacterAsync(data, "TestCharacter");
                
                while (!saveTask.IsCompleted)
                {
                    yield return null;
                }
                
                if (!saveTask.IsFaulted && !string.IsNullOrEmpty(saveTask.Result))
                {
                    LogDebug($"✅ 参数保存成功: {saveTask.Result}");
                }
                else
                {
                    LogDebug($"❌ 参数保存失败: {saveTask.Exception?.Message}");
                }
            }
            else
            {
                LogDebug("❌ CharacterDataManager不存在");
            }
        }
        
        /// <summary>
        /// 测试加载参数
        /// </summary>
        private void TestLoadParameters()
        {
            LogDebug("📥 [测试] 开始加载参数数据...");
            
            if (dataManager != null)
            {
                StartCoroutine(LoadParametersCoroutine());
            }
            else
            {
                LogDebug("❌ CharacterDataManager不存在");
            }
        }
        
        private System.Collections.IEnumerator LoadParametersCoroutine()
        {
            var loadTask = dataManager.LoadCharacterAsync("TestCharacter", true);
            
            while (!loadTask.IsCompleted)
            {
                yield return null;
            }
            
            if (!loadTask.IsFaulted && loadTask.Result != null)
            {
                var characterData = loadTask.Result;
                LogDebug($"✅ 参数加载成功: {characterData.characterName}");
                LogDebug($"📊 加载的数据:");
                LogDebug($"   🎭 面部参数: {(characterData.facialData != null ? characterData.facialData.GetTotalParameterCount() : 0)}个");
                LogDebug($"   👔 服装数据: {(characterData.clothingData != null ? characterData.clothingData.currentOutfit.Count : 0)}件");
            }
            else
            {
                LogDebug($"❌ 参数加载失败: {loadTask.Exception?.Message}");
            }
        }
        
        /// <summary>
        /// 测试场景切换
        /// </summary>
        private void TestSceneTransition()
        {
            LogDebug("🔄 [测试] 开始场景切换测试（参数传输方案）...");
            
            if (sceneTransitionManager != null)
            {
                LogDebug("🎯 使用新方案：只传输参数数据，无VRM文件传输");
                _ = sceneTransitionManager.TransitionToThirdPersonScene();
            }
            else
            {
                LogDebug("❌ SceneTransitionManager不存在");
            }
        }
        
        /// <summary>
        /// 系统诊断
        /// </summary>
        private void RunSystemDiagnostic()
        {
            LogDebug("🔧 [诊断] 运行系统诊断...");
            LogDebug("==============================");
            
            // 检查核心组件
            LogDebug($"🎭 VRM10UnifiedManager: {(unifiedManager != null ? "✅" : "❌")}");
            LogDebug($"💾 CharacterDataManager: {(dataManager != null ? "✅" : "❌")}");
            LogDebug($"🔄 SceneTransitionManager: {(sceneTransitionManager != null ? "✅" : "❌")}");
            
            // 检查VRM组件
            if (unifiedManager != null)
            {
                var currentCharacter = GameObject.Find("LoadedCharacter");
                LogDebug($"👤 LoadedCharacter对象: {(currentCharacter != null ? "✅" : "❌")}");
                
                if (currentCharacter != null)
                {
                    var vrmInstance = currentCharacter.GetComponent<UniVRM10.Vrm10Instance>();
                    var faceController = currentCharacter.GetComponent<VRM10FaceController>();
                    var clothBinder = currentCharacter.GetComponent<VRM10ClothBinder>();
                    
                    LogDebug($"🎯 Vrm10Instance: {(vrmInstance != null ? "✅" : "❌")}");
                    LogDebug($"😊 VRM10FaceController: {(faceController != null ? "✅" : "❌")}");
                    LogDebug($"👔 VRM10ClothBinder: {(clothBinder != null ? "✅" : "❌")}");
                }
            }
            
            LogDebug("==============================");
            LogDebug("🎯 新方案诊断完成！");
        }
        
        /// <summary>
        /// 更新UI显示
        /// </summary>
        private void UpdateUI()
        {
            if (statusText != null)
            {
                statusText.text = $"参数传输方案测试器\n系统状态: {(unifiedManager != null ? "正常" : "异常")}";
            }
            
            if (parameterCountText != null && unifiedManager != null)
            {
                var characterData = unifiedManager.GetCurrentCharacterData();
                int paramCount = characterData?.facialData?.GetTotalParameterCount() ?? 0;
                parameterCountText.text = $"面部参数: {paramCount}个";
            }
            
            if (clothingCountText != null && unifiedManager != null)
            {
                var characterData = unifiedManager.GetCurrentCharacterData();
                int clothCount = characterData?.clothingData?.currentOutfit.Count ?? 0;
                clothingCountText.text = $"服装数据: {clothCount}件";
            }
        }
        
        private void OnGUI()
        {
            if (!detailedLogging) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("🎯 VRM参数传输方案", GUI.skin.box);
            GUILayout.Label($"系统状态: {(unifiedManager != null ? "✅ 正常" : "❌ 异常")}");
            
            if (unifiedManager != null)
            {
                var characterData = unifiedManager.GetCurrentCharacterData();
                if (characterData != null)
                {
                    GUILayout.Label($"面部参数: {characterData.facialData?.GetTotalParameterCount() ?? 0}个");
                    GUILayout.Label($"服装数据: {characterData.clothingData?.currentOutfit.Count ?? 0}件");
                }
            }
            
            GUILayout.Label("快捷键:");
            GUILayout.Label("F4=修改 F1=保存 F2=加载 F3=切换");
            GUILayout.EndArea();
        }
        
        private void LogDebug(string message)
        {
            if (detailedLogging)
            {
                Debug.Log($"[VRMParameterTester] {message}");
            }
        }
    }
} 