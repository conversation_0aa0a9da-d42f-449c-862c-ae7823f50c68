%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.31854683, g: 0.31854683, b: 0.31854683, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 64
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000000, guid: 3ccf65945e77dba40a511fb09537db7d, type: 2}
  m_LightingSettings: {fileID: 4890085278179872738, guid: ca83447e7b6a7f045a2a70058696d11f, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &43617409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 43617412}
  - component: {fileID: 43617411}
  - component: {fileID: 43617413}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &43617411
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 43617409}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &43617412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 43617409}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 957318273}
  - {fileID: 1350887288}
  m_Father: {fileID: 1293717601}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &43617413
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 43617409}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01614664b831546d2ae94a42149d80ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_MoveRepeatDelay: 0.5
  m_MoveRepeatRate: 0.1
  m_XRTrackingOrigin: {fileID: 0}
  m_ActionsAsset: {fileID: -944628639613478452, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_PointAction: {fileID: -1654692200621890270, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MoveAction: {fileID: -8784545083839296357, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_SubmitAction: {fileID: 392368643174621059, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_CancelAction: {fileID: 7727032971491509709, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_LeftClickAction: {fileID: 3001919216989983466, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_MiddleClickAction: {fileID: -2185481485913320682, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_RightClickAction: {fileID: -4090225696740746782, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_ScrollWheelAction: {fileID: 6240969308177333660, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDevicePositionAction: {fileID: 6564999863303420839, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_TrackedDeviceOrientationAction: {fileID: 7970375526676320489, guid: ca9f5fa95ffab41fb9a615ab714db018, type: 3}
  m_DeselectOnBackgroundClick: 1
  m_PointerBehavior: 0
  m_CursorLockBehavior: 0
--- !u!1 &69970482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 69970483}
  - component: {fileID: 69970486}
  - component: {fileID: 69970485}
  - component: {fileID: 69970484}
  - component: {fileID: 69970487}
  m_Layer: 0
  m_Name: SaveAndHandoffButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &69970483
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69970482}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1870622769}
  m_Father: {fileID: 1701728037}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -20, y: 20}
  m_SizeDelta: {x: 327.6327, y: 97.8851}
  m_Pivot: {x: 1, y: 0}
--- !u!114 &69970484
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69970482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 0.2, g: 0.6, b: 1, a: 0.8}
    m_HighlightedColor: {r: 0.3, g: 0.7, b: 1, a: 1}
    m_PressedColor: {r: 0.1, g: 0.5, b: 0.9, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 69970485}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &69970485
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69970482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.2, g: 0.6, b: 1, a: 0.8}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &69970486
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69970482}
  m_CullTransparentMesh: 1
--- !u!114 &69970487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69970482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 29fcc3803558ccf47a7bd27e31f11367, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  targetVRMObject: {fileID: 0}
  autoFindVRM: 1
  targetSceneName: ThirdPersonTestScene
  saveBeforeTransition: 1
  buttonText: {fileID: 0}
  statusText: {fileID: 0}
  defaultButtonText: "\u8FDB\u5165\u6D4B\u8BD5\u573A\u666F"
  processingButtonText: "\u5904\u7406\u4E2D..."
  savingText: "\u4FDD\u5B58\u6570\u636E\u4E2D..."
  transitioningText: "\u5207\u6362\u573A\u666F\u4E2D..."
  statusDisplayTime: 2
  debugMode: 1
--- !u!21 &74198076
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_Hair_00_HAIR (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 630021176738988104, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: -1766637601443850223, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: -6196267951596334405, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 7018667825719044861, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: -6196267951596334405, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 1
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 1
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 1
    - _M_CullMode: 0
    - _M_DebugMode: 0
    - _M_DstBlend: 0
    - _M_EditMode: 1
    - _M_SrcBlend: 1
    - _M_ZWrite: 1
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: -0.19999999
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.8
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0.85882354, g: 0.5411765, b: 0.23137255, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.2745097, g: 0.090195976, b: 0.12549007, a: 1}
    - _RimColor: {r: 0.24999997, g: 0.24999997, b: 0.24999997, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!1 &95795086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 95795087}
  m_Layer: 0
  m_Name: _look_at_origin_
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &95795087
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 95795086}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.060000062, z: -0.00000009685755}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1554759956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &151454155
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 151454158}
  - component: {fileID: 151454157}
  - component: {fileID: 151454156}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &151454156
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 151454155}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &151454157
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 151454155}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37ad00496249b8b4dbf377199d92907d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &151454158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 151454155}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: 0.7071068, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 10, y: 5, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2001617010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!1 &249904989
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 249904991}
  - component: {fileID: 249904990}
  m_Layer: 0
  m_Name: KeyLight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &249904990
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249904989}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &249904991
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249904989}
  serializedVersion: 2
  m_LocalRotation: {x: 0.39044836, y: -0.346829, z: 0.16172901, w: 0.83731925}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1103011776}
  m_LocalEulerAnglesHint: {x: 50, y: -45, z: 0}
--- !u!21 &263500268
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_Body_00_SKIN (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_OUTLINE_WORLD
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -3368437746838795792, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 8871107045552810907, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 7018667825719044861, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: 8871107045552810907, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 1
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 0
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 1
    - _M_CullMode: 2
    - _M_DebugMode: 0
    - _M_DstBlend: 0
    - _M_EditMode: 1
    - _M_SrcBlend: 1
    - _M_ZWrite: 1
    - _OutlineLightingMix: 0
    - _OutlineWidth: 0.0008
    - _OutlineWidthMode: 1
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: -0.050000012
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.95
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0.24999997, g: 0.24999997, b: 0.24999997, a: 1}
    - _ShadeColor: {r: 0.9686274, g: 0.8117647, b: 0.85882354, a: 1}
  m_BuildTextureStacks: []
--- !u!21 &288074121
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_EyeHighlight_00_EYE (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHABLEND_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2999
  stringTagMap:
    RenderType: Transparent
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -1725931817652012797, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: -4407734190278982866, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: -4407734190278982866, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 2
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 1
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 0
    - _M_CullMode: 0
    - _M_DebugMode: 0
    - _M_DstBlend: 10
    - _M_EditMode: 1
    - _M_SrcBlend: 5
    - _M_ZWrite: 0
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: -1
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: 0.71
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.90999997
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!1 &304733822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 304733823}
  - component: {fileID: 304733827}
  - component: {fileID: 304733826}
  - component: {fileID: 304733825}
  - component: {fileID: 304733824}
  m_Layer: 0
  m_Name: "\u573A\u666F\u7BA1\u7406\u5668"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &304733823
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 304733822}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1267214956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &304733824
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 304733822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df663aa4b92c64b4b93dab057c84ce96, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debugMode: 1
  autoSaveVRMData: 1
  showLoadingProgress: 1
  thirdPersonSceneName: ThirdPersonTestScene
  avatarRuntimeSceneName: AvatarRuntime
--- !u!114 &304733825
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 304733822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e6d0be5e3bd161348b4e977047f1dead, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debugMode: 1
  autoSaveOnStateChange: 0
--- !u!114 &304733826
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 304733822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0131ecb72bdc2da4baaf7c47cddc1749, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  currentVRMModel: {fileID: 0}
  currentVrmInstance: {fileID: 0}
  vrmAssetPath: 
  debugMode: 1
--- !u!114 &304733827
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 304733822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a51cc2bde3fa224449cde25ea2ede450, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &379322198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 379322202}
  - component: {fileID: 379322201}
  - component: {fileID: 379322200}
  - component: {fileID: 379322199}
  m_Layer: 0
  m_Name: BackWall
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &379322199
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 379322198}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &379322200
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 379322198}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37ad00496249b8b4dbf377199d92907d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &379322201
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 379322198}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &379322202
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 379322198}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 2.5, z: 5}
  m_LocalScale: {x: 10, y: 5, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2001617010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &454862109 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7831338684674589143, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 654241383}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &654241383
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1548566905}
    m_Modifications:
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 0.79177165
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: -0.00501401
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.69447684
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.79177165
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.13295266
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 263500268}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[1]
      value: 
      objectReference: {fileID: 1113458742}
    - target: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Name
      value: "xa\u521D\u59CB\u6A21\u578B"
      objectReference: {fileID: 0}
    - target: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.x
      value: 0.00012496486
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 1.4568676
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: -0.035169587
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.123292
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.1347903
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.13434343
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 74198076}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.x
      value: 0.000000044703484
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 1.4541494
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: 0.016835487
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.10882426
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.11667144
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.0724268
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 881371293}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[1]
      value: 
      objectReference: {fileID: 980169203}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[2]
      value: 
      objectReference: {fileID: 288074121}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[3]
      value: 
      objectReference: {fileID: 1708565405}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[4]
      value: 
      objectReference: {fileID: 1812745431}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[5]
      value: 
      objectReference: {fileID: 1184738873}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Materials.Array.data[6]
      value: 
      objectReference: {fileID: 1949308721}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.size
      value: 57
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[0]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[1]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[5]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[6]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[10]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[11]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[12]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[13]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[14]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[15]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[16]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[17]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[18]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[19]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[20]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[21]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[22]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[23]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[24]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[28]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[31]
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_BlendShapeWeights.Array.data[35]
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: -9055416251393219258, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 95795087}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255093}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255092}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255091}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255090}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255100}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255099}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255098}
    - targetCorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683255097}
  m_SourcePrefab: {fileID: -3956505258223990651, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
--- !u!1 &683255088 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 654241383}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &683255089 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 654241383}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &683255090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3861f606e4aa66a469a0f9bad01f08cc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  faceController: {fileID: 0}
--- !u!114 &683255091
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 40b10d70351d3e4438e3be7a6a63f44f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  faceController: {fileID: 0}
  expression: Happy
  weight: 0
--- !u!114 &683255092
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f7d2adfd955af7145b342c75b97ab63a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  vrmInstance: {fileID: 683255094}
  facialParameters:
    standardExpressions: []
    customExpressions: []
    blendShapeParameters: []
    eyeParameters:
    - name: Fcl_EYE_Natural
      displayName: Fcl_EYE_Natural
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 11
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Angry
      displayName: Fcl_EYE_Angry
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 12
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Close
      displayName: Fcl_EYE_Close
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 13
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Close_R
      displayName: Fcl_EYE_Close_R
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 14
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Close_L
      displayName: Fcl_EYE_Close_L
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 15
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Fun
      displayName: Fcl_EYE_Fun
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 16
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Joy
      displayName: Fcl_EYE_Joy
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 17
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Joy_R
      displayName: Fcl_EYE_Joy_R
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 18
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Joy_L
      displayName: Fcl_EYE_Joy_L
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 19
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Sorrow
      displayName: Fcl_EYE_Sorrow
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 20
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Surprised
      displayName: Fcl_EYE_Surprised
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 21
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Spread
      displayName: Fcl_EYE_Spread
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 22
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Iris_Hide
      displayName: Fcl_EYE_Iris_Hide
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 23
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_EYE_Highlight_Hide
      displayName: Fcl_EYE_Highlight_Hide
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Eye
      blendShapeIndex: 24
      targetRenderer: {fileID: 1784443372}
    mouthParameters: []
    browParameters: []
    noseParameters: []
    cheekParameters: []
    faceShapeParameters: []
    expressionParameters:
    - name: Fcl_ALL_Neutral
      displayName: Fcl_ALL_Neutral
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 0
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_ALL_Angry
      displayName: Fcl_ALL_Angry
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 1
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_ALL_Surprised
      displayName: Fcl_ALL_Surprised
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 5
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_BRW_Angry
      displayName: Fcl_BRW_Angry
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 6
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_BRW_Surprised
      displayName: Fcl_BRW_Surprised
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 10
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_MTH_Angry
      displayName: Fcl_MTH_Angry
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 28
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_MTH_Neutral
      displayName: Fcl_MTH_Neutral
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 31
      targetRenderer: {fileID: 1784443372}
    - name: Fcl_MTH_Surprised
      displayName: Fcl_MTH_Surprised
      currentValue: 0
      defaultValue: 0
      minValue: 0
      maxValue: 100
      category: Expression
      blendShapeIndex: 35
      targetRenderer: {fileID: 1784443372}
    otherParameters: []
  autoInitializeOnStart: 1
  enableInspectorRealtime: 1
  debugMode: 0
  showDetailedLogs: 0
--- !u!114 &683255093
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b93d6bc3686832342b1220f68b4da34e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  analyzeOnStart: 0
  showDetailedInfo: 1
  exportToFile: 0
  analysisResult:
    modelName: "xa\u521D\u59CB\u6A21\u578B"
    modelTitle: "xa\u521D\u59CB\u6A21\u578B"
    modelAuthor: beitang
    vrmVersion: 1.0
    vrmInstanceName: "xa\u521D\u59CB\u6A21\u578B"
    hasVrmInstance: 1
    hasVrmExpression: 1
    hasVrmRuntime: 1
    standardExpressions:
    - name: happy
      preset: 1
      isStandard: 1
      description: "\u5FEB\u4E50\u8868\u60C5"
    - name: angry
      preset: 2
      isStandard: 1
      description: "\u6124\u6012\u8868\u60C5"
    - name: sad
      preset: 3
      isStandard: 1
      description: "\u60B2\u4F24\u8868\u60C5"
    - name: relaxed
      preset: 4
      isStandard: 1
      description: "\u653E\u677E\u8868\u60C5"
    - name: surprised
      preset: 5
      isStandard: 1
      description: "\u60CA\u8BB6\u8868\u60C5"
    - name: aa
      preset: 6
      isStandard: 1
      description: "A\u97F3\u53E3\u578B"
    - name: ih
      preset: 7
      isStandard: 1
      description: "I\u97F3\u53E3\u578B"
    - name: ou
      preset: 8
      isStandard: 1
      description: "U\u97F3\u53E3\u578B"
    - name: ee
      preset: 9
      isStandard: 1
      description: "E\u97F3\u53E3\u578B"
    - name: oh
      preset: 10
      isStandard: 1
      description: "O\u97F3\u53E3\u578B"
    - name: blink
      preset: 11
      isStandard: 1
      description: "\u7728\u773C"
    - name: blinkLeft
      preset: 12
      isStandard: 1
      description: "\u5DE6\u773C\u7728\u773C"
    - name: blinkRight
      preset: 13
      isStandard: 1
      description: "\u53F3\u773C\u7728\u773C"
    - name: lookUp
      preset: 14
      isStandard: 1
      description: "\u5411\u4E0A\u770B"
    - name: lookDown
      preset: 15
      isStandard: 1
      description: "\u5411\u4E0B\u770B"
    - name: lookLeft
      preset: 16
      isStandard: 1
      description: "\u5411\u5DE6\u770B"
    - name: lookRight
      preset: 17
      isStandard: 1
      description: "\u5411\u53F3\u770B"
    - name: neutral
      preset: 18
      isStandard: 1
      description: "\u4E2D\u6027\u8868\u60C5"
    customExpressions: []
    blendShapes:
    - name: Fcl_ALL_Neutral
      index: 0
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_ALL_Angry
      index: 1
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_ALL_Fun
      index: 2
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_ALL_Joy
      index: 3
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_ALL_Sorrow
      index: 4
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_ALL_Surprised
      index: 5
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_BRW_Angry
      index: 6
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_BRW_Fun
      index: 7
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_BRW_Joy
      index: 8
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_BRW_Sorrow
      index: 9
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_BRW_Surprised
      index: 10
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Natural
      index: 11
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Angry
      index: 12
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Close
      index: 13
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Close_R
      index: 14
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Close_L
      index: 15
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Fun
      index: 16
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Joy
      index: 17
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Joy_R
      index: 18
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Joy_L
      index: 19
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Sorrow
      index: 20
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Surprised
      index: 21
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Spread
      index: 22
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Iris_Hide
      index: 23
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_EYE_Highlight_Hide
      index: 24
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Eye
      isFacial: 1
      currentWeight: 0
    - name: Fcl_MTH_Close
      index: 25
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Up
      index: 26
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Down
      index: 27
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Angry
      index: 28
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_MTH_Small
      index: 29
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Large
      index: 30
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Neutral
      index: 31
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_MTH_Fun
      index: 32
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Joy
      index: 33
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Sorrow
      index: 34
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_Surprised
      index: 35
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Expression
      isFacial: 1
      currentWeight: 0
    - name: Fcl_MTH_SkinFung
      index: 36
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_SkinFung_R
      index: 37
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_SkinFung_L
      index: 38
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_A
      index: 39
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_I
      index: 40
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_U
      index: 41
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_E
      index: 42
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_MTH_O
      index: 43
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Hide
      index: 44
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung1
      index: 45
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung1_Low
      index: 46
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung1_Up
      index: 47
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung2
      index: 48
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung2_Low
      index: 49
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung2_Up
      index: 50
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung3
      index: 51
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung3_Up
      index: 52
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Fung3_Low
      index: 53
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Short
      index: 54
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Short_Up
      index: 55
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
    - name: Fcl_HA_Short_Low
      index: 56
      meshName: Face (merged)(Clone).baked
      rendererName: Face
      category: Other
      isFacial: 0
      currentWeight: 0
--- !u!114 &683255094 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: -4297446187249348513, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 654241383}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfba4ccd3f854e64f868ce83553071a9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &683255097
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90afcea9d92a25640ae8da0481cbec01, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &683255098
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b4fecd0777d4394ca1504bf94b5fbff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  faceController: {fileID: 0}
--- !u!114 &683255099
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97ddb992f53b8cd49b513c1e2627e654, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  vrmInstance: {fileID: 683255094}
  faceController: {fileID: 683255092}
  clothBinder: {fileID: 683255100}
  autoInitialize: 1
  debugMode: 1
  availableClothes:
  - {fileID: 2265601179189243413, guid: fd29a0825ad2b94429a65a60a85074a0, type: 3}
  - {fileID: 7236699369579511393, guid: 8a66815c62396fb46ade5ffbc6c980ac, type: 3}
  - {fileID: 1416415538602506335, guid: dc06ed6449624fa44b0f182403987e60, type: 3}
  - {fileID: 2265601179189243413, guid: 73b6179caca9d0148b92700c6b0bbdff, type: 3}
  outfitPresets: []
  clothLibraryPath: Assets/Prefabs/clorh model
  autoScanOnStart: 1
  useFilePrefix: 1
  topPrefix: Top_
  bottomPrefix: Bottom_
  dressPrefix: Dress_
  shoesPrefix: Shoes_
  accessoryPrefix: Acc_
  hatPrefix: Hat_
  glovesPrefix: Gloves_
  socksPrefix: Socks_
--- !u!114 &683255100
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683255088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 532e33e5b5ded134796d5e461e49d720, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debugMode: 1
  autoUpdateBounds: 1
  preserveSpringBones: 1
  strictBoneMapping: 0
  enableVRoidMode: 1
  createMissingBones: 1
  usePhysicsBonesAsStatic: 1
  minimumSuccessRate: 0.6
  delayedBoneBinding: 1
  bindingDelay: 0.1
  forceSyncBinding: 1
  preInitializeBones: 1
  stabilizationFrames: 3
  vrmInstance: {fileID: 683255094}
  avatarRoot: {fileID: 454862109}
  wornClothes: []
--- !u!1 &721795801
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 721795802}
  - component: {fileID: 721795804}
  - component: {fileID: 721795803}
  m_Layer: 0
  m_Name: MainCamera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &721795802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721795801}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.8, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1860899006}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!81 &721795803
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721795801}
  m_Enabled: 1
--- !u!20 &721795804
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 721795801}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 1, g: 1, b: 1, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 30
  orthographic: 0
  orthographic size: 5
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!1 &722846221
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 722846222}
  - component: {fileID: 722846225}
  - component: {fileID: 722846224}
  - component: {fileID: 722846223}
  m_Layer: 0
  m_Name: BlobShadowQuad
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &722846222
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722846221}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0.01, z: 4}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1548566905}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!64 &722846223
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722846221}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &722846224
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722846221}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: dda92c377996ed54aa0903a8e8981891, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &722846225
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722846221}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &816178385
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 816178387}
  - component: {fileID: 816178386}
  m_Layer: 0
  m_Name: RimLight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &816178386
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 816178385}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 0
  m_Shape: 0
  m_Color: {r: 0.9098039, g: 0.9411765, b: 1, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &816178387
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 816178385}
  serializedVersion: 2
  m_LocalRotation: {x: 0.16172901, y: 0.83731925, z: -0.39044836, w: 0.346829}
  m_LocalPosition: {x: 4, y: 3, z: -4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1103011776}
  m_LocalEulerAnglesHint: {x: 50, y: 135, z: 0}
--- !u!1 &868540835
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 868540836}
  - component: {fileID: 868540839}
  - component: {fileID: 868540838}
  - component: {fileID: 868540837}
  m_Layer: 0
  m_Name: "\u6570\u636E\u7BA1\u7406\u5668"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &868540836
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 868540835}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1267214956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &868540837
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 868540835}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 44bdcc7f76cfa5649b4ae399680f1ddf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  autoDetectOnStart: 1
  debugMode: 1
  vrmFilesDirectory: Assets/Prefabs/model/
  hasDetectedVRM: 0
  detectedVRMName: 
  detectedFilePath: 
--- !u!114 &868540838
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 868540835}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a481df145419fe24395e98c676bee7cf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  currentVRMInfo:
    modelName: 
    assetPath: 
    prefabReference: {fileID: 0}
  currentVRMInstance: {fileID: 0}
  registeredVRMs: []
  debugMode: 1
--- !u!114 &868540839
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 868540835}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 444c54b3243aa7e4092f2df54f4def99, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!21 &881371293
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_FaceMouth_00_FACE (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -1725931817652012797, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2808317123517323342, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: 2808317123517323342, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 1
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 0
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 1
    - _M_CullMode: 2
    - _M_DebugMode: 0
    - _M_DstBlend: 0
    - _M_EditMode: 1
    - _M_SrcBlend: 1
    - _M_ZWrite: 1
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: 0.71
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.90999997
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadeColor: {r: 0.9686274, g: 0.8117647, b: 0.85882354, a: 1}
  m_BuildTextureStacks: []
--- !u!1 &952762896
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 952762897}
  - component: {fileID: 952762899}
  - component: {fileID: 952762898}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &952762897
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 952762896}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1110619707}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &952762898
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 952762896}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: "\u4FDD\u5B58"
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 14a089668549a394690b910b0173e055, type: 2}
  m_sharedMaterial: {fileID: -8558580079047687507, guid: 14a089668549a394690b910b0173e055, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 30
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 1
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &952762899
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 952762896}
  m_CullTransparentMesh: 1
--- !u!1 &957318272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 957318273}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &957318273
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957318272}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.29338023, y: 0.8057158, z: 0.13796759}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 43617412}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &962967549
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 962967551}
  - component: {fileID: 962967550}
  - component: {fileID: 962967552}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &962967550
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 962967549}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5742a0afba328fa419d5876942485a00, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debugMode: 1
  testSaveKey: 282
  testLoadKey: 283
  testSceneTransitionKey: 284
  modifyVRMKey: 285
  saveButton: {fileID: 0}
  loadButton: {fileID: 0}
  sceneTransitionButton: {fileID: 0}
  modifyVRMButton: {fileID: 0}
  clearStateButton: {fileID: 0}
  statusText: {fileID: 0}
  blendShapeCountText: {fileID: 0}
  materialPropsCountText: {fileID: 0}
  vrmFilePaths:
  - "Assets/Prefabs/model/xa\u521D\u59CB\u6A21\u578B.vrm"
  - "Assets/VRMModels/xa\u521D\u59CB\u6A21\u578B.vrm"
  - "Assets/Models/xa\u521D\u59CB\u6A21\u578B.vrm"
  - "Assets/Prefabs/clorh model/xa\u521D\u59CB\u6A21\u578B.vrm"
--- !u!4 &962967551
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 962967549}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.02840646, y: -0.66844904, z: 3.8280628}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &962967552
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 962967549}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3021945577793c458a183cd5524539c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  autoRunOnStart: 1
  detailedLogging: 1
  foundVRMFiles: 0
  foundVRMInstances: 0
  missingScripts: 0
  hasVRMStateManager: 0
  hasVRMRuntimeLoader: 0
--- !u!21 &980169203
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_EyeIris_00_EYE (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHABLEND_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2998
  stringTagMap:
    RenderType: Transparent
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -1725931817652012797, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: -4960794875819661355, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: -4960794875819661355, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 2
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 0
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 0
    - _M_CullMode: 2
    - _M_DebugMode: 0
    - _M_DstBlend: 10
    - _M_EditMode: 1
    - _M_SrcBlend: 5
    - _M_ZWrite: 0
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: -2
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: 0.71
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.90999997
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!1 &1103011775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1103011776}
  m_Layer: 0
  m_Name: Lighting
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1103011776
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1103011775}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 249904991}
  - {fileID: 1618045282}
  - {fileID: 816178387}
  m_Father: {fileID: 1906044724}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1110619706
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1110619707}
  - component: {fileID: 1110619710}
  - component: {fileID: 1110619709}
  - component: {fileID: 1110619708}
  - component: {fileID: 1110619711}
  m_Layer: 0
  m_Name: SaveButton (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1110619707
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1110619706}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 952762897}
  m_Father: {fileID: 1701728037}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -20, y: 132.4}
  m_SizeDelta: {x: 327.6327, y: 97.8851}
  m_Pivot: {x: 1, y: 0}
--- !u!114 &1110619708
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1110619706}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 0.2, g: 0.6, b: 1, a: 0.8}
    m_HighlightedColor: {r: 0.3, g: 0.7, b: 1, a: 1}
    m_PressedColor: {r: 0.1, g: 0.5, b: 0.9, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1110619709}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1110619709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1110619706}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.2, g: 0.6, b: 1, a: 0.8}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1110619710
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1110619706}
  m_CullTransparentMesh: 1
--- !u!114 &1110619711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1110619706}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6133be2580fe2c4479c8d0f113eafdb6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  saveButton: {fileID: 1110619708}
  debugMode: 1
  autoFindVRM: 1
  saveVRMFile: 1
  saveRenderState: 1
  vrmFilePath: "Assets/Prefabs/model/xa\u521D\u59CB\u6A21\u578B.vrm"
--- !u!21 &1113458742
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_HairBack_00_HAIR (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _MTOON_EMISSIVEMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -4923471021021475486, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2544008388570112, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: 2544008388570112, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 1
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 0
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 1
    - _M_CullMode: 2
    - _M_DebugMode: 0
    - _M_DstBlend: 0
    - _M_EditMode: 1
    - _M_SrcBlend: 1
    - _M_ZWrite: 1
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: -0.19999999
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.8
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0.85882354, g: 0.69411767, b: 0.69411767, a: 1}
    - _MatcapColor: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineColor: {r: 0.47450978, g: 0.74509805, b: 0.737255, a: 1}
    - _RimColor: {r: 0.24999997, g: 0.24999997, b: 0.24999997, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!21 &1184738873
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_FaceBrow_00_FACE (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHABLEND_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    RenderType: Transparent
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -1725931817652012797, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: -5386000052843801069, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 7018667825719044861, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: -5386000052843801069, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 2
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 1
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 0
    - _M_CullMode: 0
    - _M_DebugMode: 0
    - _M_DstBlend: 10
    - _M_EditMode: 1
    - _M_SrcBlend: 5
    - _M_ZWrite: 0
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: 0.71
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.90999997
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!1 &1267214955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1267214956}
  m_Layer: 0
  m_Name: "\u7BA1\u7406\u5668"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1267214956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1267214955}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.013630153, y: 1.2525692, z: 0.055883706}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 868540836}
  - {fileID: 304733823}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1293717600
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1293717601}
  m_Layer: 0
  m_Name: AvatarStage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1293717601
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1293717600}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1906044724}
  - {fileID: 1860899006}
  - {fileID: 43617412}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1350887287
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1350887288}
  m_Layer: 0
  m_Name: PostProcess
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1350887288
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1350887287}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.29338023, y: 0.8057158, z: 0.13796759}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 43617412}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1436555156
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1436555160}
  - component: {fileID: 1436555159}
  - component: {fileID: 1436555158}
  - component: {fileID: 1436555157}
  m_Layer: 0
  m_Name: floor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!64 &1436555157
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1436555156}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1436555158
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1436555156}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 37ad00496249b8b4dbf377199d92907d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1436555159
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1436555156}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1436555160
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1436555156}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 10, y: 1, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2001617010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1548566904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1548566905}
  - component: {fileID: 1548566909}
  - component: {fileID: 1548566908}
  - component: {fileID: 1548566907}
  - component: {fileID: 1548566906}
  m_Layer: 0
  m_Name: Turntable
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1548566905
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548566904}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -1.6, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 722846222}
  - {fileID: 2119359188}
  - {fileID: 683255089}
  m_Father: {fileID: 1860899006}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1548566906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548566904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b93d6bc3686832342b1220f68b4da34e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  analyzeOnStart: 0
  showDetailedInfo: 1
  exportToFile: 0
  analysisResult:
    modelName: 
    modelTitle: 
    modelAuthor: 
    vrmVersion: 
    vrmInstanceName: 
    hasVrmInstance: 0
    hasVrmExpression: 0
    hasVrmRuntime: 0
    standardExpressions: []
    customExpressions: []
    blendShapes: []
--- !u!114 &1548566907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548566904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 532e33e5b5ded134796d5e461e49d720, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debugMode: 1
  autoUpdateBounds: 1
  preserveSpringBones: 1
  strictBoneMapping: 0
  enableVRoidMode: 1
  createMissingBones: 1
  usePhysicsBonesAsStatic: 1
  minimumSuccessRate: 0.6
  delayedBoneBinding: 0
  bindingDelay: 0.1
  forceSyncBinding: 1
  preInitializeBones: 1
  stabilizationFrames: 3
  vrmInstance: {fileID: 2119359190}
  avatarRoot: {fileID: 0}
  wornClothes: []
--- !u!114 &1548566908
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548566904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f7d2adfd955af7145b342c75b97ab63a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  vrmInstance: {fileID: 2119359190}
  facialParameters:
    standardExpressions: []
    customExpressions: []
    blendShapeParameters: []
    eyeParameters: []
    mouthParameters: []
    browParameters: []
    noseParameters: []
    cheekParameters: []
    faceShapeParameters: []
    expressionParameters: []
    otherParameters: []
  autoInitializeOnStart: 1
  enableInspectorRealtime: 1
  debugMode: 0
  showDetailedLogs: 0
--- !u!114 &1548566909
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548566904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97ddb992f53b8cd49b513c1e2627e654, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  vrmInstance: {fileID: 2119359190}
  faceController: {fileID: 0}
  clothBinder: {fileID: 0}
  autoInitialize: 1
  debugMode: 1
  availableClothes: []
  outfitPresets: []
  clothLibraryPath: Assets/Prefabs/clorh model/ExtractedClothes/
  autoScanOnStart: 0
  useFilePrefix: 1
  topPrefix: Top_
  bottomPrefix: Bottom_
  dressPrefix: Dress_
  shoesPrefix: Shoes_
  accessoryPrefix: Acc_
  hatPrefix: Hat_
  glovesPrefix: Gloves_
  socksPrefix: Socks_
--- !u!4 &1554759956 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -9055416251393219258, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 654241383}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1618045280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1618045282}
  - component: {fileID: 1618045281}
  m_Layer: 0
  m_Name: FillLight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1618045281
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1618045280}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 0
  m_Shape: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 0.6
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &1618045282
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1618045280}
  serializedVersion: 2
  m_LocalRotation: {x: 0.39044836, y: 0.346829, z: -0.16172901, w: 0.83731925}
  m_LocalPosition: {x: -4, y: 3, z: -4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1103011776}
  m_LocalEulerAnglesHint: {x: 50, y: 45, z: 0}
--- !u!1 &1701728033
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1701728037}
  - component: {fileID: 1701728036}
  - component: {fileID: 1701728035}
  - component: {fileID: 1701728034}
  m_Layer: 0
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1701728034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1701728033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1701728035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1701728033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0.5
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1701728036
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1701728033}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 100
  m_TargetDisplay: 0
--- !u!224 &1701728037
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1701728033}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 69970483}
  - {fileID: 1110619707}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!21 &1708565405
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_Face_00_SKIN (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_OUTLINE_WORLD
  - _MTOON_PARAMETERMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 5416188534394320506, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 5134788550220458975, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 7018667825719044861, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: -7164542329613092374, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: 5134788550220458975, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 1
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 1
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 1
    - _M_CullMode: 0
    - _M_DebugMode: 0
    - _M_DstBlend: 0
    - _M_EditMode: 1
    - _M_SrcBlend: 1
    - _M_ZWrite: 1
    - _OutlineLightingMix: 0
    - _OutlineWidth: 0.0008
    - _OutlineWidthMode: 1
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: 0.71
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.90999997
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0.24999997, g: 0.24999997, b: 0.24999997, a: 1}
    - _ShadeColor: {r: 0.9686274, g: 0.8117647, b: 0.85882354, a: 1}
  m_BuildTextureStacks: []
--- !u!137 &1784443372 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 654241383}
  m_PrefabAsset: {fileID: 0}
--- !u!21 &1812745431
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_EyeWhite_00_EYE (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -1725931817652012797, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: -5496234832007829451, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: -5496234832007829451, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 1
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 0
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 1
    - _M_CullMode: 2
    - _M_DebugMode: 0
    - _M_DstBlend: 0
    - _M_EditMode: 1
    - _M_SrcBlend: 1
    - _M_ZWrite: 1
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: 0.71
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.90999997
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!1 &1860899005
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1860899006}
  m_Layer: 0
  m_Name: StageRig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1860899006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860899005}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.6, z: -4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 721795802}
  - {fileID: 1548566905}
  m_Father: {fileID: 1293717601}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1870622768
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1870622769}
  - component: {fileID: 1870622771}
  - component: {fileID: 1870622770}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1870622769
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1870622768}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 69970483}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1870622770
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1870622768}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: "\u5207\u6362\u5230\u7B2C\u4E09\u4EBA\u79F0"
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 14a089668549a394690b910b0173e055, type: 2}
  m_sharedMaterial: {fileID: -8558580079047687507, guid: 14a089668549a394690b910b0173e055, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 30
  m_fontSizeBase: 30
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 1
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1870622771
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1870622768}
  m_CullTransparentMesh: 1
--- !u!1 &1906044723
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1906044724}
  m_Layer: 0
  m_Name: Environment
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1906044724
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1906044723}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2001617010}
  - {fileID: 1103011776}
  m_Father: {fileID: 1293717601}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!21 &1949308721
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: N00_000_00_FaceEyeline_00_FACE (Instance)
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHABLEND_ON
  - _MTOON_EMISSIVEMAP
  - _MTOON_RIMMAP
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2998
  stringTagMap:
    RenderType: Transparent
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: -1725931817652012797, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 6937422537370298584, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: -7991923158731760435, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 7018667825719044861, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: -7991923158731760435, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaMode: 2
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DoubleSided: 1
    - _GiEqualization: 0.9
    - _M_AlphaToMask: 0
    - _M_CullMode: 0
    - _M_DebugMode: 0
    - _M_DstBlend: 10
    - _M_EditMode: 1
    - _M_SrcBlend: 5
    - _M_ZWrite: 0
    - _OutlineLightingMix: 1
    - _OutlineWidth: 0
    - _OutlineWidthMode: 0
    - _RenderQueueOffset: -2
    - _RimFresnelPower: 100
    - _RimLift: 0.1
    - _RimLightingMix: 1
    - _ShadingShiftFactor: 0.71
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.90999997
    - _TransparentWithZWrite: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollYSpeed: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0.27450976, g: 0.09019602, b: 0.12549013, a: 1}
    - _RimColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!1 &2001617009
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2001617010}
  m_Layer: 0
  m_Name: Geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2001617010
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001617009}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1436555160}
  - {fileID: 379322202}
  - {fileID: 151454158}
  m_Father: {fileID: 1906044724}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &2119359187
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1548566905}
    m_Modifications:
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 0.79177165
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: -0.00501401
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.69447684
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.79177165
      objectReference: {fileID: 0}
    - target: {fileID: -5694092435888646316, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.13295266
      objectReference: {fileID: 0}
    - target: {fileID: -1728013695380148634, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_Name
      value: "xa\u521D\u59CB\u6A21\u578B (1)"
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.x
      value: 0.00012496486
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 1.4568676
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: -0.035169587
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.123292
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.1347903
      objectReference: {fileID: 0}
    - target: {fileID: 2953604236389334553, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.13434343
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_DirtyAABB
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.x
      value: 0.000000044703484
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.y
      value: 1.4541494
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Center.z
      value: 0.016835487
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 0.10882426
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 0.11667144
      objectReference: {fileID: 0}
    - target: {fileID: 7140606858727328969, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 0.0724268
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: -3956505258223990651, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
--- !u!4 &2119359188 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5159078012444724336, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 2119359187}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2119359190 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: -4297446187249348513, guid: 2b5292d257b40fe44bdcb3dbd739229a, type: 3}
  m_PrefabInstance: {fileID: 2119359187}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfba4ccd3f854e64f868ce83553071a9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 1293717601}
  - {fileID: 1267214956}
  - {fileID: 1701728037}
  - {fileID: 962967551}
