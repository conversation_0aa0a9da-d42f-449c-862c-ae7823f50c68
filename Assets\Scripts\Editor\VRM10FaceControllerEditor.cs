#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using VRoidFaceCustomization;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// VRM10FaceController的自定义Inspector编辑器
    /// 提供VRM 1.0模型的面部控制界面
    /// </summary>
    [CustomEditor(typeof(VRM10FaceController))]
    public class VRM10FaceControllerEditor : UnityEditor.Editor
    {
        private VRM10FaceController faceController;
        private bool showStandardExpressions = true;
        private bool showCustomExpressions = true;
        private bool showBlendShapeParameters = true;
        private bool showEyeParameters = true;
        private bool showMouthParameters = true;
        private bool showBrowParameters = true;
        private bool showNoseParameters = true;
        private bool showCheekParameters = true;
        private bool showFaceShapeParameters = true;
        private bool showExpressionParameters = true;
        private bool showOtherParameters = false;
        
        private bool showControllerSettings = true;
        private bool showRuntimeInfo = true;
        
        private Vector2 scrollPosition;

        private void OnEnable()
        {
            faceController = (VRM10FaceController)target;
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("VRM 1.0 面部控制器", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 控制器设置
            DrawControllerSettings();
            
            // 运行时信息
            DrawRuntimeInfo();
            
            // 初始化按钮
            DrawInitializationControls();
            
            // VRM表情控制
            if (faceController.IsInitialized())
            {
                DrawVRMExpressionControls();
                DrawBlendShapeParametersControls();
            }
            else
            {
                EditorGUILayout.HelpBox("请先初始化面部控制器来显示参数控制", MessageType.Info);
            }
            
            serializedObject.ApplyModifiedProperties();
            
            // 如果有修改，标记为脏数据
            if (GUI.changed)
            {
                EditorUtility.SetDirty(target);
            }
        }
        
        private void DrawControllerSettings()
        {
            showControllerSettings = EditorGUILayout.Foldout(showControllerSettings, "控制器设置", true);
            if (showControllerSettings)
            {
                EditorGUI.indentLevel++;

                // 显示所有序列化字段
                var vrmInstanceProp = serializedObject.FindProperty("vrmInstance");
                if (vrmInstanceProp != null)
                {
                    EditorGUILayout.PropertyField(vrmInstanceProp);
                }

                var facialParametersProp = serializedObject.FindProperty("facialParameters");
                if (facialParametersProp != null)
                {
                    EditorGUILayout.PropertyField(facialParametersProp);
                }

                var autoInitProp = serializedObject.FindProperty("autoInitializeOnStart");
                if (autoInitProp != null)
                {
                    EditorGUILayout.PropertyField(autoInitProp);
                }

                var realtimeProp = serializedObject.FindProperty("enableInspectorRealtime");
                if (realtimeProp != null)
                {
                    EditorGUILayout.PropertyField(realtimeProp);
                }

                var debugProp = serializedObject.FindProperty("debugMode");
                if (debugProp != null)
                {
                    EditorGUILayout.PropertyField(debugProp);
                }

                var logsProp = serializedObject.FindProperty("showDetailedLogs");
                if (logsProp != null)
                {
                    EditorGUILayout.PropertyField(logsProp);
                }

                EditorGUI.indentLevel--;
            }
        }
        
        private void DrawRuntimeInfo()
        {
            showRuntimeInfo = EditorGUILayout.Foldout(showRuntimeInfo, "运行时信息", true);
            if (showRuntimeInfo)
            {
                EditorGUI.indentLevel++;
                
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.Toggle("已初始化", faceController.IsInitialized());

                // 显示当前模式信息
                string modeInfo = Application.isPlaying ? "Play模式 (完整功能)" : "编辑模式 (简化功能)";
                EditorGUILayout.TextField("当前模式", modeInfo);

                if (!Application.isPlaying && faceController.IsInitialized())
                {
                    EditorGUILayout.HelpBox("编辑模式下为避免材质警告，使用简化初始化。\n完整功能请在Play模式下使用。", MessageType.Info);
                }
                
                if (faceController.IsInitialized())
                {
                    var facialParams = faceController.GetFacialParameters();
                    if (facialParams != null)
                    {
                        EditorGUILayout.IntField("总参数数量", facialParams.GetTotalParameterCount());
                        EditorGUILayout.IntField("标准表情数量", facialParams.standardExpressions.Count);
                        EditorGUILayout.IntField("自定义表情数量", facialParams.customExpressions.Count);
                        
                        var stats = facialParams.GetCategoryStatistics();
                        foreach (var kvp in stats)
                        {
                            if (kvp.Value > 0)
                            {
                                EditorGUILayout.IntField($"{kvp.Key} 参数", kvp.Value);
                            }
                        }
                    }
                }
                
                EditorGUI.EndDisabledGroup();
                EditorGUI.indentLevel--;
            }
        }
        
        private void DrawInitializationControls()
        {
            EditorGUILayout.Space();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("初始化面部控制器", GUILayout.Height(30)))
            {
                faceController.InitializeFaceController();
            }

            // 在编辑模式下提供强制完整初始化的选项
            if (!Application.isPlaying && faceController.IsInitialized())
            {
                if (GUILayout.Button("强制完整初始化", GUILayout.Height(30)))
                {
                    EditorUtility.DisplayDialog("警告",
                        "强制完整初始化会触发材质警告，但可以获得完整功能。\n" +
                        "建议在Play模式下使用完整功能。", "确定");
                    faceController.ForceFullInitialization();
                }
            }
            
            if (faceController.IsInitialized())
            {
                if (GUILayout.Button("重置参数", GUILayout.Height(30)))
                {
                    faceController.ResetFacialParameters();
                }
                
                if (GUILayout.Button("随机化", GUILayout.Height(30)))
                {
                    faceController.RandomizeFacialParameters();
                }
            }
            
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space();
        }
        
        private void DrawVRMExpressionControls()
        {
            var facialParams = faceController.GetFacialParameters();
            if (facialParams == null) return;
            
            // 标准VRM表情
            showStandardExpressions = EditorGUILayout.Foldout(showStandardExpressions, 
                $"VRM标准表情 ({facialParams.standardExpressions.Count})", true);
            
            if (showStandardExpressions)
            {
                EditorGUI.indentLevel++;
                
                foreach (var expression in facialParams.standardExpressions)
                {
                    DrawFacialParameterAsExpression(expression);
                }
                
                EditorGUI.indentLevel--;
            }
            
            // 自定义表情
            if (facialParams.customExpressions.Count > 0)
            {
                showCustomExpressions = EditorGUILayout.Foldout(showCustomExpressions, 
                    $"自定义表情 ({facialParams.customExpressions.Count})", true);
                
                if (showCustomExpressions)
                {
                    EditorGUI.indentLevel++;
                    
                    foreach (var expression in facialParams.customExpressions)
                    {
                        DrawFacialParameterAsExpression(expression);
                    }
                    
                    EditorGUI.indentLevel--;
                }
            }
        }
        
        private void DrawVRMExpressionParameter(VRM10ExpressionParameter expression)
        {
            EditorGUILayout.BeginHorizontal();

            // 表情名称标签
            string displayName = string.IsNullOrEmpty(expression.displayName) ? expression.name : expression.displayName;
            EditorGUILayout.LabelField(displayName, GUILayout.Width(120));

            // 权重滑块
            EditorGUI.BeginChangeCheck();
            float newWeight = EditorGUILayout.Slider(expression.currentWeight, expression.minWeight, expression.maxWeight);

            if (EditorGUI.EndChangeCheck())
            {
                // 记录撤销操作
                Undo.RecordObject(faceController, "Change VRM Expression");

                // 更新表情权重
                expression.SetWeight(newWeight);

                // 立即应用到VRM模型
                if (Application.isPlaying)
                {
                    faceController.SetExpression(expression.name, newWeight);
                }

                // 标记为脏数据
                EditorUtility.SetDirty(faceController);
            }

            EditorGUILayout.EndHorizontal();
        }

        private void DrawFacialParameterAsExpression(FacialParameter parameter)
        {
            EditorGUILayout.BeginHorizontal();

            // 表情名称标签
            string displayName = string.IsNullOrEmpty(parameter.displayName) ? parameter.name : parameter.displayName;
            EditorGUILayout.LabelField(displayName, GUILayout.Width(120));

            // 权重滑块
            EditorGUI.BeginChangeCheck();
            float newValue = EditorGUILayout.Slider(parameter.currentValue, 0f, 1f);

            if (EditorGUI.EndChangeCheck())
            {
                // 记录撤销操作
                Undo.RecordObject(faceController, "Change Facial Parameter");

                // 更新参数值
                parameter.currentValue = newValue;

                // 立即应用到VRM模型
                if (Application.isPlaying && parameter.isExpression)
                {
                    faceController.SetExpression(parameter.name, newValue);
                }

                // 标记为脏数据
                EditorUtility.SetDirty(faceController);
            }

            EditorGUILayout.EndHorizontal();
        }
        
        private void DrawBlendShapeParametersControls()
        {
            var facialParams = faceController.GetFacialParameters();
            if (facialParams == null) return;
            
            showBlendShapeParameters = EditorGUILayout.Foldout(showBlendShapeParameters, "BlendShape参数", true);
            
            if (!showBlendShapeParameters) return;
            
            EditorGUI.indentLevel++;
            
            // 滚动区域
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            
            // 各分类参数
            DrawBlendShapeCategoryParameters("眼部参数", facialParams.eyeParameters, ref showEyeParameters);
            DrawBlendShapeCategoryParameters("嘴部参数", facialParams.mouthParameters, ref showMouthParameters);
            DrawBlendShapeCategoryParameters("眉毛参数", facialParams.browParameters, ref showBrowParameters);
            DrawBlendShapeCategoryParameters("鼻子参数", facialParams.noseParameters, ref showNoseParameters);
            DrawBlendShapeCategoryParameters("脸颊参数", facialParams.cheekParameters, ref showCheekParameters);
            DrawBlendShapeCategoryParameters("脸型参数", facialParams.faceShapeParameters, ref showFaceShapeParameters);
            DrawBlendShapeCategoryParameters("表情参数", facialParams.expressionParameters, ref showExpressionParameters);
            DrawBlendShapeCategoryParameters("其他参数", facialParams.otherParameters, ref showOtherParameters);
            
            EditorGUILayout.EndScrollView();
            
            EditorGUI.indentLevel--;
        }
        
        private void DrawBlendShapeCategoryParameters(string categoryName, 
            System.Collections.Generic.List<BlendShapeParameter> parameters, ref bool showCategory)
        {
            if (parameters.Count == 0) return;
            
            showCategory = EditorGUILayout.Foldout(showCategory, $"{categoryName} ({parameters.Count})", true);
            
            if (showCategory)
            {
                EditorGUI.indentLevel++;
                
                foreach (var param in parameters)
                {
                    DrawBlendShapeParameter(param);
                }
                
                EditorGUI.indentLevel--;
            }
        }
        
        private void DrawBlendShapeParameter(BlendShapeParameter param)
        {
            EditorGUILayout.BeginHorizontal();

            // 参数名称标签
            string displayName = string.IsNullOrEmpty(param.displayName) ? param.name : param.displayName;
            EditorGUILayout.LabelField(displayName, GUILayout.Width(150));

            // 滑块
            EditorGUI.BeginChangeCheck();
            float newValue = EditorGUILayout.Slider(param.currentValue, param.minValue, param.maxValue);

            if (EditorGUI.EndChangeCheck())
            {
                // 记录撤销操作
                Undo.RecordObject(faceController, "Change BlendShape Parameter");

                // 更新参数值
                param.currentValue = newValue;

                // 立即应用到模型（编辑器模式和运行时都应用）
                if (param.targetRenderer != null)
                {
                    param.targetRenderer.SetBlendShapeWeight(param.blendShapeIndex, newValue);
                }

                // 如果在运行时，也通过控制器设置
                if (Application.isPlaying)
                {
                    faceController.SetBlendShapeParameter(param.name, newValue);
                }

                // 标记为脏数据
                EditorUtility.SetDirty(faceController);

                // 强制重绘场景视图以显示变化
                SceneView.RepaintAll();
            }

            EditorGUILayout.EndHorizontal();
        }
    }
}
#endif
