using UnityEngine;
using UniVRM10;
using System.Collections.Generic;
using System.Linq;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM 1.0 统一管理器 - 系统核心控制器
    /// 
    /// <para>
    /// 这是整个VRM 1.0捏脸换装系统的核心管理器，采用单例模式设计。
    /// 它整合了面部表情控制(VRM10FaceController)和动态换装系统(VRM10ClothBinder)，
    /// 为开发者提供统一、简洁的API接口。
    /// </para>
    /// 
    /// <para><b>主要功能：</b></para>
    /// <list type="bullet">
    /// <item><description>🎭 面部表情控制 - 支持VRM 1.0标准表情参数</description></item>
    /// <item><description>👔 动态换装系统 - 实时服装切换和绑定</description></item>
    /// <item><description>📦 服装库管理 - 自动分类和索引服装资源</description></item>
    /// <item><description>🎨 套装预设 - 预定义的服装搭配组合</description></item>
    /// <item><description>💾 数据持久化 - 角色状态保存和恢复</description></item>
    /// <item><description>🌐 WebGL支持 - 网页端兼容接口</description></item>
    /// </list>
    /// 
    /// <para><b>使用示例：</b></para>
    /// <code>
    /// // 获取管理器实例
    /// var manager = VRM10UnifiedManager.Instance;
    /// 
    /// // 设置面部表情
    /// manager.SetFaceExpression("happy", 1.0f);
    /// manager.SetFaceExpression("blink", 0.5f);
    /// 
    /// // 动态换装
    /// manager.WearClothByName("BlueDress");
    /// manager.WearClothByType(VRM10ClothType.Shoes, "Sneakers");
    /// 
    /// // 套装管理
    /// manager.WearOutfitByName("CasualOutfit");
    /// manager.SaveCurrentAsOutfit("MyOutfit");
    /// 
    /// // 重置系统
    /// manager.ResetAllParameters();
    /// </code>
    /// 
    /// <para><b>注意事项：</b></para>
    /// <list type="number">
    /// <item><description>确保场景中有有效的VRM 1.0模型</description></item>
    /// <item><description>服装Prefab需要使用VRM10ClothExtractor提取</description></item>
    /// <item><description>推荐在Start()后调用系统功能</description></item>
    /// </list>
    /// 
    /// <para><b>版本：</b> 1.0.0</para>
    /// <para><b>兼容：</b> VRM 1.0, Unity 2022.3+, WebGL</para>
    /// </summary>
    public class VRM10UnifiedManager : MonoBehaviour
    {
        #region VRM 1.0 核心组件
        [Header("VRM 1.0 组件")]
        
        /// <summary>
        /// VRM 1.0实例组件引用
        /// <para>Unity官方VRM 1.0插件的核心组件，包含模型的元数据和配置信息</para>
        /// </summary>
        [SerializeField] private Vrm10Instance vrmInstance;
        
        /// <summary>
        /// VRM 1.0面部控制器
        /// <para>负责处理面部表情、BlendShape参数和表情动画的专用控制器</para>
        /// </summary>
        [SerializeField] private VRM10FaceController faceController;
        
        /// <summary>
        /// VRM 1.0服装绑定器
        /// <para>负责动态换装、服装绑定和冲突检测的核心组件</para>
        /// </summary>
        [SerializeField] private VRM10ClothBinder clothBinder;
        #endregion
        
        #region 系统管理设置
        [Header("管理设置")]
        
        /// <summary>
        /// 自动初始化标志
        /// <para>如果为true，系统将在Start()时自动初始化所有组件</para>
        /// </summary>
        [SerializeField] private bool autoInitialize = true;
        
        /// <summary>
        /// 调试模式开关
        /// <para>启用时会输出详细的调试日志，用于开发和故障排除</para>
        /// </summary>
        [SerializeField] private bool debugMode = true;
        #endregion
        
        #region 服装库管理
        [Header("服装库")]
        
        /// <summary>
        /// 可用服装列表
        /// <para>存储所有可用的服装Prefab引用，用于运行时换装</para>
        /// </summary>
        [SerializeField] private List<GameObject> availableClothes = new List<GameObject>();
        
        /// <summary>
        /// 套装预设列表
        /// <para>预定义的服装搭配组合，可以一键应用完整的着装风格</para>
        /// </summary>
        [SerializeField] private List<VRM10OutfitPreset> outfitPresets = new List<VRM10OutfitPreset>();

        [Header("自动服装库管理")]
        
        /// <summary>
        /// 服装库路径
        /// <para>指定服装Prefab资源的存储路径，支持自动扫描和加载</para>
        /// </summary>
        [SerializeField] private string clothLibraryPath = "Assets/Prefabs/clorh model/ExtractedClothes/";

        /// <summary>
        /// 服装库路径的只读属性
        /// <para>外部访问服装库路径的公开接口</para>
        /// </summary>
        public string ClothLibraryPath => clothLibraryPath;
        
        /// <summary>
        /// 启动时自动扫描服装库
        /// <para>如果启用，系统启动时会自动扫描指定路径下的所有服装Prefab</para>
        /// </summary>
        [SerializeField] private bool autoScanOnStart = false;
        
        /// <summary>
        /// 使用文件名前缀进行分类
        /// <para>启用后会根据文件名前缀自动分类服装类型（如Top_、Bottom_等）</para>
        /// </summary>
        [SerializeField] private bool useFilePrefix = true;
        #endregion

        #region 服装分类前缀规则
        [Header("前缀分类规则")]
        
        /// <summary>上衣类服装的文件名前缀</summary>
        [SerializeField] private string topPrefix = "Top_";
        
        /// <summary>下装类服装的文件名前缀</summary>
        [SerializeField] private string bottomPrefix = "Bottom_";
        
        /// <summary>连衣裙类服装的文件名前缀</summary>
        [SerializeField] private string dressPrefix = "Dress_";
        
        /// <summary>鞋子类服装的文件名前缀</summary>
        [SerializeField] private string shoesPrefix = "Shoes_";
        
        /// <summary>配饰类服装的文件名前缀</summary>
        [SerializeField] private string accessoryPrefix = "Acc_";
        
        /// <summary>帽子类服装的文件名前缀</summary>
        [SerializeField] private string hatPrefix = "Hat_";
        
        /// <summary>手套类服装的文件名前缀</summary>
        [SerializeField] private string glovesPrefix = "Gloves_";
        
        /// <summary>袜子类服装的文件名前缀</summary>
        [SerializeField] private string socksPrefix = "Socks_";
        #endregion

        #region 私有字段
        /// <summary>
        /// 分类后的服装库字典
        /// <para>按服装类型分类存储的服装Prefab集合，提高查找和管理效率</para>
        /// </summary>
        private Dictionary<VRM10ClothType, List<GameObject>> categorizedClothes = new Dictionary<VRM10ClothType, List<GameObject>>();
        
        /// <summary>
        /// 单例模式的静态实例
        /// <para>确保系统中只有一个VRM10UnifiedManager实例</para>
        /// </summary>
        private static VRM10UnifiedManager _instance;
        #endregion
        
        #region 单例模式实现
        public static VRM10UnifiedManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<VRM10UnifiedManager>();
                    if (_instance == null)
                    {
                        Debug.Log("🚨 [VRM10UnifiedManager] 没有找到预部署的实例，创建新的GameObject");
                        var go = new GameObject("VRM10UnifiedManager");
                        _instance = go.AddComponent<VRM10UnifiedManager>();
                        DontDestroyOnLoad(go);
                    }
                    else
                    {
                        Debug.Log("✅ [VRM10UnifiedManager] 使用预部署的实例");
                    }
                }
                return _instance;
            }
        }
        
        // 事件系统
        public System.Action<string> OnCharacterChanged;
        public System.Action<VRM10WornCloth> OnClothChanged;
        public System.Action<string, float> OnFaceParameterChanged;
        public System.Action OnSystemInitialized;
        
        // 初始化状态
        private bool isInitialized = false;
        

        
        private void Awake()
        {
            // 单例模式设置
            if (_instance == null)
            {
                _instance = this;
                // 只有动态创建的实例才设置DontDestroyOnLoad
                if (gameObject.name == "VRM10UnifiedManager" && transform.parent == null)
                {
                    DontDestroyOnLoad(gameObject);
                    Debug.Log("🎯 VRM10UnifiedManager初始化 (DontDestroyOnLoad)");
                }
                else
                {
                    Debug.Log("🎯 VRM10UnifiedManager初始化 (预部署实例)");
                }
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            if (autoInitialize)
            {
                InitializeSystem();
            }

            // 自动扫描服装库
            if (autoScanOnStart)
            {
                ScanClothLibrary();
            }
        }
        
        /// <summary>
        /// 初始化系统
        /// </summary>
        public void InitializeSystem()
        {
            if (isInitialized) return;
            
            LogDebug("开始初始化VRM 1.0统一管理系统...");
            
            // 自动查找组件
            FindVRMComponents();
            
            // 初始化子系统
            InitializeSubSystems();
            
            // 设置事件监听
            SetupEventListeners();
            
            isInitialized = true;
            LogDebug("VRM 1.0统一管理系统初始化完成！");
            
            OnSystemInitialized?.Invoke();
        }
        
        /// <summary>
        /// 查找VRM组件
        /// </summary>
        private void FindVRMComponents()
        {
            // 在方法开头声明vrmAssetManager，避免变量名冲突
            var vrmAssetManager = VRMAssetManager.Instance;
            
            // 🎯 新方案：优先使用场景中的LoadedCharacter，避免重创建
            var existingLoadedCharacter = GameObject.Find("LoadedCharacter");
            if (existingLoadedCharacter != null)
            {
                var existingVrmInstance = existingLoadedCharacter.GetComponent<Vrm10Instance>();
                if (existingVrmInstance != null)
                {
                    vrmInstance = existingVrmInstance;
                    LogDebug($"🎯 [新方案] 使用场景中的LoadedCharacter: {existingLoadedCharacter.name}");
                    
                    // 注册到VRMAssetManager但不触发重创建
                    if (vrmAssetManager != null)
                    {
                        vrmAssetManager.RegisterVRMModel(existingLoadedCharacter);
                        LogDebug($"📝 LoadedCharacter已注册到VRMAssetManager: {existingLoadedCharacter.name}");
                    }
                    
                    // 跳过后续查找逻辑
                    LogDebug("✅ [新方案] 跳过VRMAssetManager重创建逻辑");
                    FindFaceAndClothComponents();
                    return;
                }
            }
            
            // 🚫 旧方案：如果没有LoadedCharacter才使用VRMAssetManager
            LogDebug("📍 [旧方案] 未找到LoadedCharacter，使用VRMAssetManager查找...");
            
            // 首先尝试从VRMAssetManager获取当前模型
            if (vrmAssetManager != null)
            {
                var currentModel = vrmAssetManager.GetCurrentVRMInstance();
                if (currentModel != null)
                {
                    vrmInstance = currentModel.GetComponent<Vrm10Instance>();
                    LogDebug($"✅ 从VRMAssetManager获取VRM实例: {currentModel.name}");
                }
                else if (vrmAssetManager.HasValidVRMModel())
                {
                    // 尝试重新创建VRM模型
                    var recreatedModel = vrmAssetManager.RecreateVRMModel();
                    if (recreatedModel != null)
                    {
                        vrmInstance = recreatedModel.GetComponent<Vrm10Instance>();
                        LogDebug($"✅ 重新创建VRM模型成功: {recreatedModel.name}");
                    }
                }
            }

            // 如果还没有找到，使用传统方法查找VRM实例
            if (vrmInstance == null)
            {
                vrmInstance = GetComponent<Vrm10Instance>();
                if (vrmInstance == null)
                {
                    vrmInstance = GetComponentInChildren<Vrm10Instance>();
                }
                if (vrmInstance == null)
                {
                    vrmInstance = FindObjectOfType<Vrm10Instance>();
                }

                // 如果找到了VRM实例，将其注册到VRMAssetManager
                if (vrmInstance != null && vrmAssetManager != null)
                {
                    vrmAssetManager.RegisterVRMModel(vrmInstance.gameObject);
                    LogDebug($"📝 将找到的VRM模型注册到VRMAssetManager: {vrmInstance.name}");
                }
            }
            
            FindFaceAndClothComponents();
        }
        
        /// <summary>
        /// 查找面部和服装组件
        /// </summary>
        private void FindFaceAndClothComponents()
        {
            // 查找面部控制器
            if (faceController == null)
            {
                faceController = GetComponent<VRM10FaceController>();
                if (faceController == null && vrmInstance != null)
                {
                    faceController = vrmInstance.GetComponent<VRM10FaceController>();
                }
            }
            
            // 查找服装绑定器
            if (clothBinder == null)
            {
                clothBinder = GetComponent<VRM10ClothBinder>();
                if (clothBinder == null && vrmInstance != null)
                {
                    clothBinder = vrmInstance.GetComponent<VRM10ClothBinder>();
                }
            }
            
            LogDebug($"组件查找结果 - VRM实例: {(vrmInstance != null ? "✓" : "✗")}, " +
                    $"面部控制器: {(faceController != null ? "✓" : "✗")}, " +
                    $"服装绑定器: {(clothBinder != null ? "✓" : "✗")}");
        }
        
        /// <summary>
        /// 初始化子系统
        /// </summary>
        private void InitializeSubSystems()
        {
            // 初始化面部控制器
            if (faceController != null && !faceController.IsInitialized())
            {
                faceController.InitializeFaceController();
            }
            
            // 服装绑定器会自动初始化
        }
        
        /// <summary>
        /// 设置事件监听
        /// </summary>
        private void SetupEventListeners()
        {
            if (faceController != null)
            {
                faceController.OnParameterChanged += (name, value) =>
                {
                    OnFaceParameterChanged?.Invoke(name, value);
                    OnCharacterChanged?.Invoke($"面部参数变化: {name} = {value}");
                };
            }
            
            if (clothBinder != null)
            {
                clothBinder.OnClothWorn += (cloth) =>
                {
                    OnClothChanged?.Invoke(cloth);
                    OnCharacterChanged?.Invoke($"穿上服装: {cloth.name}");
                };
                
                clothBinder.OnClothRemoved += (cloth) =>
                {
                    OnClothChanged?.Invoke(cloth);
                    OnCharacterChanged?.Invoke($"脱下服装: {cloth.name}");
                };
            }
        }
        #endregion
        
        #region 面部控制API
        
        /// <summary>
        /// 设置面部表情
        /// </summary>
        public void SetFaceExpression(string expressionName, float weight)
        {
            if (faceController != null)
            {
                faceController.SetExpression(expressionName, weight);
            }
            else
            {
                LogDebug("面部控制器未初始化");
            }
        }
        
        /// <summary>
        /// 重置面部参数
        /// </summary>
        public void ResetFaceParameters()
        {
            if (faceController != null)
            {
                faceController.ResetFacialParameters();
            }
        }
        
        /// <summary>
        /// 随机化面部参数
        /// </summary>
        public void RandomizeFaceParameters()
        {
            if (faceController != null)
            {
                faceController.RandomizeFacialParameters();
            }
        }
        
        /// <summary>
        /// 获取面部参数
        /// </summary>
        public VRM10FacialParameters GetFaceParameters()
        {
            return faceController?.GetFacialParameters();
        }
        
        #endregion
        
        #region 换装控制API
        
        /// <summary>
        /// 穿上服装
        /// </summary>
        public bool WearCloth(GameObject clothPrefab)
        {
            if (clothBinder != null)
            {
                var result = clothBinder.WearCloth(clothPrefab);
                return result != null;
            }
            return false;
        }
        
        /// <summary>
        /// 按类型脱下服装
        /// </summary>
        public bool RemoveClothByType(VRM10ClothType type)
        {
            if (clothBinder != null)
            {
                return clothBinder.RemoveClothByType(type);
            }
            return false;
        }
        
        /// <summary>
        /// 脱下所有服装
        /// </summary>
        public void RemoveAllClothes()
        {
            if (clothBinder != null)
            {
                clothBinder.RemoveAllClothes();
            }
        }
        
        /// <summary>
        /// 穿上整套服装
        /// </summary>
        public void WearOutfit(VRM10OutfitPreset outfit)
        {
            if (outfit == null || clothBinder == null) return;
            
            LogDebug($"穿上套装: {outfit.name}");
            
            // 先脱下所有服装
            RemoveAllClothes();
            
            // 穿上套装中的每件服装
            foreach (var clothPrefab in outfit.clothPrefabs)
            {
                if (clothPrefab != null)
                {
                    WearCloth(clothPrefab);
                }
            }
        }
        
        /// <summary>
        /// 获取已穿戴的服装
        /// </summary>
        public List<VRM10WornCloth> GetWornClothes()
        {
            return clothBinder?.GetWornClothes() ?? new List<VRM10WornCloth>();
        }
        
        #endregion
        
        #region 数据管理API

        /// <summary>
        /// 获取当前角色数据
        /// </summary>
        public CharacterData GetCurrentCharacterData()
        {
            var characterData = new CharacterData
            {
                characterId = System.Guid.NewGuid().ToString(),
                characterName = vrmInstance?.name ?? "未命名角色",
                createdTime = System.DateTime.Now,
                lastModifiedTime = System.DateTime.Now
            };

            // 收集面部参数数据
            if (faceController != null && faceController.IsInitialized())
            {
                var faceParams = faceController.GetFacialParameters();
                var facialParametersData = new FacialParametersData
                {
                    standardExpressions = new Dictionary<string, float>(),
                    customExpressions = new Dictionary<string, float>(),
                    blendShapeParameters = new Dictionary<string, float>()
                };

                // 收集标准表情数据
                foreach (var expr in faceParams.standardExpressions)
                {
                    facialParametersData.standardExpressions[expr.name] = expr.currentValue;
                }

                // 收集自定义表情数据
                foreach (var expr in faceParams.customExpressions)
                {
                    facialParametersData.customExpressions[expr.name] = expr.currentValue;
                }

                // 收集BlendShape参数
                var allParams = faceParams.GetAllParameters();
                foreach (var param in allParams)
                {
                    facialParametersData.blendShapeParameters[param.name] = param.currentValue;
                }

                // 直接使用FacialParametersData
                characterData.facialData = facialParametersData;
            }

            // 收集服装数据
            if (clothBinder != null)
            {
                var wornClothes = clothBinder.GetWornClothes();
                characterData.clothingData = new ClothingData();

                foreach (var cloth in wornClothes)
                {
                    if (cloth.clothObject != null && cloth.isActive)
                    {
                        // 将当前穿着的服装添加到currentOutfit字典中
                        characterData.clothingData.currentOutfit[cloth.type.ToString()] = cloth.name;

                        // 添加到最近使用的服装列表
                        if (!characterData.clothingData.recentClothes.Contains(cloth.name))
                        {
                            characterData.clothingData.recentClothes.Add(cloth.name);
                        }
                    }
                }
            }

            // 保存VRM模型到管理器
            if (vrmInstance != null)
            {
                // 确保VRMModelManager存在
                var vrmModelManager = VRMModelManager.EnsureInstance();

                // 设置当前VRM模型
                vrmModelManager.SetCurrentVRMModel(vrmInstance.gameObject);
                LogDebug($"VRM模型已保存到管理器: {vrmInstance.name}");

                // 获取VRM模型信息
                var vrmInfo = vrmModelManager.GetVRMModelInfo();
                if (vrmInfo != null)
                {
                    characterData.vrmInstanceId = vrmInfo.instanceId;
                    characterData.vrmModelPath = vrmInfo.modelName;
                    LogDebug($"VRM模型信息: {vrmInfo.modelName}");
                }
            }

            LogDebug($"收集角色数据完成: {characterData.characterName}");
            return characterData;
        }

        /// <summary>
        /// 加载角色数据并应用到当前模型
        /// </summary>
        public void LoadCharacterData(CharacterData characterData)
        {
            LogDebug($"🎭 [LoadCharacterData] 开始加载角色数据: {( characterData != null ? characterData.characterName : "null")}");
            
            if (characterData == null)
            {
                LogDebug("❌ [LoadCharacterData] 角色数据为空，无法加载");
                return;
            }

            LogDebug($"📊 [LoadCharacterData] 角色数据详情:");
            LogDebug($"   - 角色名称: {characterData.characterName}");
            LogDebug($"   - 角色ID: {characterData.characterId}");
            LogDebug($"   - 创建时间: {characterData.createdTime}");
            LogDebug($"   - 面部数据: {( characterData.facialData != null ? "存在" : "不存在")}");
            LogDebug($"   - 服装数据: {( characterData.clothingData != null ? "存在" : "不存在")}");

            // 检查组件状态
            LogDebug($"🔍 [LoadCharacterData] 组件状态检查:");
            LogDebug($"   - faceController: {( faceController != null ? "存在" : "不存在")}");
            LogDebug($"   - clothBinder: {( clothBinder != null ? "存在" : "不存在")}");
            LogDebug($"   - vrmInstance: {( vrmInstance != null ? "存在" : "不存在")}");

            // 应用面部参数
            if (characterData.facialData != null && faceController != null)
            {
                LogDebug("😊 [LoadCharacterData] 开始应用面部参数...");
                
                // 检查面部控制器初始化状态
                if (!faceController.IsInitialized())
                {
                    LogDebug("⚠️ [LoadCharacterData] 面部控制器未初始化，尝试初始化...");
                    faceController.InitializeFaceController();
                    LogDebug($"🔍 [LoadCharacterData] 面部控制器初始化结果: {faceController.IsInitialized()}");
                }
                
                LogDebug("🔄 [LoadCharacterData] 重置所有面部参数...");
                faceController.ResetFacialParameters();
                LogDebug("✅ [LoadCharacterData] 面部参数重置完成");

                // 直接使用FacialParametersData
                var facialParametersData = characterData.facialData;

                // 应用标准表情
                if (facialParametersData.standardExpressions != null)
                {
                    LogDebug($"📋 [LoadCharacterData] 应用标准表情 ({facialParametersData.standardExpressions.Count}个):");
                    int successCount = 0, failCount = 0;
                    
                    foreach (var kvp in facialParametersData.standardExpressions)
                    {
                        LogDebug($"   - 设置 {kvp.Key} = {kvp.Value}");
                        try
                    {
                        faceController.SetExpression(kvp.Key, kvp.Value);
                            successCount++;
                            LogDebug($"     ✅ 成功");
                        }
                        catch (System.Exception ex)
                        {
                            failCount++;
                            LogDebug($"     ❌ 失败: {ex.Message}");
                        }
                    }
                    LogDebug($"📊 [LoadCharacterData] 标准表情应用结果: 成功{successCount}个, 失败{failCount}个");
                }
                else
                {
                    LogDebug("⚠️ [LoadCharacterData] 无标准表情数据");
                }

                // 应用自定义表情
                if (facialParametersData.customExpressions != null)
                {
                    LogDebug($"📋 [LoadCharacterData] 应用自定义表情 ({facialParametersData.customExpressions.Count}个):");
                    int successCount = 0, failCount = 0;
                    
                    foreach (var kvp in facialParametersData.customExpressions)
                    {
                        LogDebug($"   - 设置 {kvp.Key} = {kvp.Value}");
                        try
                    {
                        faceController.SetExpression(kvp.Key, kvp.Value);
                            successCount++;
                            LogDebug($"     ✅ 成功");
                        }
                        catch (System.Exception ex)
                        {
                            failCount++;
                            LogDebug($"     ❌ 失败: {ex.Message}");
                    }
                }
                    LogDebug($"📊 [LoadCharacterData] 自定义表情应用结果: 成功{successCount}个, 失败{failCount}个");
                }
                else
                {
                    LogDebug("⚠️ [LoadCharacterData] 无自定义表情数据");
                }

                LogDebug("✅ [LoadCharacterData] 面部参数应用完成");
                
                // 应用BlendShape参数
                if (facialParametersData.blendShapeParameters != null && facialParametersData.blendShapeParameters.Count > 0)
                {
                    LogDebug($"📋 [LoadCharacterData] 应用BlendShape参数 ({facialParametersData.blendShapeParameters.Count}个):");
                    int successCount = 0, failCount = 0;
                    
                    foreach (var kvp in facialParametersData.blendShapeParameters)
                    {
                        LogDebug($"   - 设置BlendShape {kvp.Key} = {kvp.Value}");
                        try
                        {
                            faceController.SetParameter(kvp.Key, kvp.Value);
                            successCount++;
                            LogDebug($"     ✅ 成功");
                        }
                        catch (System.Exception ex)
                        {
                            failCount++;
                            LogDebug($"     ❌ 失败: {ex.Message}");
                        }
                    }
                    LogDebug($"📊 [LoadCharacterData] BlendShape参数应用结果: 成功{successCount}个, 失败{failCount}个");
                }
                else
                {
                    LogDebug("⚠️ [LoadCharacterData] 无BlendShape参数数据");
                }
            }
            else
            {
                if (characterData.facialData == null)
                {
                    LogDebug("⚠️ [LoadCharacterData] 跳过面部参数应用 - 无面部数据");
                }
                if (faceController == null)
                {
                    LogDebug("⚠️ [LoadCharacterData] 跳过面部参数应用 - 面部控制器不存在");
                }
            }

            // 应用服装数据
            if (characterData.clothingData != null && clothBinder != null)
            {
                LogDebug("👔 [LoadCharacterData] 开始应用服装数据...");
                
                LogDebug("🔄 [LoadCharacterData] 脱下所有当前服装...");
                clothBinder.RemoveAllClothes();
                LogDebug("✅ [LoadCharacterData] 所有服装已移除");

                // 穿上保存的服装
                if (characterData.clothingData.currentOutfit != null)
                {
                    LogDebug($"📋 [LoadCharacterData] 应用保存的服装 ({characterData.clothingData.currentOutfit.Count}件):");
                    
                    int successCount = 0, failCount = 0;
                    foreach (var kvp in characterData.clothingData.currentOutfit)
                    {
                        LogDebug($"   - 尝试穿上: {kvp.Key} - {kvp.Value}");
                        try
                        {
                            // 根据服装名称查找并穿上服装
                            WearClothByName(kvp.Value);
                            successCount++;
                            LogDebug($"     ✅ 尝试穿上服装: {kvp.Value}");
                        }
                        catch (System.Exception ex)
                        {
                            failCount++;
                            LogDebug($"     ❌ 穿戴失败: {ex.Message}");
                        }
                    }
                    LogDebug($"📊 [LoadCharacterData] 服装应用结果: 成功{successCount}件, 失败{failCount}件");
                }
                else
                {
                    LogDebug("⚠️ [LoadCharacterData] 无当前服装数据");
                }

                LogDebug("✅ [LoadCharacterData] 服装数据应用完成");
            }
            else
            {
                if (characterData.clothingData == null)
                {
                    LogDebug("⚠️ [LoadCharacterData] 跳过服装应用 - 无服装数据");
                    }
                if (clothBinder == null)
                {
                    LogDebug("⚠️ [LoadCharacterData] 跳过服装应用 - 服装绑定器不存在");
                }
            }

            LogDebug($"🎉 [LoadCharacterData] 角色数据加载完成: {characterData.characterName}");
            OnCharacterChanged?.Invoke($"加载角色数据: {characterData.characterName}");
        }

        /// <summary>
        /// 保存当前角色状态
        /// </summary>
        public void SaveCharacterState(string saveName)
        {
            var characterData = GetCurrentCharacterData();
            characterData.characterName = saveName;

            // 通过CharacterDataManager保存
            var dataManager = CharacterDataManager.Instance;
            if (dataManager != null)
            {
                _ = dataManager.SaveCharacterAsync(characterData, saveName);
            }
            else
            {
                LogDebug("CharacterDataManager未找到，无法保存数据");
            }
        }

        /// <summary>
        /// 加载角色状态
        /// </summary>
        public void LoadCharacterState(string saveName)
        {
            var dataManager = CharacterDataManager.Instance;
            if (dataManager != null)
            {
                _ = LoadCharacterStateAsync(saveName);
            }
            else
            {
                LogDebug("CharacterDataManager未找到，无法加载数据");
            }
        }

        /// <summary>
        /// 异步加载角色状态
        /// </summary>
        private async System.Threading.Tasks.Task LoadCharacterStateAsync(string saveName)
        {
            try
            {
                var dataManager = CharacterDataManager.Instance;
                var characterData = await dataManager.LoadCharacterAsync(saveName, true);
                if (characterData != null)
                {
                    LoadCharacterData(characterData);
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"加载角色状态失败: {e.Message}");
            }
        }

        #endregion
        
        #region WebGL接口
        
        /// <summary>
        /// WebGL专用的表情设置接口
        /// </summary>
        public void SetVRMExpression(string expressionName, float weight)
        {
            SetFaceExpression(expressionName, weight);
        }
        
        /// <summary>
        /// WebGL专用的换装接口
        /// </summary>
        public void WearClothByName(string clothName)
        {
            var clothPrefab = availableClothes.FirstOrDefault(c => c.name == clothName);
            if (clothPrefab != null)
            {
                WearCloth(clothPrefab);
            }
            else
            {
                LogDebug($"未找到服装: {clothName}");
            }
        }
        
        /// <summary>
        /// WebGL专用的套装接口
        /// </summary>
        public void WearOutfitByName(string outfitName)
        {
            var outfit = outfitPresets.FirstOrDefault(o => o.name == outfitName);
            if (outfit != null)
            {
                WearOutfit(outfit);
            }
            else
            {
                LogDebug($"未找到套装: {outfitName}");
            }
        }
        
        /// <summary>
        /// 重置所有参数
        /// </summary>
        public void ResetAllParameters()
        {
            ResetFaceParameters();
            RemoveAllClothes();
        }
        
        #endregion
        
        #region 工具方法
        
        /// <summary>
        /// 获取可用的服装列表
        /// </summary>
        public List<string> GetAvailableClothNames()
        {
            return availableClothes.Where(c => c != null).Select(c => c.name).ToList();
        }
        
        /// <summary>
        /// 获取可用的套装列表
        /// </summary>
        public List<string> GetAvailableOutfitNames()
        {
            return outfitPresets.Select(o => o.name).ToList();
        }
        
        /// <summary>
        /// 检查系统是否已初始化
        /// </summary>
        public bool IsSystemInitialized()
        {
            return isInitialized;
        }
        
        /// <summary>
        /// 获取VRM实例
        /// </summary>
        public Vrm10Instance GetVRMInstance()
        {
            return vrmInstance;
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRM10UnifiedManager] {message}");
            }
        }

        /// <summary>
        /// 将FacialParametersData转换为VRM10FacialParameters
        /// </summary>
        private VRM10FacialParameters ConvertToFacialParameters(FacialParametersData data)
        {
            var facialParams = new VRM10FacialParameters();

            // 将数据映射到VRM10FacialParameters
            foreach (var kvp in data.standardExpressions)
            {
                facialParams.standardExpressions.Add(new FacialParameter
                {
                    name = kvp.Key,
                    displayName = kvp.Key,
                    currentValue = kvp.Value,
                    defaultValue = 0f,
                    isExpression = true
                });
            }

            foreach (var kvp in data.customExpressions)
            {
                facialParams.customExpressions.Add(new FacialParameter
                {
                    name = kvp.Key,
                    displayName = kvp.Key,
                    currentValue = kvp.Value,
                    defaultValue = 0f,
                    isExpression = true
                });
            }

            foreach (var kvp in data.blendShapeParameters)
            {
                facialParams.blendShapeParameters.Add(new FacialParameter
                {
                    name = kvp.Key,
                    displayName = kvp.Key,
                    currentValue = kvp.Value,
                    defaultValue = 0f,
                    isExpression = false
                });
            }

            return facialParams;
        }

        /// <summary>
        /// 将VRM10FacialParameters转换为FacialParametersData
        /// </summary>
        private FacialParametersData ConvertFromFacialParameters(VRM10FacialParameters facialParams)
        {
            var data = new FacialParametersData();

            // 将VRM10FacialParameters映射到数据
            foreach (var param in facialParams.standardExpressions)
            {
                data.standardExpressions[param.name] = param.currentValue;
            }

            foreach (var param in facialParams.customExpressions)
            {
                data.customExpressions[param.name] = param.currentValue;
            }

            foreach (var param in facialParams.blendShapeParameters)
            {
                data.blendShapeParameters[param.name] = param.currentValue;
            }

            return data;
        }

        #endregion

        #region 自动服装库管理

        /// <summary>
        /// 扫描服装库并自动分类
        /// </summary>
        [ContextMenu("扫描服装库")]
        public void ScanClothLibrary()
        {
            LogDebug("开始扫描服装库...");

            // 清空现有服装库
            availableClothes.Clear();
            categorizedClothes.Clear();

            // 初始化分类字典
            InitializeCategorizedClothes();

            // 扫描指定路径下的所有Prefab
            if (!System.IO.Directory.Exists(clothLibraryPath))
            {
                LogDebug($"服装库路径不存在: {clothLibraryPath}");
                return;
            }

            string[] prefabFiles = System.IO.Directory.GetFiles(clothLibraryPath, "*.prefab", System.IO.SearchOption.AllDirectories);

            int loadedCount = 0;
            foreach (string prefabPath in prefabFiles)
            {
                // 转换为Unity资源路径
                string assetPath = prefabPath.Replace('\\', '/');
                if (assetPath.StartsWith(Application.dataPath))
                {
                    assetPath = "Assets" + assetPath.Substring(Application.dataPath.Length);
                }

                // 加载Prefab
                GameObject clothPrefab = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                if (clothPrefab != null)
                {
                    // 检查是否为有效的服装Prefab
                    if (IsValidClothPrefab(clothPrefab))
                    {
                        availableClothes.Add(clothPrefab);

                        // 根据前缀或VRM10ClothInfo分类
                        VRM10ClothType clothType = DetermineClothType(clothPrefab);
                        categorizedClothes[clothType].Add(clothPrefab);

                        loadedCount++;
                        LogDebug($"加载服装: {clothPrefab.name} ({clothType})");
                    }
                }
            }

            LogDebug($"服装库扫描完成，共加载 {loadedCount} 件服装");
            LogCategorizedClothes();
        }

        /// <summary>
        /// 初始化分类字典
        /// </summary>
        private void InitializeCategorizedClothes()
        {
            categorizedClothes.Clear();
            foreach (VRM10ClothType type in System.Enum.GetValues(typeof(VRM10ClothType)))
            {
                categorizedClothes[type] = new List<GameObject>();
            }
        }

        /// <summary>
        /// 检查是否为有效的服装Prefab
        /// </summary>
        private bool IsValidClothPrefab(GameObject prefab)
        {
            // 检查是否有SkinnedMeshRenderer
            var renderer = prefab.GetComponent<SkinnedMeshRenderer>();
            if (renderer == null) return false;

            // 检查是否有VRM10ClothInfo组件
            var clothInfo = prefab.GetComponent<VRM10ClothInfo>();

            return true; // 基本检查通过
        }

        /// <summary>
        /// 根据前缀或组件信息确定服装类型
        /// </summary>
        private VRM10ClothType DetermineClothType(GameObject clothPrefab)
        {
            // 优先检查VRM10ClothInfo组件
            var clothInfo = clothPrefab.GetComponent<VRM10ClothInfo>();
            if (clothInfo != null)
            {
                return clothInfo.clothType;
            }

            // 如果启用了前缀分类，则根据文件名前缀判断
            if (useFilePrefix)
            {
                string prefabName = clothPrefab.name;

                if (prefabName.StartsWith(topPrefix)) return VRM10ClothType.Top;
                if (prefabName.StartsWith(bottomPrefix)) return VRM10ClothType.Bottom;
                if (prefabName.StartsWith(dressPrefix)) return VRM10ClothType.Dress;
                if (prefabName.StartsWith(shoesPrefix)) return VRM10ClothType.Shoes;
                if (prefabName.StartsWith(hatPrefix)) return VRM10ClothType.Hat;
                if (prefabName.StartsWith(glovesPrefix)) return VRM10ClothType.Gloves;
                if (prefabName.StartsWith(socksPrefix)) return VRM10ClothType.Socks;
                if (prefabName.StartsWith(accessoryPrefix)) return VRM10ClothType.Accessory;
            }

            // 默认返回Other类型
            return VRM10ClothType.Other;
        }

        /// <summary>
        /// 输出分类结果
        /// </summary>
        private void LogCategorizedClothes()
        {
            LogDebug("=== 服装分类结果 ===");
            foreach (var kvp in categorizedClothes)
            {
                if (kvp.Value.Count > 0)
                {
                    LogDebug($"{kvp.Key}: {kvp.Value.Count} 件");
                    foreach (var cloth in kvp.Value)
                    {
                        LogDebug($"  - {cloth.name}");
                    }
                }
            }
        }

        /// <summary>
        /// 获取指定类型的服装列表
        /// </summary>
        public List<GameObject> GetClothsByType(VRM10ClothType clothType)
        {
            if (categorizedClothes.ContainsKey(clothType))
            {
                return new List<GameObject>(categorizedClothes[clothType]);
            }
            return new List<GameObject>();
        }

        /// <summary>
        /// 获取所有分类的服装
        /// </summary>
        public Dictionary<VRM10ClothType, List<GameObject>> GetCategorizedClothes()
        {
            return new Dictionary<VRM10ClothType, List<GameObject>>(categorizedClothes);
        }

        /// <summary>
        /// 添加服装到库中
        /// </summary>
        public void AddClothToLibrary(GameObject clothPrefab)
        {
            if (clothPrefab == null) return;

            if (!availableClothes.Contains(clothPrefab))
            {
                availableClothes.Add(clothPrefab);

                VRM10ClothType clothType = DetermineClothType(clothPrefab);
                if (!categorizedClothes.ContainsKey(clothType))
                {
                    categorizedClothes[clothType] = new List<GameObject>();
                }
                categorizedClothes[clothType].Add(clothPrefab);

                LogDebug($"添加服装到库: {clothPrefab.name} ({clothType})");
            }
        }

        /// <summary>
        /// 从库中移除服装
        /// </summary>
        public void RemoveClothFromLibrary(GameObject clothPrefab)
        {
            if (clothPrefab == null) return;

            availableClothes.Remove(clothPrefab);

            foreach (var kvp in categorizedClothes)
            {
                kvp.Value.Remove(clothPrefab);
            }

            LogDebug($"从库中移除服装: {clothPrefab.name}");
        }

        /// <summary>
        /// 设置服装库路径并重新扫描
        /// </summary>
        public void SetClothLibraryPath(string newPath)
        {
            clothLibraryPath = newPath;
            ScanClothLibrary();
        }

        /// <summary>
        /// 更新前缀分类规则
        /// </summary>
        public void UpdatePrefixRules(string top, string bottom, string dress, string shoes, string accessory, string hat, string gloves = "Gloves_", string socks = "Socks_")
        {
            topPrefix = top;
            bottomPrefix = bottom;
            dressPrefix = dress;
            shoesPrefix = shoes;
            accessoryPrefix = accessory;
            hatPrefix = hat;
            glovesPrefix = gloves;
            socksPrefix = socks;

            // 重新分类现有服装
            if (availableClothes.Count > 0)
            {
                ReclassifyClothes();
            }
        }

        /// <summary>
        /// 重新分类现有服装
        /// </summary>
        private void ReclassifyClothes()
        {
            LogDebug("重新分类现有服装...");

            // 清空分类
            InitializeCategorizedClothes();

            // 重新分类
            foreach (var cloth in availableClothes)
            {
                VRM10ClothType clothType = DetermineClothType(cloth);
                categorizedClothes[clothType].Add(cloth);
            }

            LogCategorizedClothes();
        }

        #endregion

        #region 角色管理
        /// <summary>
        /// 设置当前管理的角色对象
        /// </summary>
        /// <param name="characterObject">要管理的角色GameObject</param>
        public void SetCurrentCharacter(GameObject characterObject)
        {
            if (characterObject == null)
            {
                LogDebug("❌ [SetCurrentCharacter] 传入的角色对象为null");
                return;
            }

            LogDebug($"🎯 [SetCurrentCharacter] 设置当前角色: {characterObject.name}");
            
            // 重新初始化组件引用，针对新的角色对象
            vrmInstance = characterObject.GetComponent<Vrm10Instance>();
            if (vrmInstance == null)
            {
                vrmInstance = characterObject.GetComponentInChildren<Vrm10Instance>();
            }

            faceController = characterObject.GetComponent<VRM10FaceController>();
            if (faceController == null)
            {
                faceController = characterObject.GetComponentInChildren<VRM10FaceController>();
            }

            clothBinder = characterObject.GetComponent<VRM10ClothBinder>();
            if (clothBinder == null)
            {
                clothBinder = characterObject.GetComponentInChildren<VRM10ClothBinder>();
            }

            LogDebug($"✅ [SetCurrentCharacter] 组件查找结果:");
            LogDebug($"   - VRM实例: {( vrmInstance != null ? "✓" : "✗")}");
            LogDebug($"   - 面部控制器: {( faceController != null ? "✓" : "✗")}");
            LogDebug($"   - 服装绑定器: {( clothBinder != null ? "✓" : "✗")}");
            
            // 如果找到了VRM实例，进行进一步初始化
            if (vrmInstance != null)
            {
                InitializeSubSystems();
                LogDebug($"🎉 [SetCurrentCharacter] 角色设置完成: {characterObject.name}");
            }
            else
            {
                LogDebug($"⚠️ [SetCurrentCharacter] 角色对象缺少Vrm10Instance组件: {characterObject.name}");
            }
        }
        #endregion
    }
}
