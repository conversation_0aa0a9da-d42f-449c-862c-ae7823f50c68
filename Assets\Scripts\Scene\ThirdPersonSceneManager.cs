using UnityEngine;
using System.Collections;
using VRoidFaceCustomization.Core;

namespace VRoidFaceCustomization.Scene
{
    /// <summary>
    /// 第三人称测试场景管理器 - 自动加载和应用角色数据
    /// </summary>
    public class ThirdPersonSceneManager : MonoBehaviour
    {
        [Header("VRM设置")]
        [SerializeField] private GameObject defaultVRMPrefab; // 默认VRM预制体
        [SerializeField] private Transform vrmSpawnPoint; // VRM生成位置
        [SerializeField] private bool autoLoadOnStart = true; // 启动时自动加载
        
        [Header("场景设置")]
        [SerializeField] private bool keepOnlyEssentials = true; // 只保留必要对象
        [SerializeField] private string[] essentialTags = { "MainCamera", "Directional Light", "Ground" }; // 必要标签
        
        [Header("调试设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private bool showLoadingUI = true;
        
        [Header("当前状态")]
        [SerializeField] private GameObject currentVRMObject;
        [SerializeField] private bool isLoading = false;
        [SerializeField] private bool hasLoadedData = false;

        private GameObject createdVRMObject;
        
        private VRMDataApplier dataApplier;
        
        #region Unity生命周期
        private void Awake()
        {
            LogDebug("✅ ThirdPersonSceneManager 初始化");
            
            // 获取或创建数据应用器
            dataApplier = GetOrCreateDataApplier();
        }
        
        private void Start()
        {
            if (autoLoadOnStart)
            {
                StartCoroutine(AutoLoadCharacterDataCoroutine());
            }
        }
        #endregion
        
        #region 自动加载功能
        /// <summary>
        /// 自动加载角色数据的协程
        /// </summary>
        private IEnumerator AutoLoadCharacterDataCoroutine()
        {
            LogDebug("🚀 开始自动加载角色数据");
            isLoading = true;
            
            // 等待一帧确保场景完全加载
            yield return null;
            
            // 清理场景（可选）
            if (keepOnlyEssentials)
            {
                CleanupScene();
            }
            
            // 检查桥接器中是否有数据
            var bridge = CharacterDataBridge.Instance;
            
            if (!bridge.HasValidData())
            {
                LogDebug("⚠️ 桥接器中没有有效数据，使用默认VRM");
                yield return StartCoroutine(LoadDefaultVRMCoroutine());
            }
            else
            {
                LogDebug("📂 发现桥接器数据，开始加载");
                var parameters = bridge.GetCharacterParameters();
                yield return StartCoroutine(LoadCharacterFromDataCoroutine(parameters));
            }
            
            isLoading = false;
            LogDebug("🎉 角色数据加载完成");
        }
        
        /// <summary>
        /// 从数据加载角色的协程
        /// </summary>
        private IEnumerator LoadCharacterFromDataCoroutine(CharacterParameters parameters)
        {
            if (parameters == null || !parameters.IsValid())
            {
                LogDebug("❌ 角色参数无效，使用默认VRM");
                yield return StartCoroutine(LoadDefaultVRMCoroutine());
                yield break;
            }
            
            LogDebug($"📊 加载角色: {parameters.characterName}");
            LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
            LogDebug($"   👔 服装数量: {parameters.clothingData.GetClothingCount()}件");
            
            // 步骤1：创建或获取VRM对象
            createdVRMObject = null;
            yield return StartCoroutine(CreateVRMObjectCoroutine(parameters));

            if (createdVRMObject == null)
            {
                LogDebug("❌ VRM对象创建失败，使用默认VRM");
                yield return StartCoroutine(LoadDefaultVRMCoroutine());
                yield break;
            }
            
            // 步骤2：等待VRM初始化
            yield return StartCoroutine(WaitForVRMInitializationCoroutine(createdVRMObject));

            // 步骤3：应用角色参数
            bool applySuccess = false;
            dataApplier.ApplyToVRM(createdVRMObject, parameters, (success) => {
                applySuccess = success;
            });

            // 等待应用完成
            while (dataApplier.IsApplying())
            {
                yield return null;
            }

            if (applySuccess)
            {
                LogDebug("✅ 角色参数应用成功");
                currentVRMObject = createdVRMObject;
                hasLoadedData = true;

                // 触发加载完成事件
                OnCharacterLoaded?.Invoke(createdVRMObject, parameters);
            }
            else
            {
                LogDebug("❌ 角色参数应用失败");

                // 清理失败的对象
                if (createdVRMObject != null)
                {
                    Destroy(createdVRMObject);
                }

                // 使用默认VRM
                yield return StartCoroutine(LoadDefaultVRMCoroutine());
            }
        }
        
        /// <summary>
        /// 加载默认VRM的协程
        /// </summary>
        private IEnumerator LoadDefaultVRMCoroutine()
        {
            LogDebug("🔧 加载默认VRM");
            
            if (defaultVRMPrefab == null)
            {
                LogDebug("❌ 未设置默认VRM预制体");
                yield break;
            }
            
            // 实例化默认VRM
            Vector3 spawnPosition = vrmSpawnPoint != null ? vrmSpawnPoint.position : Vector3.zero;
            Quaternion spawnRotation = vrmSpawnPoint != null ? vrmSpawnPoint.rotation : Quaternion.identity;
            
            GameObject vrmObject = Instantiate(defaultVRMPrefab, spawnPosition, spawnRotation);
            vrmObject.name = "DefaultVRM";
            
            // 等待初始化
            yield return StartCoroutine(WaitForVRMInitializationCoroutine(vrmObject));
            
            currentVRMObject = vrmObject;
            LogDebug($"✅ 默认VRM加载完成: {vrmObject.name}");
            
            // 触发默认加载完成事件
            OnDefaultVRMLoaded?.Invoke(vrmObject);
        }
        
        /// <summary>
        /// 创建VRM对象的协程
        /// </summary>
        private IEnumerator CreateVRMObjectCoroutine(CharacterParameters parameters)
        {
            // 这里需要根据你的VRM加载系统来实现
            // 暂时使用默认预制体作为基础
            
            if (defaultVRMPrefab == null)
            {
                LogDebug("❌ 未设置默认VRM预制体，无法创建VRM对象");
                createdVRMObject = null;
                yield break;
            }

            Vector3 spawnPosition = vrmSpawnPoint != null ? vrmSpawnPoint.position : parameters.modelPosition;
            Quaternion spawnRotation = vrmSpawnPoint != null ? vrmSpawnPoint.rotation : Quaternion.Euler(parameters.modelRotation);

            createdVRMObject = Instantiate(defaultVRMPrefab, spawnPosition, spawnRotation);
            createdVRMObject.name = parameters.characterName;
            createdVRMObject.transform.localScale = parameters.modelScale;

            LogDebug($"🔧 创建VRM对象: {createdVRMObject.name}");
        }
        
        /// <summary>
        /// 等待VRM初始化完成的协程
        /// </summary>
        private IEnumerator WaitForVRMInitializationCoroutine(GameObject vrmObject)
        {
            LogDebug("⏳ 等待VRM初始化...");
            
            var vrmInstance = vrmObject.GetComponent<UniVRM10.Vrm10Instance>();
            if (vrmInstance == null)
            {
                LogDebug("⚠️ 未找到Vrm10Instance组件");
                yield break;
            }
            
            int maxWaitFrames = 60;
            int frameCount = 0;
            
            while (frameCount < maxWaitFrames)
            {
                // 检查VRM是否完全初始化
                if (vrmInstance.Runtime != null && vrmInstance.Humanoid != null)
                {
                    // 尝试获取一个基本骨骼来验证初始化完成
                    if (vrmInstance.TryGetBoneTransform(HumanBodyBones.Hips, out var hips) && hips != null)
                    {
                        LogDebug("✅ VRM初始化完成");
                        break;
                    }
                }
                
                yield return null;
                frameCount++;
            }
            
            // 额外等待几帧确保完全初始化
            for (int i = 0; i < 5; i++)
            {
                yield return null;
            }
            
            LogDebug($"🔄 VRM初始化等待完成 (等待了{frameCount}帧)");
        }
        #endregion
        
        #region 场景清理
        /// <summary>
        /// 清理场景，只保留必要对象
        /// </summary>
        private void CleanupScene()
        {
            LogDebug("🧹 开始清理场景");
            
            var allObjects = FindObjectsOfType<GameObject>();
            int removedCount = 0;
            
            foreach (var obj in allObjects)
            {
                // 跳过根对象和必要对象
                if (obj.transform.parent != null) continue;
                if (IsEssentialObject(obj)) continue;
                if (obj == gameObject) continue; // 跳过自己
                
                LogDebug($"🗑️ 移除对象: {obj.name}");
                Destroy(obj);
                removedCount++;
            }
            
            LogDebug($"✅ 场景清理完成，移除了 {removedCount} 个对象");
        }
        
        /// <summary>
        /// 检查是否为必要对象
        /// </summary>
        private bool IsEssentialObject(GameObject obj)
        {
            // 检查标签
            foreach (var tag in essentialTags)
            {
                if (obj.CompareTag(tag))
                {
                    return true;
                }
            }
            
            // 检查名称
            string objName = obj.name.ToLower();
            if (objName.Contains("camera") || objName.Contains("light") || objName.Contains("ground"))
            {
                return true;
            }
            
            return false;
        }
        #endregion
        
        #region 辅助方法
        /// <summary>
        /// 获取或创建数据应用器
        /// </summary>
        private VRMDataApplier GetOrCreateDataApplier()
        {
            var applier = FindObjectOfType<VRMDataApplier>();
            
            if (applier == null)
            {
                LogDebug("🔧 创建数据应用器");
                GameObject applierObject = new GameObject("VRMDataApplier");
                applier = applierObject.AddComponent<VRMDataApplier>();
            }
            
            return applier;
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[ThirdPersonSceneManager] {message}");
            }
        }
        #endregion
        
        #region 公共接口
        /// <summary>
        /// 手动加载角色数据
        /// </summary>
        public void LoadCharacterData()
        {
            if (isLoading)
            {
                LogDebug("⚠️ 正在加载中，请稍候");
                return;
            }
            
            StartCoroutine(AutoLoadCharacterDataCoroutine());
        }
        
        /// <summary>
        /// 获取当前VRM对象
        /// </summary>
        public GameObject GetCurrentVRM()
        {
            return currentVRMObject;
        }
        
        /// <summary>
        /// 检查是否正在加载
        /// </summary>
        public bool IsLoading()
        {
            return isLoading;
        }
        
        /// <summary>
        /// 检查是否已加载数据
        /// </summary>
        public bool HasLoadedData()
        {
            return hasLoadedData;
        }
        #endregion
        
        #region 事件系统
        /// <summary>
        /// 角色加载完成事件
        /// </summary>
        public static event System.Action<GameObject, CharacterParameters> OnCharacterLoaded;
        
        /// <summary>
        /// 默认VRM加载完成事件
        /// </summary>
        public static event System.Action<GameObject> OnDefaultVRMLoaded;
        
        /// <summary>
        /// 加载失败事件
        /// </summary>
        public static event System.Action<string> OnLoadFailed;
        #endregion
        
        #region 调试功能
        /// <summary>
        /// 显示当前状态
        /// </summary>
        [ContextMenu("显示当前状态")]
        private void ShowCurrentStatus()
        {
            LogDebug($"当前VRM: {(currentVRMObject != null ? currentVRMObject.name : "null")}");
            LogDebug($"正在加载: {isLoading}");
            LogDebug($"已加载数据: {hasLoadedData}");
            
            var bridge = CharacterDataBridge.Instance;
            LogDebug($"桥接器数据: {bridge.GetDataSummary()}");
        }
        
        /// <summary>
        /// 测试重新加载
        /// </summary>
        [ContextMenu("测试重新加载")]
        private void TestReload()
        {
            if (Application.isPlaying)
            {
                if (currentVRMObject != null)
                {
                    Destroy(currentVRMObject);
                    currentVRMObject = null;
                    hasLoadedData = false;
                }
                
                LoadCharacterData();
            }
            else
            {
                LogDebug("⚠️ 只能在运行时测试重新加载功能");
            }
        }
        #endregion
    }
}
