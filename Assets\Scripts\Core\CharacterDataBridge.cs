using UnityEngine;
using VRoidFaceCustomization.Core;

namespace VRoidFaceCustomization.Core
{
    /// <summary>
    /// 角色数据桥接器 - 场景间数据传递的核心组件
    /// 使用单例模式，在场景切换时保持数据
    /// </summary>
    public class CharacterDataBridge : MonoBehaviour
    {
        #region 单例模式
        private static CharacterDataBridge _instance;
        public static CharacterDataBridge Instance
        {
            get
            {
                if (_instance == null)
                {
                    // 查找现有实例
                    _instance = FindObjectOfType<CharacterDataBridge>();
                    
                    if (_instance == null)
                    {
                        // 创建新实例
                        GameObject bridgeObject = new GameObject("CharacterDataBridge");
                        _instance = bridgeObject.AddComponent<CharacterDataBridge>();
                        DontDestroyOnLoad(bridgeObject);
                        Debug.Log("✅ [CharacterDataBridge] 创建新实例");
                    }
                    else
                    {
                        Debug.Log("✅ [CharacterDataBridge] 使用现有实例");
                    }
                }
                return _instance;
            }
        }
        
        private void Awake()
        {
            // 确保只有一个实例存在
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                Debug.Log("✅ [CharacterDataBridge] 初始化完成");
            }
            else if (_instance != this)
            {
                Debug.Log("🗑️ [CharacterDataBridge] 销毁重复实例");
                Destroy(gameObject);
            }
        }
        #endregion
        
        #region 数据存储
        [Header("当前数据")]
        [SerializeField] private CharacterParameters currentParameters;
        [SerializeField] private bool hasValidData = false;
        
        [Header("调试信息")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private string lastSaveTime = "";
        [SerializeField] private int faceParameterCount = 0;
        [SerializeField] private int clothingCount = 0;
        #endregion
        
        #region 公共接口
        /// <summary>
        /// 设置角色参数数据（用于场景切换）
        /// </summary>
        public void SetCharacterParameters(CharacterParameters parameters)
        {
            if (parameters == null)
            {
                LogDebug("❌ 尝试设置空的角色参数");
                return;
            }
            
            if (!parameters.IsValid())
            {
                LogDebug("❌ 角色参数数据无效");
                return;
            }
            
            // 创建数据副本以避免引用问题
            currentParameters = parameters.Clone();
            hasValidData = true;
            
            // 更新调试信息
            UpdateDebugInfo();
            
            LogDebug($"✅ 角色参数已设置: {parameters.characterName}");
            LogDebug($"   📊 面部参数: {faceParameterCount}个");
            LogDebug($"   👔 服装数量: {clothingCount}件");
        }
        
        /// <summary>
        /// 获取角色参数数据
        /// </summary>
        public CharacterParameters GetCharacterParameters()
        {
            if (!hasValidData || currentParameters == null)
            {
                LogDebug("⚠️ 没有有效的角色参数数据");
                return null;
            }
            
            LogDebug($"📤 返回角色参数: {currentParameters.characterName}");
            return currentParameters.Clone(); // 返回副本以避免外部修改
        }
        
        /// <summary>
        /// 检查是否有有效数据
        /// </summary>
        public bool HasValidData()
        {
            bool isValid = hasValidData && currentParameters != null && currentParameters.IsValid();
            LogDebug($"🔍 数据有效性检查: {isValid}");
            return isValid;
        }
        
        /// <summary>
        /// 清除当前数据
        /// </summary>
        public void ClearData()
        {
            currentParameters = null;
            hasValidData = false;
            UpdateDebugInfo();
            LogDebug("🗑️ 角色参数数据已清除");
        }
        
        /// <summary>
        /// 获取数据摘要信息
        /// </summary>
        public string GetDataSummary()
        {
            if (!HasValidData())
            {
                return "无有效数据";
            }
            
            return $"角色: {currentParameters.characterName}, " +
                   $"面部参数: {currentParameters.faceData.GetTotalParameterCount()}个, " +
                   $"服装: {currentParameters.clothingData.GetClothingCount()}件, " +
                   $"保存时间: {currentParameters.saveTime:HH:mm:ss}";
        }
        #endregion
        
        #region 事件系统
        /// <summary>
        /// 数据设置事件
        /// </summary>
        public static event System.Action<CharacterParameters> OnDataSet;
        
        /// <summary>
        /// 数据获取事件
        /// </summary>
        public static event System.Action<CharacterParameters> OnDataRequested;
        
        /// <summary>
        /// 数据清除事件
        /// </summary>
        public static event System.Action OnDataCleared;
        
        /// <summary>
        /// 触发数据设置事件
        /// </summary>
        private void TriggerDataSetEvent()
        {
            OnDataSet?.Invoke(currentParameters);
        }
        
        /// <summary>
        /// 触发数据获取事件
        /// </summary>
        private void TriggerDataRequestedEvent()
        {
            OnDataRequested?.Invoke(currentParameters);
        }
        
        /// <summary>
        /// 触发数据清除事件
        /// </summary>
        private void TriggerDataClearedEvent()
        {
            OnDataCleared?.Invoke();
        }
        #endregion
        
        #region 辅助方法
        /// <summary>
        /// 更新调试信息
        /// </summary>
        private void UpdateDebugInfo()
        {
            if (currentParameters != null)
            {
                lastSaveTime = currentParameters.saveTime.ToString("yyyy-MM-dd HH:mm:ss");
                faceParameterCount = currentParameters.faceData.GetTotalParameterCount();
                clothingCount = currentParameters.clothingData.GetClothingCount();
            }
            else
            {
                lastSaveTime = "";
                faceParameterCount = 0;
                clothingCount = 0;
            }
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[CharacterDataBridge] {message}");
            }
        }
        #endregion
        
        #region Unity生命周期
        private void Start()
        {
            LogDebug("🚀 CharacterDataBridge 启动完成");
        }
        
        private void OnDestroy()
        {
            if (_instance == this)
            {
                _instance = null;
                LogDebug("🗑️ CharacterDataBridge 实例已销毁");
            }
        }
        #endregion
        
        #region 调试功能
        /// <summary>
        /// 在Inspector中显示当前数据状态
        /// </summary>
        [ContextMenu("显示数据摘要")]
        private void ShowDataSummary()
        {
            Debug.Log($"[CharacterDataBridge] 数据摘要: {GetDataSummary()}");
        }
        
        /// <summary>
        /// 手动清除数据（调试用）
        /// </summary>
        [ContextMenu("清除数据")]
        private void DebugClearData()
        {
            ClearData();
        }
        #endregion
    }
}
