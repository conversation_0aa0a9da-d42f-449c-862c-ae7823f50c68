using UnityEngine;
using UniVRM10;
using System.Collections;
using System.Collections.Generic;
using VRoidFaceCustomization.Core;

namespace VRoidFaceCustomization.Core
{
    /// <summary>
    /// VRM数据应用器 - 将保存的参数应用到VRM模型
    /// </summary>
    public class VRMDataApplier : MonoBehaviour
    {
        [Header("应用设置")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private float applyDelay = 0.1f; // 应用延迟，确保VRM完全初始化
        
        [Header("当前状态")]
        [SerializeField] private GameObject currentVRMObject;
        [SerializeField] private Vrm10Instance currentVrmInstance;
        [SerializeField] private bool isApplying = false;
        
        /// <summary>
        /// 将角色参数应用到指定的VRM对象
        /// </summary>
        public void ApplyToVRM(GameObject vrmObject, CharacterParameters parameters, System.Action<bool> onComplete = null)
        {
            if (vrmObject == null)
            {
                LogDebug("❌ VRM对象为空，无法应用数据");
                onComplete?.Invoke(false);
                return;
            }
            
            if (parameters == null || !parameters.IsValid())
            {
                LogDebug("❌ 参数数据无效，无法应用");
                onComplete?.Invoke(false);
                return;
            }
            
            LogDebug($"🎯 开始应用参数到VRM对象: {vrmObject.name}");
            LogDebug($"   📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
            LogDebug($"   👔 服装数量: {parameters.clothingData.GetClothingCount()}件");
            
            StartCoroutine(ApplyParametersCoroutine(vrmObject, parameters, onComplete));
        }
        
        /// <summary>
        /// 应用参数的协程
        /// </summary>
        private IEnumerator ApplyParametersCoroutine(GameObject vrmObject, CharacterParameters parameters, System.Action<bool> onComplete)
        {
            isApplying = true;
            currentVRMObject = vrmObject;
            
            // 获取VRM实例
            currentVrmInstance = vrmObject.GetComponent<Vrm10Instance>();
            if (currentVrmInstance == null)
            {
                LogDebug("❌ 未找到Vrm10Instance组件");
                isApplying = false;
                onComplete?.Invoke(false);
                yield break;
            }
            
            // 等待VRM初始化完成
            yield return StartCoroutine(WaitForVRMInitialization());
            
            // 应用模型变换
            ApplyModelTransform(vrmObject, parameters);
            
            // 等待一帧确保变换应用
            yield return null;
            
            // 应用面部参数
            bool faceSuccess = ApplyFaceParameters(vrmObject, parameters.faceData);
            
            // 等待一帧
            yield return null;
            
            // 应用服装参数
            bool clothingSuccess = ApplyClothingParameters(vrmObject, parameters.clothingData);
            
            // 等待最终确认
            yield return new WaitForSeconds(0.1f);
            
            bool success = faceSuccess && clothingSuccess;
            
            LogDebug(success ? "✅ 参数应用完成" : "❌ 参数应用失败");
            
            isApplying = false;
            onComplete?.Invoke(success);
        }
        
        /// <summary>
        /// 等待VRM初始化完成
        /// </summary>
        private IEnumerator WaitForVRMInitialization()
        {
            LogDebug("⏳ 等待VRM初始化完成...");
            
            int maxWaitFrames = 30;
            int frameCount = 0;
            
            while (frameCount < maxWaitFrames)
            {
                // 检查VRM是否完全初始化
                if (currentVrmInstance.Runtime != null && currentVrmInstance.Humanoid != null)
                {
                    // 尝试获取一个基本骨骼来验证初始化完成
                    if (currentVrmInstance.TryGetBoneTransform(HumanBodyBones.Hips, out var hips) && hips != null)
                    {
                        LogDebug("✅ VRM初始化完成");
                        break;
                    }
                }
                
                yield return null;
                frameCount++;
            }
            
            // 额外等待几帧确保完全初始化
            for (int i = 0; i < 3; i++)
            {
                yield return null;
            }
            
            LogDebug($"🔄 VRM初始化等待完成 (等待了{frameCount}帧)");
        }
        
        /// <summary>
        /// 应用模型变换
        /// </summary>
        private void ApplyModelTransform(GameObject vrmObject, CharacterParameters parameters)
        {
            LogDebug("📍 应用模型变换...");
            
            vrmObject.transform.position = parameters.modelPosition;
            vrmObject.transform.eulerAngles = parameters.modelRotation;
            vrmObject.transform.localScale = parameters.modelScale;
            
            LogDebug($"   位置: {parameters.modelPosition}");
            LogDebug($"   旋转: {parameters.modelRotation}");
            LogDebug($"   缩放: {parameters.modelScale}");
        }
        
        /// <summary>
        /// 应用面部参数
        /// </summary>
        private bool ApplyFaceParameters(GameObject vrmObject, FaceParameters faceData)
        {
            LogDebug("🎭 应用面部参数...");
            
            try
            {
                // 应用BlendShape权重
                ApplyBlendShapeWeights(vrmObject, faceData);
                
                // 应用面部材质属性
                ApplyFaceMaterialProperties(vrmObject, faceData);
                
                LogDebug($"✅ 面部参数应用完成: {faceData.GetTotalParameterCount()}个参数");
                return true;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 面部参数应用失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 应用BlendShape权重
        /// </summary>
        private void ApplyBlendShapeWeights(GameObject vrmObject, FaceParameters faceData)
        {
            var skinnedMeshRenderers = vrmObject.GetComponentsInChildren<SkinnedMeshRenderer>();
            int appliedCount = 0;
            
            foreach (var renderer in skinnedMeshRenderers)
            {
                if (renderer.sharedMesh == null) continue;
                
                var mesh = renderer.sharedMesh;
                for (int i = 0; i < mesh.blendShapeCount; i++)
                {
                    string shapeName = mesh.GetBlendShapeName(i);
                    string fullShapeName = $"{renderer.name}/{shapeName}";
                    
                    float weight = faceData.GetBlendShapeWeight(fullShapeName);
                    if (weight > 0.001f)
                    {
                        renderer.SetBlendShapeWeight(i, weight);
                        appliedCount++;
                    }
                }
            }
            
            LogDebug($"   🎭 BlendShape: 应用了 {appliedCount} 个权重");
        }
        
        /// <summary>
        /// 应用面部材质属性
        /// </summary>
        private void ApplyFaceMaterialProperties(GameObject vrmObject, FaceParameters faceData)
        {
            var renderers = vrmObject.GetComponentsInChildren<Renderer>();
            int appliedCount = 0;
            
            foreach (var renderer in renderers)
            {
                // 只处理面部相关的渲染器
                if (!IsFaceRenderer(renderer)) continue;
                
                for (int i = 0; i < renderer.materials.Length; i++)
                {
                    var material = renderer.materials[i];
                    if (material == null) continue;
                    
                    string materialPath = $"{renderer.name}/Material_{i}";
                    
                    // 应用材质属性
                    appliedCount += ApplyMaterialProperties(material, materialPath, faceData);
                }
            }
            
            LogDebug($"   🎨 材质属性: 应用了 {appliedCount} 个属性");
        }
        
        /// <summary>
        /// 应用材质属性
        /// </summary>
        private int ApplyMaterialProperties(Material material, string materialPath, FaceParameters faceData)
        {
            int appliedCount = 0;
            
            foreach (var kvp in faceData.faceMaterialProperties)
            {
                if (!kvp.Key.StartsWith(materialPath)) continue;
                
                var property = kvp.Value;
                if (!material.HasProperty(property.propertyName)) continue;
                
                try
                {
                    switch (property.propertyType)
                    {
                        case MaterialPropertyType.Color:
                            material.SetColor(property.propertyName, property.colorValue);
                            break;
                        case MaterialPropertyType.Float:
                            material.SetFloat(property.propertyName, property.floatValue);
                            break;
                        case MaterialPropertyType.Vector:
                            material.SetVector(property.propertyName, property.vectorValue);
                            break;
                    }
                    appliedCount++;
                }
                catch (System.Exception e)
                {
                    LogDebug($"⚠️ 材质属性应用失败: {property.propertyName}, 错误: {e.Message}");
                }
            }
            
            return appliedCount;
        }
        
        /// <summary>
        /// 应用服装参数
        /// </summary>
        private bool ApplyClothingParameters(GameObject vrmObject, ClothingParameters clothingData)
        {
            LogDebug("👔 应用服装参数...");
            
            try
            {
                // 查找VRM10ClothBinder组件
                var clothBinder = vrmObject.GetComponent<VRM10ClothBinder>();
                if (clothBinder == null)
                {
                    LogDebug("⚠️ 未找到VRM10ClothBinder组件，跳过服装应用");
                    return true; // 不算失败，只是没有服装系统
                }
                
                // 应用服装配置
                int appliedCount = 0;
                foreach (var kvp in clothingData.currentOutfit)
                {
                    string slotType = kvp.Key;
                    string clothingName = kvp.Value;
                    
                    if (clothingData.clothingConfigs.TryGetValue(clothingName, out var config))
                    {
                        // 这里需要根据你的服装系统实现来应用服装
                        // 暂时只记录日志
                        LogDebug($"   👔 应用服装: {slotType} -> {clothingName}");
                        appliedCount++;
                    }
                }
                
                LogDebug($"✅ 服装参数应用完成: {appliedCount}件服装");
                return true;
            }
            catch (System.Exception e)
            {
                LogDebug($"❌ 服装参数应用失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 判断是否为面部渲染器
        /// </summary>
        private bool IsFaceRenderer(Renderer renderer)
        {
            string name = renderer.name.ToLower();
            return name.Contains("face") || name.Contains("head") || name.Contains("eye") || name.Contains("mouth");
        }
        
        /// <summary>
        /// 检查是否正在应用参数
        /// </summary>
        public bool IsApplying()
        {
            return isApplying;
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[VRMDataApplier] {message}");
            }
        }
    }
}
