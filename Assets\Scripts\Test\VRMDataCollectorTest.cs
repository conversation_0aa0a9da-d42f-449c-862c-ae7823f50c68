using UnityEngine;
using VRoidFaceCustomization.Core;
using VRoidFaceCustomization;

namespace VRoidFaceCustomization.Test
{
    /// <summary>
    /// VRMDataCollector测试脚本
    /// </summary>
    public class VRMDataCollectorTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private GameObject testVRMObject;
        [SerializeField] private bool autoTest = false;
        
        private void Start()
        {
            if (autoTest && testVRMObject != null)
            {
                TestVRMDataCollector();
            }
        }
        
        [ContextMenu("测试VRM数据收集")]
        public void TestVRMDataCollector()
        {
            if (testVRMObject == null)
            {
                Debug.LogError("[VRMDataCollectorTest] 请先设置测试VRM对象！");
                return;
            }
            
            Debug.Log("[VRMDataCollectorTest] 🧪 开始测试VRM数据收集...");
            
            try
            {
                // 创建VRMDataCollector实例（现在应该可以正常工作）
                var collector = new VRMDataCollector();
                Debug.Log("[VRMDataCollectorTest] ✅ VRMDataCollector创建成功（已修复MonoBehaviour问题）");
                
                // 测试数据收集
                var parameters = collector.CollectFromVRM(testVRMObject);
                
                if (parameters != null && parameters.IsValid())
                {
                    Debug.Log("[VRMDataCollectorTest] ✅ 数据收集成功！");
                    Debug.Log($"[VRMDataCollectorTest]    📊 面部参数: {parameters.faceData.GetTotalParameterCount()}个");
                    Debug.Log($"[VRMDataCollectorTest]    👔 服装数量: {parameters.clothingData.GetClothingCount()}件");
                }
                else
                {
                    Debug.LogWarning("[VRMDataCollectorTest] ⚠️ 数据收集失败或数据无效");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[VRMDataCollectorTest] ❌ 测试失败: {ex.Message}");
                Debug.LogError($"[VRMDataCollectorTest] 堆栈跟踪: {ex.StackTrace}");
            }
        }
        
        [ContextMenu("检查VRM对象组件")]
        public void CheckVRMComponents()
        {
            if (testVRMObject == null)
            {
                Debug.LogError("[VRMDataCollectorTest] 请先设置测试VRM对象！");
                return;
            }

            Debug.Log($"[VRMDataCollectorTest] 🔍 检查VRM对象组件: {testVRMObject.name}");

            // 检查根对象组件
            var rootComponents = testVRMObject.GetComponents<Component>();
            Debug.Log($"[VRMDataCollectorTest] 根对象组件数量: {rootComponents.Length}");
            foreach (var comp in rootComponents)
            {
                Debug.Log($"[VRMDataCollectorTest]    - {comp.GetType().Name}");
            }

            // 检查所有子对象组件
            var allComponents = testVRMObject.GetComponentsInChildren<Component>();
            Debug.Log($"[VRMDataCollectorTest] 所有子对象组件数量: {allComponents.Length}");

            // 检查VRM10ClothBinder（使用完整命名空间）
            var clothBinder = testVRMObject.GetComponent<VRoidFaceCustomization.VRM10ClothBinder>();
            if (clothBinder != null)
            {
                Debug.Log("[VRMDataCollectorTest] ✅ 在根对象找到VRM10ClothBinder");
            }
            else
            {
                clothBinder = testVRMObject.GetComponentInChildren<VRoidFaceCustomization.VRM10ClothBinder>();
                if (clothBinder != null)
                {
                    Debug.Log($"[VRMDataCollectorTest] ✅ 在子对象找到VRM10ClothBinder: {clothBinder.gameObject.name}");
                }
                else
                {
                    Debug.LogWarning("[VRMDataCollectorTest] ⚠️ 未找到VRM10ClothBinder组件");

                    // 搜索所有可能的ClothBinder相关组件
                    Debug.Log("[VRMDataCollectorTest] 🔍 搜索所有包含'Cloth'的组件:");
                    foreach (var comp in allComponents)
                    {
                        if (comp.GetType().Name.Contains("Cloth"))
                        {
                            Debug.Log($"[VRMDataCollectorTest]    - 找到: {comp.GetType().FullName} 在 {comp.gameObject.name}");
                        }
                    }
                }
            }
        }
    }
}
